import 'dart:developer';
import 'package:domain/domain.dart';
import 'package:e_trader/fusion.dart' show EquitiTraderRouteSchema;
import 'package:equiti_platform/di/di_container.dart';
import 'package:equiti_router/equiti_router.dart';
import 'package:hub/hub.dart';
import 'package:login/login.dart';
import 'package:onboarding/onboarding.dart';
import 'package:payment/payments.dart';

class OnboardingNavigationImpl extends OnboardingNavigation {
  OnboardingNavigationImpl();

  @override
  void goToLogin() {
    diContainer<EquitiNavigatorBase>().push(LoginRouteSchema.loginroute.label);
  }

  @override
  void goToSignupOptions({required SignupOptionsArgs args}) {
    diContainer<EquitiNavigatorBase>().pushReplacement(
      OnboardingRouteSchema.signupOptionsRoute.label,
      arguments: args,
    );
  }

  @override
  void goToSignup({String? email}) {
    diContainer<EquitiNavigatorBase>().push(
      OnboardingRouteSchema.signup.label,
      data: {"email": email},
    );
  }

  @override
  void goToLoginOptions() {
    diContainer<EquitiNavigatorBase>().pushReplacement(
      OnboardingRouteSchema.loginOptionsRoute.label,
    );
  }

  @override
  void goToPersonalDetailsIntroPage() {
    diContainer<EquitiNavigatorBase>().push(
      OnboardingRouteSchema.personalDetailsIntroRoute.label,
    );
  }

  @override
  void navigateToVerifyMobile({required MobileOtpVerificationArgs args}) {
    log('args for navigating to verify mobile: $args');
    diContainer<EquitiNavigatorBase>().push(
      OnboardingRouteSchema.verifyMobileRoute.label,
      data: args.toJson(),
    );
  }

  @override
  void navigateToMobileNumberInput() {
    diContainer<EquitiNavigatorBase>().pushReplacement(
      OnboardingRouteSchema.mobileNumberInputRoute.label,
    );
  }

  @override
  void navigateToOtpInput({
    required MobileOtpVerificationArgs args,
    required SendOtpResponseData sendOtpModel,
  }) {
    log('args for navigating to otp input: $args $sendOtpModel');
    diContainer<EquitiNavigatorBase>().push(
      OnboardingRouteSchema.otpInputRoute.label,
      data: {"args": args, "sendOtpModel": sendOtpModel},
    );
  }

  @override
  void removeUntilMobileNumberInput() {
    diContainer<EquitiNavigatorBase>().pop();
    diContainer<EquitiNavigatorBase>().pop();
  }

  @override
  void navigateToPhoneNumberVerified() {
    diContainer<EquitiNavigatorBase>().push(
      OnboardingRouteSchema.phoneNumberVerifiedRoute.label,
    );
  }

  @override
  void navigateToCountrySelector() {
    diContainer<EquitiNavigatorBase>().push(
      OnboardingRouteSchema.countrySelectorRoute.label,
    );
  }

  @override
  void navigateToProgressTracker() {
    diContainer<EquitiNavigatorBase>().push(
      OnboardingRouteSchema.progressTrackerPage.label,
    );
  }

  @override
  void navigateToVerifyIdentity({bool replaceRoute = false}) {
    if (replaceRoute) {
      diContainer<EquitiNavigatorBase>().pushReplacement(
        OnboardingRouteSchema.verifyIdentityRoute.label,
      );
    } else {
      diContainer<EquitiNavigatorBase>().push(
        OnboardingRouteSchema.verifyIdentityRoute.label,
      );
    }
  }

  @override
  void navigateToMorphFormBuilder() {
    diContainer<EquitiNavigatorBase>().push(
      OnboardingRouteSchema.morphFormBuilder.label,
    );
  }

  @override
  void navigateToCreateAccountMain({
    required CreateAccountFlow createAccountFlow,
  }) {
    diContainer<EquitiNavigatorBase>().push(
      OnboardingRouteSchema.createAccountMainRoute.label,
      arguments: CreateAccountMainArgs(createAccountFlow: createAccountFlow),
    );
  }

  @override
  void navigateToAccountSuccessful({
    required AccountCreationRequestModel data,
    required CreateAccountFlow createAccountFlow,
    bool replace = false,
  }) {
    if (replace) {
      diContainer<EquitiNavigatorBase>().pushReplacement(
        OnboardingRouteSchema.accountSuccessfulRoute.label,
        arguments: AccountSuccessfulArgs(
          data: data,
          createAccountFlow: createAccountFlow,
        ),
      );
    } else {
      diContainer<EquitiNavigatorBase>().push(
        OnboardingRouteSchema.accountSuccessfulRoute.label,
        arguments: AccountSuccessfulArgs(
          data: data,
          createAccountFlow: createAccountFlow,
        ),
      );
    }
  }

  @override
  void navigateToCreateAccountIntro() {
    diContainer<EquitiNavigatorBase>().push(
      OnboardingRouteSchema.createAccountIntroRoute.label,
    );
  }

  @override
  void navigateToDepositIntro() {
    diContainer<EquitiNavigatorBase>().pushAndRemoveUntil(
      OnboardingRouteSchema.depositIntroRoute.label,
      OnboardingRouteSchema.depositIntroRoute.label,
    );
  }

  @override
  void navigateToDepositPaymentOptions() {
    diContainer<EquitiNavigatorBase>().pushReplacement(
      PaymentRouteSchema.depositOptionsRoute.label,
    );
  }

  @override
  void navigateToHub({bool replace = false}) {
    if (replace) {
      diContainer<EquitiNavigatorBase>().pushReplacement(
        HubRouteSchema.hubRoute.label,
      );
    } else {
      diContainer<EquitiNavigatorBase>().push(HubRouteSchema.hubRoute.label);
    }
  }

  @override
  void goToCitySelection() {
    diContainer<EquitiNavigatorBase>().push(
      OnboardingRouteSchema.citySelectorRoute.label,
    );
  }

  @override
  void navigateToUserRegistration() {
    diContainer<EquitiNavigatorBase>().pushAndRemoveUntil(
      OnboardingRouteSchema.userRegistrationRoute.label,
      '',
    );
  }

  @override
  void navigateToSwitchAccounts({bool replace = false}) {
    if (replace) {
      diContainer<EquitiNavigatorBase>().pushReplacement(
        EquitiTraderRouteSchema.switchAccountRoute.label,
      );
    } else {
      diContainer<EquitiNavigatorBase>().push(
        EquitiTraderRouteSchema.switchAccountRoute.label,
      );
    }
  }

  @override
  void navigateBackToAccountsScreen() {
    diContainer<EquitiNavigatorBase>().popUntil(
      EquitiTraderRouteSchema.switchAccountRoute.label,
    );
  }
}
