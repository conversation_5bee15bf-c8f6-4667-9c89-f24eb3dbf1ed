import 'package:domain/domain.dart';
import 'package:e_trader/fusion.dart';
import 'package:equiti_platform/di/di_container.dart';
import 'package:equiti_router/equiti_router.dart';
import 'package:flutter/material.dart';
import 'package:onboarding/onboarding.dart';
import 'package:payment/payments.dart';
import 'package:payment/src/data/account_model.dart';
import 'package:payment/src/data/withdraw_card_model/withdraw_card_model.dart';
import 'package:payment/src/navigation/arguments/payment_method_args.dart';
import 'package:payment/src/navigation/arguments/equiti_pay_cards_args.dart';
import 'package:payment/src/navigation/arguments/transfer_type_page_arguments.dart';
import 'package:payment/src/navigation/arguments/withdraw_bank_transfer_arguments.dart';
import 'package:payment/src/navigation/arguments/withdraw_select_account_and_amount_args.dart';

class PaymentNavigationImpl extends PaymentNavigation {
  @override
  void goToDepositSelectAccountAndAmountScreen(
    DepositPaymentMethod method, {
    num? maxPollingAttempts,
    num? pollingFrequencySeconds,
  }) {
    diContainer<EquitiNavigatorBase>().push(
      PaymentRouteSchema.depositSelectAccountAndAmountRoute.label,
      arguments: DepositPaymentMethodArgs(
        method: method,
        maxPollingAttempts: maxPollingAttempts,
        pollingFrequencySeconds: pollingFrequencySeconds,
      ),
    );
  }

  @override
  void goToWithdrawSelectAccountAndAmountScreen({
    bool replace = false,
    required WithdrawalPaymentMethod method,
    String? account,
    WithdrawCard? selectedCard,
  }) {
    final args = WithdrawSelectAccountAndAmountArgs(
      method: method,
      account: account,
      selectedCard: selectedCard,
    );

    if (replace) {
      diContainer<EquitiNavigatorBase>().pushReplacement(
        PaymentRouteSchema.withdrawSelectAccountAndAmountRoute.label,
        arguments: args,
      );
    } else {
      diContainer<EquitiNavigatorBase>().push(
        PaymentRouteSchema.withdrawSelectAccountAndAmountRoute.label,
        arguments: args,
      );
    }
  }

  @override
  void goToEquitiPayCardsScreen(
    String url,
    String title,
    String transactionId,
    String accountNumber, {
    num? maxPollingAttempts,
    num? pollingFrequencySeconds,
  }) {
    diContainer<EquitiNavigatorBase>().push(
      PaymentRouteSchema.equitiPayCardsRoute.label,
      arguments: EquitiPayCardsArgs(
        url: url,
        title: title,
        transactionId: transactionId,
        accountNumber: accountNumber,
        maxPollingAttempts: maxPollingAttempts,
        pollingFrequencySeconds: pollingFrequencySeconds,
      ),
    );
  }

  @override
  void goToPaymentStatusScreen({
    required String transactionId,
    required String accountNumber,
    required VoidCallback onContinue,
    required VoidCallback onMakeAnotherDeposit,
    required VoidCallback onTryAgain,
    required VoidCallback onChangePaymentMethod,
    PaymentStatus? paymentStatus,
    num? maxPollingAttempts,
    num? pollingFrequencySeconds,
  }) {
    diContainer<EquitiNavigatorBase>().push(
      PaymentRouteSchema.paymentStatusRoute.label,
      data: {
        "transactionId": transactionId,
        "accountNumber": accountNumber,
        "onContinue": onContinue,
        "onMakeAnotherDeposit": onMakeAnotherDeposit,
        "onTryAgain": onTryAgain,
        "onChangePaymentMethod": onChangePaymentMethod,
        "paymentStatus": paymentStatus,
        "maxPollingAttempts": maxPollingAttempts,
        "pollingFrequencySeconds": pollingFrequencySeconds,
      },
    );
  }

  @override
  void goToFormBuilder() {
    diContainer<EquitiNavigatorBase>().push(
      OnboardingRouteSchema.morphFormBuilder.label,
    );
  }

  @override
  void goToPaymentOptionsScreen() {
    diContainer<EquitiNavigatorBase>().push(
      PaymentRouteSchema.depositOptionsRoute.label,
    );
  }

  @override
  void goToPaymentNotAvailableYetPage() {
    diContainer<EquitiNavigatorBase>().push(
      PaymentRouteSchema.paymentPageNotAvailableRoute.label,
    );
  }

  @override
  void goToDepositSelectBankScreen(DepositPaymentMethodGroup methodGroup) {
    diContainer<EquitiNavigatorBase>().push(
      PaymentRouteSchema.depositSelectBankRoute.label,
      arguments: DepositSelectBankArgs(paymentMethodGroup: methodGroup),
    );
  }

  @override
  void goToDespositBankDetailsScreen(Bank bank) {
    diContainer<EquitiNavigatorBase>().push(
      PaymentRouteSchema.depositBankDetailsRoute.label,
      data: {"bank": bank},
    );
  }

  @override
  void goToWithdrawCardPage(WithdrawalPaymentMethod method) {
    diContainer<EquitiNavigatorBase>().push(
      PaymentRouteSchema.withdrawCardRoute.label,
      arguments: WithdrawalPaymentMethodArgs(method: method),
    );
  }

  @override
  void goToWithdrawAddNewBankPage(WithdrawCardParams withdrawCardParams) {
    diContainer<EquitiNavigatorBase>().push(
      PaymentRouteSchema.withdrawAddNewBankRoute.label,
      data: {"withdrawCardParams": withdrawCardParams},
    );
  }

  @override
  void goToAdditionalPaymentScreen(
    String url,
    String title,
    String transactionId,
    String accountNumber, {
    num? maxPollingAttempts,
    num? pollingFrequencySeconds,
  }) {
    diContainer<EquitiNavigatorBase>().push(
      PaymentRouteSchema.additionalPaymentMethodsRoute.label,
      arguments: AdditionalPaymentMethodsArgs(
        url: url,
        title: title,
        transactionId: transactionId,
        accountNumber: accountNumber,
        maxPollingAttempts: maxPollingAttempts,
        pollingFrequencySeconds: pollingFrequencySeconds,
      ),
    );
  }

  @override
  void goBackToDepositPaymentOptionsScreen() {
    diContainer<EquitiNavigatorBase>().popUntil(
      PaymentRouteSchema.depositOptionsRoute.label,
    );
  }

  @override
  void goBackToDepositSelectAccountAndAmountScreen() {
    diContainer<EquitiNavigatorBase>().popUntil(
      PaymentRouteSchema.depositSelectAccountAndAmountRoute.label,
    );
  }

  @override
  void popUntilRoute(String routeLabel, {bool inclusive = false}) {
    diContainer<EquitiNavigatorBase>().popUntil(
      routeLabel,
      inclusive: inclusive,
    );
  }

  void goToWithdrawSkrillAndNetellerScreen(WithdrawalPaymentMethod method) {
    diContainer<EquitiNavigatorBase>().push(
      PaymentRouteSchema.withdrawSkrillAndNetellerRoute.label,
      arguments: WithdrawalPaymentMethodArgs(method: method),
    );
  }

  @override
  void goToTransferFundsDestSelectionScreen(Account account) {
    diContainer<EquitiNavigatorBase>().push(
      PaymentRouteSchema.transferFundsDestSelectionRoute.label,
      data: {"account": account},
    );
  }

  @override
  void navigateToCreateAccountMain({
    required CreateAccountFlow createAccountFlow,
  }) {
    diContainer<EquitiNavigatorBase>().push(
      OnboardingRouteSchema.createAccountMainRoute.label,
      arguments: {"createAccountFlow": createAccountFlow},
    );
  }

  @override
  void goToTransferFundsScreen() {
    diContainer<EquitiNavigatorBase>().push(
      PaymentRouteSchema.transferFundsScreen.label,
    );
  }

  @override
  void goToTransferTypeScreen(TransferTypePageArguments arguments) {
    diContainer<EquitiNavigatorBase>().push(
      arguments: arguments,
      PaymentRouteSchema.transferTypeRoute.label,
    );
  }

  @override
  void goToWithdrawBankTransferScreen(
    WithdrawBankTransferArguments withdrawBankTransferArguments,
  ) {
    diContainer<EquitiNavigatorBase>().push(
      PaymentRouteSchema.withdrawBankTransferRoute.label,
      arguments: withdrawBankTransferArguments,
    );
  }

  void goToWithdrawNewBankUploadDocScreen({
    required String operationId,
    required String tradingAccountId,
    bool replace = false,
  }) {
    if (replace) {
      diContainer<EquitiNavigatorBase>().pushReplacement(
        PaymentRouteSchema.withdrawNewBankUploadDocRoute.label,
        data: {
          "operationId": operationId,
          "tradingAccountId": tradingAccountId,
        },
      );
    } else {
      diContainer<EquitiNavigatorBase>().push(
        PaymentRouteSchema.withdrawNewBankUploadDocRoute.label,
        data: {
          "operationId": operationId,
          "tradingAccountId": tradingAccountId,
        },
      );
    }
  }

  @override
  void goToWithdrawStatusScreen({
    bool replace = false,
    VoidCallback? onContinue,
    WithdrawStatusType? status,
    String? popUntilRoute,
  }) {
    if (replace) {
      diContainer<EquitiNavigatorBase>().pushReplacement(
        PaymentRouteSchema.withdrawStatusRoute.label,
        arguments: WithdrawStatusArgs(
          statusType: status,
          onButtonPressed: onContinue,
          popUntilRoute: popUntilRoute,
        ),
      );
    } else {
      diContainer<EquitiNavigatorBase>().push(
        PaymentRouteSchema.withdrawStatusRoute.label,
        arguments: WithdrawStatusArgs(
          statusType: status,
          onButtonPressed: onContinue,
          popUntilRoute: popUntilRoute,
        ),
      );
    }
  }

  @override
  void goBackToSwitchAccounts() {
    diContainer<EquitiNavigatorBase>().popUntil(
      EquitiTraderRouteSchema.navBarRoute.label,
    );
    diContainer<EquitiNavigatorBase>().pushReplacement(
      EquitiTraderRouteSchema.switchAccountRoute.label,
    );
  }
}
