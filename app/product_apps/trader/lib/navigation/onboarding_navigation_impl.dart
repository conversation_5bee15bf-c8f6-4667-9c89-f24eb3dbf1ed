import 'package:domain/domain.dart';
import 'package:flutter/material.dart';
import 'package:onboarding/onboarding.dart';

class OnboardingNavigationImpl extends OnboardingNavigation {
  OnboardingNavigationImpl({required this.navigatorKey});
  final GlobalKey<NavigatorState> navigatorKey;

  @override
  void goToLogin() {
    // TODO: implement goToLogin
  }

  @override
  void goToLoginOptions() {
    // TODO: implement goToLoginOptions
  }

  @override
  void goToSignup({String? email}) {
    // TODO: implement goToSignup
  }

  @override
  void goToSignupOptions({required SignupOptionsArgs args}) {
    navigatorKey.currentState!.push<void>(
      MaterialPageRoute<void>(
        builder:
            (context) => SignupOptionsScreen(
              country: args.country,
              countryCode: args.countryCode,
              brokerId: args.brokerId,
              city: args.city,
            ),
      ),
    );
  }

  @override
  void goToPersonalDetailsIntroPage() {
    navigatorKey.currentState!.pushReplacement(
      MaterialPageRoute<void>(builder: (context) => PersonalDetailsIntroPage()),
    );
  }

  @override
  void navigateToVerifyMobile({required MobileOtpVerificationArgs args}) {
    navigatorKey.currentState!.push<Object?>(
      MaterialPageRoute<void>(
        builder: (context) => VerifyMobileIntroPage(args: args),
      ),
    );
  }

  @override
  void navigateToMobileNumberInput() {
    navigatorKey.currentState!.push<void>(
      MaterialPageRoute<void>(
        builder: (context) => const MobileNumberInputScreen(),
      ),
    );
  }

  @override
  void navigateToOtpInput({
    required MobileOtpVerificationArgs args,
    required SendOtpResponseData sendOtpModel,
  }) {
    navigatorKey.currentState!.push<Object?>(
      MaterialPageRoute<void>(
        builder:
            (context) => OtpInputView(args: args, sendOtpModel: sendOtpModel),
      ),
    );
  }

  @override
  void removeUntilMobileNumberInput() {
    navigatorKey.currentState!.pop();
    navigatorKey.currentState!.pop();
  }

  @override
  void navigateToPhoneNumberVerified() {
    navigatorKey.currentState!.push<void>(
      MaterialPageRoute<void>(
        builder: (context) => const PhoneNumberVerified(),
      ),
    );
  }

  @override
  void navigateToCountrySelector() {
    navigatorKey.currentState!.push<void>(
      MaterialPageRoute<void>(builder: (context) => const CountrySelection()),
    );
  }

  @override
  void navigateToProgressTracker() {
    navigatorKey.currentState!.push<void>(
      MaterialPageRoute<void>(
        builder: (context) => const ProgressTrackerScreen(),
      ),
    );
  }

  @override
  void navigateToVerifyIdentity({bool replaceRoute = false}) {
    if (replaceRoute) {
      navigatorKey.currentState!.pushReplacement(
        MaterialPageRoute<void>(builder: (context) => VerifyIdentityScreen()),
      );
    } else {
      navigatorKey.currentState!.push<void>(
        MaterialPageRoute<void>(builder: (context) => VerifyIdentityScreen()),
      );
    }
  }

  @override
  void navigateToMorphFormBuilder() {
    navigatorKey.currentState!.push<void>(
      MaterialPageRoute<void>(builder: (context) => const MorphFormBuilder()),
    );
  }

  @override
  void navigateToCreateAccountMain({
    required CreateAccountFlow createAccountFlow,
  }) {
    navigatorKey.currentState!.push<void>(
      MaterialPageRoute<void>(
        builder:
            (context) =>
                CreateAccountMainScreen(createAccountFlow: createAccountFlow),
      ),
    );
  }

  @override
  void navigateToAccountSuccessful({
    required AccountCreationRequestModel data,
    required CreateAccountFlow createAccountFlow,
    bool replace = false,
  }) {
    if (replace) {
      navigatorKey.currentState!.pushReplacement(
        MaterialPageRoute<void>(
          builder:
              (context) => AccountSuccessfulScreen(
                data: data,
                createAccountFlow: createAccountFlow,
              ),
        ),
      );
    } else {
      navigatorKey.currentState!.push(
        MaterialPageRoute<void>(
          builder:
              (context) => AccountSuccessfulScreen(
                data: data,
                createAccountFlow: createAccountFlow,
              ),
        ),
      );
    }
  }

  @override
  void navigateToCreateAccountIntro() {
    // TODO: implement navigateToCreateAccountIntro
  }

  @override
  void navigateToDepositIntro() {
    // TODO: implement navigateToDepositIntro
  }

  @override
  void navigateToDepositPaymentOptions() {
    // TODO: implement navigateToDepositPaymentOptions
  }

  @override
  void navigateToHub({bool replace = false}) {
    // TODO: implement navigateToHub
  }

  @override
  void goToCitySelection() {
    // TODO: implement goToCitySelection
  }

  @override
  void navigateToUserRegistration() {
    // TODO: implement navigateToUserRegistration
  }

  @override
  void navigateToSwitchAccounts({bool replace = false}) {
    // TODO: implement navigateToSwitchAccounts
  }

  @override
  void navigateBackToAccountsScreen() {
    // TODO: implement navigateBackToAccountsScreen
  }
}
