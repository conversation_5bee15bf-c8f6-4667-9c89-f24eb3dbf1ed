import 'package:api_client/api_client.dart';
import 'package:host/host.dart';
import 'package:onboarding/onboarding.dart';
import 'package:trader/di/di_initializer.dart';

DisplayableComponent accountCreation() {
  diContainer<MockApiInterceptor>()
    ..reset()
    ..reply({
      '/api/v1/broker-settings': [
        MockResponse(
          bodyFilePath: 'resources/mocks/broker_settings/success.json',
        ),
      ],
    });
  return DisplayableComponent(
    title: 'Account Creation',
    onTap: () {
      return CreateAccountBannerScreen();
    },
  );
}
