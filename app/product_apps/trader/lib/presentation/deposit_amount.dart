import 'package:api_client/api_client.dart';
import 'package:flutter/material.dart';
import 'package:host/host.dart';
import 'package:payment/payments.dart';
import 'package:payment/src/domain/data/conversion_type.dart';
import 'package:payment/src/domain/data/payment_type.dart';
import 'package:trader/di/di_initializer.dart';

DisplayableComponent depositAmount() {
  return DisplayableComponent(
    title: 'Deposit Amount',
    onTap: () {
      diContainer<MockApiInterceptor>()
        ..reset()
        ..reply({
          '/api/v1/conversionRate': [
            MockResponse(
              bodyFilePath: 'resources/mocks/conversion_rate/success.json',
            ),
          ],
        });
      return Scaffold(
        appBar: AppBar(title: const Text('Deposit Amount')),
        body: AmountConversionWidget(
          args: (
            transferCurrency: 'USD',
            transferCurrencyImage: '',
            currencyAmountDetails: [
              CurrencyAmountDetail(
                currency: 'AED',
                suggestedAmounts: [500, 1000, 1500],
                minAmount: 1,
                maxAmount: 1000,
              ),
              CurrencyAmountDetail(
                currency: 'USD',
                suggestedAmounts: [50, 100, 150],
                minAmount: 1,
                maxAmount: 2000,
              ),
            ],
            currencies: ["Usd", "AED", "KWD"],
            showSuggestedAmounts: true,
            isStartWithConversionRate: false,
            targetCurrency: null,
            externalErrorMessage: null,
            paymentType: PaymentType.deposit,
            conversionType: ConversionType.targetToAccountCurrency,
          ),
          onAmountChange: ({
            required String amount,
            required bool isAmountValid,
            required String convertedAmount,
            required ConversionRateModel? conversionRateData,
            required RatesModel? ratesModel,
            String? conversionRateString,
            String? targetCurrency,
          }) {
            print('Amount: $amount, Is Valid: $isAmountValid');
            print('Conversion Rate String: ${conversionRateString ?? 'N/A'}');
            print('Target Currency: ${targetCurrency ?? 'N/A'}');
          },
        ),
      );
    },
  );
}
