import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';
import 'package:host/host.dart';
import 'package:trader/assets/assets.gen.dart' as trader;
import 'package:trader/presentation/duplo_demo/bottom_sheet_with_list.dart';
import 'package:trader/presentation/duplo_demo/color_tokens_demo_screen.dart';
import 'package:trader/presentation/duplo_demo/drop_down_selector.dart';
import 'package:trader/presentation/duplo_demo/duplo_alert_message_demo.dart';
import 'package:trader/presentation/duplo_demo/duplo_bottom_sheet.dart';
import 'package:trader/presentation/duplo_demo/duplo_buttons_demo.dart';
import 'package:trader/presentation/duplo_demo/duplo_check_box_demo.dart';
import 'package:trader/presentation/duplo_demo/duplo_date_of_birth_input_field_demo.dart';
import 'package:trader/presentation/duplo_demo/duplo_icon_button_demo.dart';
import 'package:trader/presentation/duplo_demo/duplo_search_input_field.dart';
import 'package:trader/presentation/duplo_demo/duplo_shimmers.dart';
import 'package:trader/presentation/duplo_demo/duplo_verification_code_input_demo.dart';
import 'package:trader/presentation/duplo_demo/duplo_wrapped_selection_list_demo.dart';
import 'package:trader/presentation/duplo_demo/flags_demo_screen.dart';
import 'package:trader/presentation/duplo_demo/lottie_view_demo.dart';
import 'package:trader/presentation/duplo_demo/typography_tokens_demo_screen.dart';
import 'package:trader/presentation/reusable_ui.dart';

DisplayableComponent duploDemo() {
  return DisplayableComponent(
    title: 'Duplo Demo',
    children: [
      DisplayableComponent(
        title: 'Flags',
        onTap: () => const FlagsDemoScreen(),
      ),
      DisplayableComponent(
        title: 'Colors',
        children: [
          DisplayableComponent(
            title: 'Color Tokens',
            onTap: () => const ColorTokensDemoScreen(),
          ),
        ],
      ),
      DisplayableComponent(
        title: 'Typography Tokens',
        onTap: () => const TypographyTokensDemoScreen(),
      ),
      DisplayableComponent(
        title: 'Custom Tab bar',
        children: [
          DisplayableComponent(
            title: 'Non-scrollable TabBar',
            onTap: () {
              return Scaffold(
                appBar: AppBar(
                  automaticallyImplyLeading: true,
                  title: Text('Non-scrollable Tab Bar'),
                ),
                body: Padding(
                  padding: EdgeInsets.all(16),
                  child: DuploTabBar(
                    tabTitles: [
                      DuploTabBarTitle(
                        text: "News",
                        semanticsIdentifier: 'News',
                      ),
                      DuploTabBarTitle(
                        text: "Event",
                        semanticsIdentifier: 'Event',
                      ),
                    ],
                    isScrollable: false,
                    tabViews: [Text('tab 1'), Text('tab 2')],
                    isFlex: false,
                  ),
                ),
              );
            },
          ),
          DisplayableComponent(
            title: 'Scrollable TabBar',
            onTap: () {
              var list = [
                DuploTabBarTitle(text: "Tab 1", semanticsIdentifier: 'Tab 1'),
                DuploTabBarTitle(text: "Tab 2", semanticsIdentifier: 'Tab 2'),
                DuploTabBarTitle(text: "Tab 3", semanticsIdentifier: 'Tab 3'),
                DuploTabBarTitle(text: "Tab 4", semanticsIdentifier: 'Tab 4'),
                DuploTabBarTitle(text: "Tab 5", semanticsIdentifier: 'Tab 5'),
                DuploTabBarTitle(text: "Tab 6", semanticsIdentifier: 'Tab 6'),
                DuploTabBarTitle(text: "Tab 7", semanticsIdentifier: 'Tab 7'),
                DuploTabBarTitle(text: "Tab 8", semanticsIdentifier: 'Tab 8'),
              ];
              return Scaffold(
                appBar: AppBar(
                  automaticallyImplyLeading: true,
                  title: Text('Scrollable Tab Bar'),
                ),
                body: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: DuploTabBar(
                    isFlex: false,
                    isScrollable: true,
                    tabTitles: list,
                    enableSplashEffect: true,
                    tabViews: List.generate(list.length, (index) {
                      return Text(list[index].text);
                    }),
                  ),
                ),
              );
            },
          ),
        ],
      ),
      DisplayableComponent(
        title: 'Modal Bottom Sheet',
        children: [
          DisplayableComponent(
            title: "Full Screen Bottom Sheet",
            onTap: () {
              return DuploBottomSheet(isFullScreen: true);
            },
          ),
          DisplayableComponent(
            title: "Default Bottom Sheet",
            onTap: () {
              return DuploBottomSheet(isFullScreen: false);
            },
          ),
          DisplayableComponent(
            title: "Bottom Sheet With List View Full Screen",
            onTap: () {
              return BottomSheetWithList(isFullScreen: true);
            },
          ),
          DisplayableComponent(
            title: "Bottom Sheet With List View Small",
            onTap: () {
              return BottomSheetWithList(isFullScreen: false);
            },
          ),
        ],
      ),
      DisplayableComponent(
        title: 'Duplo Bottom Nav Bar',
        children: [
          DisplayableComponent(
            title: "Bottom Nav Bar with 5 items",
            onTap: () {
              return DuploBottomNavbar(
                navItems: [
                  DubloBottomNavBarItems(
                    title: "Discover",
                    selectedIcon: trader.Assets.images.discoverIc.svg(
                      colorFilter: ColorFilter.mode(
                        Color(0xffFFFFFF),
                        BlendMode.srcIn,
                      ),
                    ),
                    unselectedIcon: trader.Assets.images.discoverIc.svg(
                      colorFilter: ColorFilter.mode(
                        Color(0xff98A2B3),
                        BlendMode.srcIn,
                      ),
                    ),
                  ),
                  DubloBottomNavBarItems(
                    selectedIcon: trader.Assets.images.marketsIc.svg(
                      colorFilter: ColorFilter.mode(
                        Color(0xffFFFFFF),
                        BlendMode.srcIn,
                      ),
                    ),
                    unselectedIcon: trader.Assets.images.marketsIc.svg(
                      colorFilter: ColorFilter.mode(
                        Color(0xff98A2B3),
                        BlendMode.srcIn,
                      ),
                    ),
                    title: "Markets",
                  ),
                  DubloBottomNavBarItems(
                    selectedIcon: trader.Assets.images.portfolioIc.svg(
                      colorFilter: ColorFilter.mode(
                        Color(0xffFFFFFF),
                        BlendMode.srcIn,
                      ),
                    ),
                    unselectedIcon: trader.Assets.images.portfolioIc.svg(
                      colorFilter: ColorFilter.mode(
                        Color(0xff98A2B3),
                        BlendMode.srcIn,
                      ),
                    ),
                    title: "Portfolio",
                  ),
                  DubloBottomNavBarItems(
                    selectedIcon: trader.Assets.images.performanceIc.svg(
                      colorFilter: ColorFilter.mode(
                        Color(0xffFFFFFF),
                        BlendMode.srcIn,
                      ),
                    ),
                    unselectedIcon: trader.Assets.images.performanceIc.svg(
                      colorFilter: ColorFilter.mode(
                        Color(0xff98A2B3),
                        BlendMode.srcIn,
                      ),
                    ),
                    title: "Performance",
                  ),
                  DubloBottomNavBarItems(
                    selectedIcon: trader.Assets.images.moreIc.svg(
                      colorFilter: ColorFilter.mode(
                        Color(0xffFFFFFF),
                        BlendMode.srcIn,
                      ),
                    ),
                    unselectedIcon: trader.Assets.images.moreIc.svg(
                      colorFilter: ColorFilter.mode(
                        Color(0xff98A2B3),
                        BlendMode.srcIn,
                      ),
                    ),
                    title: "More",
                  ),
                ],
              );
            },
          ),
          DisplayableComponent(
            title: "Bottom Nav Bar with 4 items",
            onTap: () {
              return DuploBottomNavbar(
                navItems: [
                  DubloBottomNavBarItems(
                    selectedIcon: trader.Assets.images.marketsIc.svg(
                      colorFilter: ColorFilter.mode(
                        Color(0xffFFFFFF),
                        BlendMode.srcIn,
                      ),
                    ),
                    unselectedIcon: trader.Assets.images.marketsIc.svg(
                      colorFilter: ColorFilter.mode(
                        Color(0xff98A2B3),
                        BlendMode.srcIn,
                      ),
                    ),
                    title: "Markets",
                  ),
                  DubloBottomNavBarItems(
                    selectedIcon: trader.Assets.images.portfolioIc.svg(
                      colorFilter: ColorFilter.mode(
                        Color(0xffFFFFFF),
                        BlendMode.srcIn,
                      ),
                    ),
                    unselectedIcon: trader.Assets.images.portfolioIc.svg(
                      colorFilter: ColorFilter.mode(
                        Color(0xff98A2B3),
                        BlendMode.srcIn,
                      ),
                    ),
                    title: "Portfolio",
                  ),
                  DubloBottomNavBarItems(
                    selectedIcon: trader.Assets.images.performanceIc.svg(
                      colorFilter: ColorFilter.mode(
                        Color(0xffFFFFFF),
                        BlendMode.srcIn,
                      ),
                    ),
                    unselectedIcon: trader.Assets.images.performanceIc.svg(
                      colorFilter: ColorFilter.mode(
                        Color(0xff98A2B3),
                        BlendMode.srcIn,
                      ),
                    ),
                    title: "Performance",
                  ),
                  DubloBottomNavBarItems(
                    selectedIcon: trader.Assets.images.moreIc.svg(
                      colorFilter: ColorFilter.mode(
                        Color(0xffFFFFFF),
                        BlendMode.srcIn,
                      ),
                    ),
                    unselectedIcon: trader.Assets.images.moreIc.svg(
                      colorFilter: ColorFilter.mode(
                        Color(0xff98A2B3),
                        BlendMode.srcIn,
                      ),
                    ),
                    title: "More",
                  ),
                ],
              );
            },
          ),
          DisplayableComponent(
            title: "Bottom Nav Bar with 3 items",
            onTap: () {
              return DuploBottomNavbar(
                navItems: [
                  DubloBottomNavBarItems(
                    title: "Discover",
                    selectedIcon: trader.Assets.images.discoverIc.svg(
                      colorFilter: ColorFilter.mode(
                        Color(0xffFFFFFF),
                        BlendMode.srcIn,
                      ),
                    ),
                    unselectedIcon: trader.Assets.images.discoverIc.svg(
                      colorFilter: ColorFilter.mode(
                        Color(0xff98A2B3),
                        BlendMode.srcIn,
                      ),
                    ),
                  ),
                  DubloBottomNavBarItems(
                    selectedIcon: trader.Assets.images.marketsIc.svg(
                      colorFilter: ColorFilter.mode(
                        Color(0xffFFFFFF),
                        BlendMode.srcIn,
                      ),
                    ),
                    unselectedIcon: trader.Assets.images.marketsIc.svg(
                      colorFilter: ColorFilter.mode(
                        Color(0xff98A2B3),
                        BlendMode.srcIn,
                      ),
                    ),
                    title: "Markets",
                  ),
                  DubloBottomNavBarItems(
                    selectedIcon: trader.Assets.images.portfolioIc.svg(
                      colorFilter: ColorFilter.mode(
                        Color(0xffFFFFFF),
                        BlendMode.srcIn,
                      ),
                    ),
                    unselectedIcon: trader.Assets.images.portfolioIc.svg(
                      colorFilter: ColorFilter.mode(
                        Color(0xff98A2B3),
                        BlendMode.srcIn,
                      ),
                    ),
                    title: "Portfolio",
                  ),
                ],
              );
            },
          ),
        ],
      ),
      duploButtonsDemo(),
      duploIconButtonsDemo(),
      reusableUI(),
      duploShimmers(),
      dropDownSelector("en_US"),
      duploSearchInputField(),
      duploCheckBox(),
      dropDownSelector("en_US"),
      duploVerificationCodeInputDemo(),
      duploDateOfBirthInputField(),
      duploWrappedSelectionList(),
      duploLottieView(),
      duploAlertMessageDemo(),
    ],
  );
}
