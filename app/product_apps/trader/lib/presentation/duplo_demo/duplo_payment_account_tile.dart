import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';
import 'package:host/host.dart';

DisplayableComponent duploPaymentAccountTileDemo() {
  return DisplayableComponent(
    title: 'Duplo Payment Account Tile',
    onTap:
        () => Scaffold(
          appBar: AppBar(title: const Text('Duplo Payment Account Tile')),
          body: Padding(
            padding: const EdgeInsets.all(16),
            child: <PERSON>um<PERSON>(
              children: [
                DuploPaymentAccountTile.legacy(
                  accountNickName: '[CFD Account Nickname]',
                  balance: 5.658,
                  currency: 'USD',
                  accountNumber: "********",
                  accountPlatform: 'MT5',
                  accountType: 'Standard',
                  currencyImage: FlagProvider.getFlagFromCurrencyCode(
                    'USD',
                    width: 16,
                    height: 16,
                  ),
                  isSelected: true,
                  isWallet: false,
                ),
                SizedBox(height: 16),
                DuploPaymentAccountTile.legacy(
                  accountNickName: '[CFD Account Nickname]',
                  balance: 5.658,
                  currency: 'USD',
                  accountNumber: "********",
                  accountPlatform: 'MT5',
                  accountType: 'Standard',
                  currencyImage: FlagProvider.getFlagFromCurrencyCode(
                    'USD',
                    width: 16,
                    height: 16,
                  ),
                  isSelected: false,
                  isWallet: false,
                ),
              ],
            ),
          ),
        ),
  );
}
