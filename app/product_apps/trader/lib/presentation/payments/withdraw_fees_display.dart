import 'package:api_client/api_client.dart';
import 'package:flutter/material.dart';
import 'package:host/host.dart';
import 'package:payment/payments.dart';
import 'package:prelude/prelude.dart';
import 'package:trader/di/di_initializer.dart';
import 'package:trader/presentation/payments/dynamic_withdraw_fees_demo.dart';

DisplayableComponent withdrawFeesDisplay() {
  return DisplayableComponent(
    title: 'Withdraw Fees Display',
    children: [
      DisplayableComponent(
        title: 'Zero Fees',
        onTap: () {
          _returnZeroFees();
          return _withdrawFeesDisplay();
        },
      ),
      DisplayableComponent(
        title: 'Non-Zero Fees',
        onTap: () {
          _returnNonZeroFees();
          return _withdrawFeesDisplay();
        },
      ),
      DisplayableComponent(
        title: 'Failure for Client Profile',
        onTap: () {
          _returnFailureForClinetProfile();
          return _withdrawFeesDisplay();
        },
      ),
      DisplayableComponent(
        title: 'Failure for Withdrawal Fees',
        onTap: () {
          _returnFailureForWithdrawalFees();
          return _withdrawFeesDisplay();
        },
      ),
      DisplayableComponent(
        title: 'Dynamic Price',
        onTap: () {
          _returnNonZeroFees();
          return _withdrawFeesDisplayWithDynamicPrice();
        },
      ),
    ],
  );
}

Widget _withdrawFeesDisplay() {
  return Scaffold(
    appBar: AppBar(title: const Text('Withdraw Fees Display')),
    body: Padding(
      padding: const EdgeInsets.all(16.0),
      child: Builder(
        builder: (context) {
          return WithdrawFeesDisplay(
            args: (
              amount: 100,
              accountId: '123',
              accountCurrency: 'USD',
              transactionCurrency: 'USD',
              paymentType: WithdrawalMop.bank.name,
              convertedAmount: 100,
              transferType: null,
            ),
            content: (
              idle:
                  'Choose a transfer type to calculate withdrawal fees (if applicable)',
              loading: 'Calculating withdrawal fees..',
              zeroFees: 'No fees will be deducted for this withdrawal.',
              nonZeroFees: (fees, total, currency) {
                final formattedFees = EquitiFormatter.formatNumber(
                  value: fees,
                  locale: Localizations.localeOf(context).toString(),
                );
                return 'Withdrawal will incur a ${formattedFees} $currency transfer fee.';
              },
            ),
            onFeesChange: (state) {
              print(state);
            },
          );
        },
      ),
    ),
  );
}

Widget _withdrawFeesDisplayWithDynamicPrice() {
  return DynamicWithdrawFeesDemo();
}

void _returnZeroFees() {
  diContainer<MockApiInterceptor>()
    ..reset()
    ..reply({
      '/api/v1/client-profiles/trader_user_registration_id?brokerId=trader_broker_id':
          [
            MockResponse(
              bodyFilePath: 'resources/mocks/client_profile/success.json',
            ),
          ],
      '/api/v1/withdrawal/fees': [
        MockResponse(
          bodyFilePath: 'resources/mocks/withdraw_fees/success_zero_fees.json',
        ),
      ],
    });
}

void _returnNonZeroFees() {
  diContainer<MockApiInterceptor>()
    ..reset()
    ..reply({
      '/api/v1/client-profiles/trader_user_registration_id?brokerId=trader_broker_id':
          [
            MockResponse(
              bodyFilePath: 'resources/mocks/client_profile/success.json',
            ),
          ],
      '/api/v1/withdrawal/fees': [
        MockResponse(
          bodyFilePath:
              'resources/mocks/withdraw_fees/success_non_zero_fees.json',
        ),
      ],
    });
}

void _returnFailureForClinetProfile() {
  diContainer<MockApiInterceptor>()
    ..reset()
    ..reply({
      '/api/v1/client-profiles/trader_user_registration_id?brokerId=trader_broker_id':
          [
            MockResponse(
              bodyFilePath: 'resources/mocks/client_profile/failure.json',
            ),
          ],
      '/api/v1/withdrawal/fees': [
        MockResponse(
          bodyFilePath: 'resources/mocks/withdraw_fees/failure.json',
        ),
      ],
    });
}

void _returnFailureForWithdrawalFees() {
  diContainer<MockApiInterceptor>()
    ..reset()
    ..reply({
      '/api/v1/client-profiles/trader_user_registration_id?brokerId=trader_broker_id':
          [
            MockResponse(
              bodyFilePath: 'resources/mocks/client_profile/success.json',
            ),
          ],
      '/api/v1/withdrawal/fees': [
        MockResponse(
          bodyFilePath: 'resources/mocks/withdraw_fees/failure.json',
        ),
      ],
    });
}
