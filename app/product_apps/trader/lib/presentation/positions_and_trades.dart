// ignore_for_file: inference_failure_on_instance_creation, no-empty-block

import 'package:e_trader/fusion.dart';
import 'package:flutter/material.dart';
import 'package:host/host.dart';
import 'package:intl/intl.dart';

DisplayableComponent positionAndTrades() {
  return DisplayableComponent(
    title: 'Positions and Trades',
    children: [
      DisplayableComponent(
        title: 'position header',
        onTap: () {
          return Scaffold(
            appBar: AppBar(
              automaticallyImplyLeading: true,
              title: Text("postions header"),
            ),
            body: PositionHeader(
              lots: 0.4,
              productIcon:
                  'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQiauMVXJTGHbVWoUu6w62vuYIAxYTSGA1sBg&s',
              productName: 'EURUSD',
              isHedging: false,
              profit: 15000,
              margin: 12000,
              tradeType: TradeType.buy,
            ),
          );
        },
      ),
      DisplayableComponent(
        title: 'expandable position header',
        onTap: () {
          return Scaffold(
            appBar: AppBar(
              automaticallyImplyLeading: true,
              title: Text("expandable postions header"),
            ),
            body: ExpandablePositionHeader(
              lots: 0.4,
              productIcon:
                  'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQiauMVXJTGHbVWoUu6w62vuYIAxYTSGA1sBg&s',
              productName: 'EURUSD',
              isHedging: true,
              profit: 15000,
              margin: 12000,
              tradeType: TradeType.buy,
              tradesData: [
                PositionModel(
                  volume: 40,
                  multiply: 10000,
                  profit: 15000,
                  percentageChange: 12000,
                  currentPrice: 1.23456,
                  openPrice: 1.23456,
                  takeProfit: 1.23456,
                  stopLoss: 1.23456,
                  positionType: TradeType.buy,
                  productCategoryId: '',
                  positionId: '1',
                  symbol: '',
                  marginRate: 0.0,
                  tradingAccountNumber: '',
                  profitRate: 0.0,
                  contractSize: 1.0,
                  margin: 0.0,
                  marginAllocation: 0.0,
                  productCategory: '',
                  commission: 0.0,
                  leverage: 1,
                  digits: 5,
                  productName: 'EURUSD',
                  productLogoUrl: '',
                  notionalValue: 0.0,
                  pipValue: 0.0,
                  assetType: '',
                  sector: '',
                  baseCurrency: '',
                  grossProfit: 15000,
                  minLot: 0.01,
                  maxLot: 100.0,
                  marginLevel: 0.0,
                  messageType: 1,
                  openedAt: DateFormat().format(DateTime.now()),
                  updatedAt: DateFormat().format(DateTime.now()),
                  priceDirection: PriceDirection.up,
                  swap: 0.0,
                ),
                PositionModel(
                  volume: 40,
                  multiply: 10000,
                  profit: -134,
                  percentageChange: 12000,
                  currentPrice: 1.23456,
                  openPrice: 1.23456,
                  takeProfit: 1.23456,
                  stopLoss: 1.23456,
                  positionType: TradeType.sell,
                  productCategoryId: '',
                  positionId: '2',
                  symbol: '',
                  marginRate: 0.0,
                  tradingAccountNumber: '',
                  profitRate: 0.0,
                  contractSize: 1.0,
                  margin: 0.0,
                  marginAllocation: 0.0,
                  productCategory: '',
                  commission: 0.0,
                  leverage: 1,
                  digits: 5,
                  productName: 'EURUSD',
                  productLogoUrl: '',
                  notionalValue: 0.0,
                  pipValue: 0.0,
                  assetType: '',
                  sector: '',
                  baseCurrency: '',
                  grossProfit: -134,
                  minLot: 0.01,
                  maxLot: 100.0,
                  marginLevel: 0.0,
                  messageType: 1,
                  openedAt: DateFormat().format(DateTime.now()),
                  updatedAt: DateFormat().format(DateTime.now()),
                  priceDirection: PriceDirection.down,
                  swap: 0.0,
                ),
                PositionModel(
                  volume: 40,
                  multiply: 10000,
                  profit: 15000,
                  percentageChange: 12000,
                  currentPrice: 1.23456,
                  openPrice: 1.23456,
                  takeProfit: 1.23456,
                  stopLoss: 1.23456,
                  positionType: TradeType.buy,
                  productCategoryId: '',
                  positionId: '3',
                  symbol: '',
                  marginRate: 0.0,
                  tradingAccountNumber: '',
                  profitRate: 0.0,
                  contractSize: 1.0,
                  margin: 0.0,
                  marginAllocation: 0.0,
                  productCategory: '',
                  commission: 0.0,
                  leverage: 1,
                  digits: 5,
                  productName: 'EURUSD',
                  productLogoUrl: '',
                  notionalValue: 0.0,
                  pipValue: 0.0,
                  assetType: '',
                  sector: '',
                  baseCurrency: '',
                  grossProfit: 15000,
                  minLot: 0.01,
                  maxLot: 100.0,
                  marginLevel: 0.0,
                  messageType: 1,
                  openedAt: DateFormat().format(DateTime.now()),
                  updatedAt: DateFormat().format(DateTime.now()),
                  priceDirection: PriceDirection.up,
                  swap: 0.0,
                ),
                PositionModel(
                  volume: 40,
                  multiply: 10000,
                  profit: -134,
                  percentageChange: 12000,
                  currentPrice: 1.23456,
                  openPrice: 1.23456,
                  takeProfit: 1.23456,
                  stopLoss: 1.23456,
                  positionType: TradeType.sell,
                  productCategoryId: '',
                  positionId: '4',
                  symbol: '',
                  marginRate: 0.0,
                  tradingAccountNumber: '',
                  profitRate: 0.0,
                  contractSize: 1.0,
                  margin: 0.0,
                  marginAllocation: 0.0,
                  productCategory: '',
                  commission: 0.0,
                  leverage: 1,
                  digits: 5,
                  productName: 'EURUSD',
                  productLogoUrl: '',
                  notionalValue: 0.0,
                  pipValue: 0.0,
                  assetType: '',
                  sector: '',
                  baseCurrency: '',
                  grossProfit: -134,
                  minLot: 0.01,
                  maxLot: 100.0,
                  marginLevel: 0.0,
                  messageType: 1,
                  openedAt: DateFormat().format(DateTime.now()),
                  updatedAt: DateFormat().format(DateTime.now()),
                  priceDirection: PriceDirection.down,
                  swap: 0.0,
                ),
              ],
            ),
          );
        },
      ),
      DisplayableComponent(
        title: 'Trade Tile',
        onTap: () {
          return Scaffold(
            appBar: AppBar(
              automaticallyImplyLeading: true,
              title: Text("Trade Tile"),
            ),
            body: TradeTile(
              digits: 5,
              lots: 0.4,
              profit: 15000,
              tradeType: TradeType.buy,
              currentPrice: 1.5432,
              priceChange: 12000,
              tpValue: 0.1,
              slValue: 0.2,
              productIcon:
                  'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQiauMVXJTGHbVWoUu6w62vuYIAxYTSGA1sBg&s',
              productName: 'EURUSD',
            ),
          );
        },
      ),
      DisplayableComponent(
        title: 'Order Tile',
        onTap: () {
          return Scaffold(
            appBar: AppBar(
              automaticallyImplyLeading: true,
              title: Text("Order Tile"),
            ),
            body: OrderListTile(
              tpValue: 0.2,
              slValue: 0.1,
              digits: 5,
              orderPrice: 099.987,
              lots: 0.04,
              currentPrice: 101.064,
              tradeType: TradeType.buy,
              productIconURL:
                  'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQiauMVXJTGHbVWoUu6w62vuYIAxYTSGA1sBg&s',
              productName: 'EURUSD',
            ),
          );
        },
      ),
    ],
  );
}
