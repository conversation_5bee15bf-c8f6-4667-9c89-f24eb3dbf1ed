import 'package:duplo/src/assets/assets.gen.dart';
import 'package:duplo/src/components/drop_down_selector/drop_down_image_view.dart';
import 'package:duplo/src/data/drop_down_item_model/drop_down_item_model.dart';
import 'package:duplo/src/theming/duplo_theme.dart';
import 'package:duplo/src/typography/duplo_font_weight.dart';
import 'package:duplo/src/typography/duplo_text.dart';
import 'package:duplo/src/typography/duplo_text_styles.dart';
import 'package:flutter/material.dart';

/// Internal widget used by [SelectorBottomSheet] to display individual selectable items
/// in the dropdown list with proper styling and layout.
///
/// Each item shows an image (typically a flag), name text, and a radio button to indicate selection.
/// The item is tappable and triggers the onChanged callback when selected.
class DropDownListItem extends StatelessWidget {
  /// Creates a DropDownListItem widget.
  ///
  /// All parameters are required.
  const DropDownListItem({
    super.key,
    required this.selectedItem,
    required this.isSelected,
    required this.onChanged,
  });

  final DropDownItemModel selectedItem;

  /// Whether this item is currently selected
  final bool isSelected;

  /// Callback triggered when this item is selected
  /// The boolean parameter indicates the new selection state
  final void Function(bool? value) onChanged;

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final isLTR = Directionality.of(context) == TextDirection.ltr;
    return GestureDetector(
      onTap: () => onChanged(true),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: theme.background.bgPrimary,
          border: Border.all(
            width: isSelected ? 2 : 1,
            color:
                isSelected
                    ? theme.border.borderBrand
                    : theme.border.borderTertiary,
          ),
          borderRadius: BorderRadius.circular(6),
        ),
        child: Row(
          children: [
            Expanded(
              child: Row(
                children: [
                  if (selectedItem.image != null)
                    DropDownImageView(
                      selectedItemImage: selectedItem.image,
                      height: 32,
                      width: 32,
                    ),
                  if (selectedItem.image != null)
                    SizedBox(width: 8)
                  else
                    SizedBox(height: 32),
                  Expanded(
                    child: DuploText(
                      text: selectedItem.title,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      style: DuploTextStyles.of(context).textMd,
                      fontWeight: DuploFontWeight.semiBold,
                      color: theme.text.textPrimary,
                      textAlign: isLTR ? TextAlign.left : TextAlign.right,
                    ),
                  ),
                ],
              ),
            ),
            GestureDetector(
              onTap: () => onChanged(true),
              child: Container(
                height: 20,
                width: 20,
                decoration: BoxDecoration(
                  border: Border.all(color: theme.border.borderPrimary),
                  borderRadius: BorderRadius.circular(999),
                ),
                child: isSelected ? Assets.images.radioBase.svg() : null,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
