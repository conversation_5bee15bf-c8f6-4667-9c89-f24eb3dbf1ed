import 'package:duplo/src/assets/assets.gen.dart';
import 'package:duplo/src/components/duplo_alert/alert_type.dart';
import 'package:duplo/src/components/duplo_tap.dart';
import 'package:duplo/src/theming/duplo_theme.dart';
import 'package:duplo/src/typography/duplo_font_weight.dart';
import 'package:duplo/src/typography/duplo_text.dart';
import 'package:duplo/src/typography/duplo_text_styles.dart';
import 'package:flutter/material.dart';

class DuploAlertMessage extends StatefulWidget {
  const DuploAlertMessage._({
    super.key,
    required this.title,
    this.titleFontWeight = DuploFontWeight.semiBold,
    this.subtitle,
    required this.leading,
    required this.alertType,
    this.expandable = false,
    this.trailing,
    this.children = const [],
    this.primaryAction,
    this.secondaryAction,
    this.onTapPrimaryAction,
    this.onTapSecondaryAction,
  }) : assert(
         trailing == null || !expandable,
         'If trailing is provided, expandable must be false',
       );

  factory DuploAlertMessage.info({
    Key? key,
    required String title,
    DuploFontWeight titleFontWeight = DuploFontWeight.semiBold,
    String? subtitle,
    Widget? leading,
    Widget? trailing,
    bool expandable = false,
    List<Widget> children = const [],
    String? primaryAction,
    String? secondaryAction,
    VoidCallback? onTapPrimaryAction,
    VoidCallback? onTapSecondaryAction,
  }) {
    return DuploAlertMessage._(
      key: key,
      title: title,
      titleFontWeight: titleFontWeight,
      subtitle: subtitle,
      leading: leading ?? Assets.images.duploAlertInfo.svg(),
      trailing: trailing,
      expandable: expandable,
      children: children,
      alertType: AlertType.info,
      primaryAction: primaryAction,
      secondaryAction: secondaryAction,
      onTapPrimaryAction: onTapPrimaryAction,
      onTapSecondaryAction: onTapSecondaryAction,
    );
  }

  factory DuploAlertMessage.warning({
    Key? key,
    required String title,
    DuploFontWeight titleFontWeight = DuploFontWeight.semiBold,
    String? subtitle,
    Widget? leading,
    Widget? trailing,
    bool expandable = false,
    List<Widget> children = const [],
    String? primaryAction,
    String? secondaryAction,
    VoidCallback? onTapPrimaryAction,
    VoidCallback? onTapSecondaryAction,
  }) {
    return DuploAlertMessage._(
      key: key,
      title: title,
      titleFontWeight: titleFontWeight,
      subtitle: subtitle,
      leading: leading ?? Assets.images.duploAlertWarning.svg(),
      trailing: trailing,
      expandable: expandable,
      children: children,
      alertType: AlertType.warning,
      primaryAction: primaryAction,
      secondaryAction: secondaryAction,
      onTapPrimaryAction: onTapPrimaryAction,
      onTapSecondaryAction: onTapSecondaryAction,
    );
  }

  factory DuploAlertMessage.error({
    Key? key,
    required String title,
    DuploFontWeight titleFontWeight = DuploFontWeight.semiBold,
    String? subtitle,
    Widget? leading,
    Widget? trailing,
    bool expandable = false,
    List<Widget> children = const [],
    String? primaryAction,
    String? secondaryAction,
    VoidCallback? onTapPrimaryAction,
    VoidCallback? onTapSecondaryAction,
  }) {
    return DuploAlertMessage._(
      key: key,
      title: title,
      titleFontWeight: titleFontWeight,
      subtitle: subtitle,
      leading: leading ?? Assets.images.duploAlertError.svg(),
      trailing: trailing,
      expandable: expandable,
      children: children,
      alertType: AlertType.error,
      primaryAction: primaryAction,
      secondaryAction: secondaryAction,
      onTapPrimaryAction: onTapPrimaryAction,
      onTapSecondaryAction: onTapSecondaryAction,
    );
  }

  final String title;
  final DuploFontWeight titleFontWeight;
  final String? subtitle;
  final bool expandable;
  final List<Widget> children;
  final Widget leading;
  final Widget? trailing;
  final AlertType alertType;
  final String? primaryAction;
  final String? secondaryAction;
  final VoidCallback? onTapPrimaryAction;
  final VoidCallback? onTapSecondaryAction;

  @override
  State<DuploAlertMessage> createState() => _DuploAlertMessageState();
}

class _DuploAlertMessageState extends State<DuploAlertMessage> {
  Color _getColor(AlertType alertType) {
    switch (alertType) {
      case AlertType.info:
        return DuploTheme.of(context).background.bgSecondary;
      case AlertType.warning:
        return DuploTheme.of(context).utility.utilityWarning50;
      case AlertType.error:
        return DuploTheme.of(context).utility.utilityError50;
    }
  }

  Color _getBorderColor(AlertType alertType) {
    switch (alertType) {
      case AlertType.info:
        return DuploTheme.of(context).border.borderPrimary;
      case AlertType.warning:
        return DuploTheme.of(context).utility.utilityWarning200;
      case AlertType.error:
        return DuploTheme.of(context).utility.utilityError200;
    }
  }

  @override
  Widget build(BuildContext context) {
    final textStyles = DuploTextStyles.of(context);
    final theme = DuploTheme.of(context);
    final isRtl = Directionality.of(context) == TextDirection.rtl;
    final showTrailing = widget.expandable || widget.trailing != null;
    return Theme(
      data: ThemeData(
        dividerColor: Colors.transparent,
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
      ),
      child: Stack(
        children: [
          DecoratedBox(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(6),
              color: _getColor(widget.alertType),
              border: Border.all(color: _getBorderColor(widget.alertType)),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                IgnorePointer(
                  ignoring: !widget.expandable,
                  child: ExpansionTile(
                    leading: SizedBox(),
                    title: DuploText(
                      text: widget.title,
                      style: textStyles.textSm,
                      fontWeight: widget.titleFontWeight,
                      color: theme.text.textSecondary,
                    ),
                    subtitle:
                        widget.subtitle == null
                            ? null
                            : Padding(
                              padding: const EdgeInsets.only(top: 4.0),
                              child: DuploText(
                                text: widget.subtitle,
                                style: textStyles.textSm,
                                color: theme.text.textTertiary,
                              ),
                            ),
                    showTrailingIcon: showTrailing,
                    trailing: widget.trailing,
                    children: widget.children,
                    expandedCrossAxisAlignment: CrossAxisAlignment.start,
                    dense: true,
                    visualDensity: VisualDensity.compact,
                    childrenPadding: EdgeInsets.fromLTRB(52, 0, 52, 16),
                  ),
                ),
                if (widget.primaryAction != null ||
                    widget.secondaryAction != null)
                  Padding(
                    padding: const EdgeInsets.only(
                      left: 52,
                      right: 52,
                      bottom: 12,
                    ),
                    child: Row(
                      children: [
                        if (widget.primaryAction != null)
                          DuploTap(
                            onTap: widget.onTapPrimaryAction,
                            child: DuploText(
                              text: widget.primaryAction,
                              style: textStyles.textMd,
                              fontWeight: DuploFontWeight.semiBold,
                              color: theme.button.buttonTertiaryFg,
                            ),
                          ),
                        SizedBox(width: 12),
                        if (widget.secondaryAction != null)
                          DuploTap(
                            onTap: widget.onTapSecondaryAction,
                            child: DuploText(
                              text: widget.secondaryAction,
                              style: textStyles.textMd,
                              fontWeight: DuploFontWeight.semiBold,
                              color: theme.button.buttonTertiaryFg,
                            ),
                          ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
          Positioned(
            left: isRtl ? null : 16,
            right: isRtl ? 16 : null,
            top: 12,
            child: widget.leading,
          ),
        ],
      ),
    );
  }
}
