import 'package:duplo/src/assets/assets.gen.dart';
import 'package:duplo/src/components/duplo_overlay.dart';
import 'package:duplo/src/components/duplo_tap.dart';
import 'package:duplo/src/theming/duplo_theme.dart';
import 'package:duplo/src/typography/duplo_font_weight.dart';
import 'package:duplo/src/typography/duplo_text.dart';
import 'package:duplo/src/typography/duplo_text_styles.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class DuploCopyTile extends StatelessWidget {
  const DuploCopyTile({
    super.key,
    this.backgroundColor,
    this.borderRadius = 6,
    this.borderWidth = 1,
    this.borderColor,
    required this.label,
    required this.content,
    this.contentPadding = const EdgeInsets.symmetric(
      horizontal: 12,
      vertical: 8,
    ),
    this.onCopy,
  });

  final Color? backgroundColor;
  final double borderRadius;
  final double borderWidth;
  final Color? borderColor;
  final String label;
  final String content;
  final EdgeInsetsGeometry contentPadding;
  final VoidCallback? onCopy;

  void onTapCopyTile(BuildContext ctx, String message) {
    Clipboard.setData(ClipboardData(text: content));
    onCopy?.call();
    //calculating position of copy button
    final box = ctx.findRenderObject() as RenderBox?;
    Offset? position = box?.localToGlobal(Offset.zero);
    DuploOverlay.show(ctx, message, position: position);
  }

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final textStyles = DuploTextStyles.of(context);
    final localization = EquitiLocalization.of(context);
    return Container(
      height: 70,
      decoration: BoxDecoration(
        color: backgroundColor ?? theme.background.bgSecondary,
        border: Border.all(
          color: borderColor ?? theme.border.borderPrimary,
          width: borderWidth,
        ),
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            flex: 5,
            child: Container(
              padding: contentPadding,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  DuploText(
                    text: label,
                    style: textStyles.textXs,
                    color: theme.text.textSecondary,
                  ),
                  const SizedBox(height: 6),
                  DuploText(
                    text: content,
                    style: textStyles.textMd,
                    color: theme.text.textPrimary,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ),
          VerticalDivider(thickness: 1, color: theme.border.borderSecondary),
          Expanded(
            flex: 2,
            child: Builder(
              builder: (ctx) {
                return DuploTap(
                  onTap: () {
                    onTapCopyTile(ctx, localization.duplo_copied);
                  },
                  child: Container(
                    padding: contentPadding,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Assets.images.copy.svg(
                          height: 20,
                          width: 20,
                          colorFilter: ColorFilter.mode(
                            theme.button.buttonSecondaryFg,
                            BlendMode.srcIn,
                          ),
                        ),
                        const SizedBox(width: 6),
                        DuploText(
                          text: localization.duplo_copy,
                          fontWeight: DuploFontWeight.semiBold,
                          style: textStyles.textMd,
                          color: theme.button.buttonSecondaryFg,
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
