import 'dart:developer';

import 'package:custom_action_keyboard/custom_action_keyboard.dart';
import 'package:duplo/src/assets/assets.gen.dart';
import 'package:duplo/src/components/duplo_date_of_birth/utils/date_input_formatter.dart';
import 'package:duplo/src/components/duplo_date_of_birth/utils/duplo_date_of_birth_utils.dart';
import 'package:duplo/src/components/duplo_date_of_birth/utils/duplo_year_view.dart';
import 'package:duplo/src/components/duplo_tap.dart';
import 'package:duplo/src/constants/duplo_spacing.dart';
import 'package:duplo/src/di/di_container.dart';
import 'package:duplo/src/theming/duplo_theme.dart';
import 'package:duplo/src/typography/duplo_text.dart';
import 'package:duplo/src/typography/duplo_typography_context_extension.dart';
import 'package:equiti_localization/l10n/gen/equiti_localization.gen.dart';
import 'package:flutter/material.dart';
import 'package:theme_manager/theme_manager.dart';

/// A customized date of birth input field component.
///
/// This widget provides a specialized text field for entering date of birth
/// with formatting, validation, and a calendar picker option.
///
/// Example usage:
/// ```dart
/// DuploDateOfBirthInputField(
///   controller: TextEditingController(),
///   showYears: true,
///   helperText: 'Enter your date of birth',
///   autoFocus: true,
/// )
/// ```
class DuploDateOfBirthInputField extends StatefulWidget {
  /// Creates a date of birth input field.
  ///
  /// The [showYears] parameter must not be null.
  ///
  /// If [controller] is not provided, a default one will be created internally.
  const DuploDateOfBirthInputField({
    super.key,
    this.controller,
    required this.showYears,
    this.onTapOutside,
    this.errorMessage,
    this.autoFocus = false,
    this.isEnabled = true,
    this.helperText = '',
    this.label,
    this.hint,
    this.onChanged,
    this.onSubmitted,
    this.focusNode,
  });

  /// Controller for the text input field.
  final TextEditingController? controller;

  /// Error message to display below the input field.
  final String? errorMessage;

  /// Helper text to display below the input field when there's no error.
  final String helperText;

  /// Whether the field should be auto-focused when displayed.
  final bool autoFocus;

  /// Whether the field is enabled for user interaction.
  final bool isEnabled;

  /// Whether to show the years view component.
  final bool showYears;

  /// Callback for when the user taps outside the input field.
  final void Function(PointerDownEvent)? onTapOutside;

  /// The label to display above the input field.
  final String? label;

  /// The hint text to display below the input field.
  final String? hint;

  /// The callback function to be called when the text in the input field changes.
  final void Function(String)? onChanged;

  /// The callback function to be called when the user submits the text in the input field.
  final void Function(String)? onSubmitted;

  /// Focus node to track the focus state of the input field.
  final FocusNode? focusNode;

  @override
  State<DuploDateOfBirthInputField> createState() =>
      _DuploDateOfBirthInputFieldState();
}

/// The state for the [DuploDateOfBirthInputField] widget.
class _DuploDateOfBirthInputFieldState
    extends State<DuploDateOfBirthInputField> {
  /// Controller for the text input field.
  late TextEditingController controller;

  /// Whether the input field is currently focused.
  bool isFocused = false;

  /// Whether the input field is in an error state.
  bool isError = false;

  /// Focus node to track the focus state of the input field.
  late FocusNode focusNode;

  @override
  void initState() {
    super.initState();
    controller = widget.controller ?? TextEditingController();
    focusNode = widget.focusNode ?? FocusNode();
    listenToFocusNode();
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      controller.dispose();
    }

    if (widget.focusNode == null) {
      widget.focusNode?.removeListener(() {
        log('Focus node removed');
      });
      widget.focusNode?.dispose();
    }
    removeKeyboardActionStrip();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final textTheme = context.duploTextStyles;
    isError = (widget.errorMessage ?? '').isNotEmpty;
    final isRTL = Directionality.of(context) == TextDirection.rtl;
    final utils = DuploDateOfBirthUtils();
    handleKeyboardActionStrip();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: EdgeInsets.symmetric(vertical: DuploSpacing.spacing_md_8),
          decoration: BoxDecoration(
            color:
                widget.isEnabled
                    ? theme.background.bgSecondarySubtle
                    : theme.background.bgDisabled,
            borderRadius: BorderRadius.circular(DuploSpacing.spacing_sm_6),
            border: Border.all(
              color: utils.getBorderColor(
                isFocused: isFocused,
                isError: isError,
                theme: theme,
              ),
            ),
            boxShadow: utils.getShadow(
              textValue: controller.text,
              isFocused: isFocused,
              isError: isError,
            ),
          ),
          child: TextField(
            key: Key('duplo_date_of_birth_input_field'),
            controller: controller,
            focusNode: widget.focusNode,
            enabled: widget.isEnabled,
            autofocus: widget.autoFocus,
            scrollPadding: EdgeInsets.only(bottom: 50),
            style: TextStyle(
              fontSize: textTheme.textMd.fontSize,
              height: textTheme.textSm.lineHeight,
              color:
                  !widget.isEnabled
                      ? theme.text.textPlaceholder
                      : theme.text.textPrimary,
              fontFamily: textTheme.textSm.fontFamily,
            ),
            keyboardType: TextInputType.number,
            keyboardAppearance:
                diContainer<ThemeManager>().isDarkMode
                    ? Brightness.dark
                    : Brightness.light,
            inputFormatters: [DateInputFormatter()],
            onChanged: (text) {
              widget.onChanged?.call(text);
            },
            onTapOutside:
                widget.onTapOutside ??
                (_) => FocusManager.instance.primaryFocus?.unfocus(),
            decoration: InputDecoration(
              filled: true,
              isDense: true,
              fillColor: Colors.transparent,
              contentPadding: EdgeInsets.symmetric(
                horizontal: DuploSpacing.spacing_lg_12,
                vertical: DuploSpacing.spacing_sm_6,
              ),
              border: InputBorder.none,
              focusedBorder: InputBorder.none,
              enabledBorder: InputBorder.none,
              errorBorder: InputBorder.none,
              label: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    widget.label ?? 'Date of Birth',
                    style: TextStyle(
                      fontSize: textTheme.textMd.fontSize,
                      color:
                          !widget.isEnabled
                              ? theme.text.textSecondary
                              : (isFocused || controller.text.isNotEmpty)
                              ? theme.text.textSecondary
                              : theme.text.textPlaceholderSubtle,
                      fontFamily: textTheme.textMd.fontFamily,
                    ),
                  ),
                  if (isError) Assets.images.errorIc.svg(height: 20, width: 20),
                  if (!isError &&
                      !isFocused &&
                      widget.isEnabled &&
                      controller.text.isEmpty)
                    DuploTap(
                      key: Key('duplo_date_of_birth_input_field_calendar'),
                      onTap: openDatePicker,
                      child: Assets.images.calendar.svg(
                        height: 20,
                        width: 20,
                        colorFilter: ColorFilter.mode(
                          theme.foreground.fgDisabled,
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                ],
              ),
              hintText: widget.hint ?? 'DD - MM - YYYY',
              hintStyle: TextStyle(
                fontSize: textTheme.textMd.fontSize,
                height: textTheme.textMd.lineHeight,
                color: theme.text.textPlaceholder,
                fontFamily: textTheme.textMd.fontFamily,
              ),
              suffix: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  const SizedBox(height: 6),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      DuploYearView(
                        dateOfBirthValue: controller.text,
                        isError: isError,
                        isEnabled: widget.isEnabled,
                      ),
                      DuploTap(
                        onTap: openDatePicker,
                        child: Assets.images.calendar.svg(
                          height: 20,
                          width: 20,
                          colorFilter: ColorFilter.mode(
                            theme.foreground.fgDisabled,
                            BlendMode.srcIn,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
        if (isError || widget.helperText.isNotEmpty)
          Padding(
            padding: EdgeInsets.only(
              left: isRTL ? 0 : DuploSpacing.spacing_md_8,
              right: isRTL ? DuploSpacing.spacing_md_8 : 0,
              top: DuploSpacing.spacing_sm_6,
            ),
            child: DuploText(
              text: isError ? widget.errorMessage : widget.helperText,
              style: textTheme.textXs,
              color:
                  isError
                      ? theme.text.textErrorPrimary
                      : theme.text.textTertiary,
            ),
          ),
      ],
    );
  }

  /// Opens a date picker dialog and updates the input field with the selected date.
  void openDatePicker() {
    final utils = DuploDateOfBirthUtils();
    utils.openDatePicker(context: context).then((value) {
      if (value != null) {
        setState(() {
          controller.text = value;
          widget.onChanged?.call(value);
        });
      }
    });
  }

  void listenToFocusNode() {
    widget.focusNode?.addListener(() {
      setState(() {
        isFocused = widget.focusNode?.hasFocus ?? false;
      });
      if (!isFocused) {
        removeKeyboardActionStrip();
      }
    });
  }

  void handleKeyboardActionStrip() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final theme = DuploTheme.of(context);
      final localization = EquitiLocalization.of(context);
      diContainer<CustomActionKeyboard>().initService(
        diContainer<GlobalKey<NavigatorState>>(),
        backgroundColor: theme.background.bgTertiary,
        textColor: theme.text.textPrimary,
        doneButtonText: localization.duplo_done_button,
      );
    });
  }

  void removeKeyboardActionStrip() {
    diContainer<CustomActionKeyboard>().dispose();
  }
}
