import 'package:duplo/src/assets/assets.gen.dart';
import 'package:duplo/src/components/duplo_tag_container/duplo_tag_container.dart';
import 'package:duplo/src/data/account_info.dart';
import 'package:duplo/src/components/duplo_payment_account_tile/utils/balance_formatter.dart';
import 'package:duplo/src/theming/duplo_theme.dart';
import 'package:duplo/src/theming/duplo_theme_data.dart';
import 'package:duplo/src/typography/duplo_font_weight.dart';
import 'package:duplo/src/typography/duplo_text.dart';
import 'package:duplo/src/typography/duplo_text_styles.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class DuploPaymentAccountTile extends StatelessWidget {
  const DuploPaymentAccountTile({
    super.key,
    required this.accountInfo,
    required this.isSelected,
    this.balanceFormatter = const DefaultBalanceFormatter(),
  });

  // Factory constructor for backward compatibility
  @Deprecated(
    'Use the default constructor with AccountInfo instead. '
    'This legacy constructor will be removed in a future version. '
    'Example: DuploPaymentAccountTile(accountInfo: AccountInfo(...), isSelected: true)',
  )
  factory DuploPaymentAccountTile.legacy({
    Key? key,
    required String accountNickName,
    required double balance,
    required String currency,
    required String accountNumber,
    required String accountPlatform,
    required String accountType,
    required SvgPicture currencyImage,
    required bool isSelected,
    required bool isWallet,
    BalanceFormatter balanceFormatter = const DefaultBalanceFormatter(),
  }) {
    return DuploPaymentAccountTile(
      key: key,
      accountInfo: AccountInfo(
        nickName: accountNickName,
        balance: balance,
        currency: currency,
        accountNumber: accountNumber,
        platform: accountPlatform,
        type: accountType,
        currencyImage: currencyImage,
        isWallet: isWallet,
      ),
      isSelected: isSelected,
      balanceFormatter: balanceFormatter,
    );
  }

  final AccountInfo accountInfo;
  final bool isSelected;
  final BalanceFormatter balanceFormatter;

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: _buildContainerDecoration(theme),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: _AccountDetailsSection(
              accountInfo: accountInfo,
              balanceFormatter: balanceFormatter,
            ),
          ),
          const SizedBox(width: 8),
          _SelectionIndicator(isSelected: isSelected),
        ],
      ),
    );
  }

  BoxDecoration _buildContainerDecoration(DuploThemeData theme) {
    return BoxDecoration(
      color: theme.background.bgPrimary,
      border: Border.all(
        color:
            isSelected
                ? theme.border.borderBrand
                : theme.border.borderSecondary,
        width: 1,
      ),
      borderRadius: BorderRadius.circular(12),
    );
  }
}

// Widget responsible for displaying account details
class _AccountDetailsSection extends StatelessWidget {
  const _AccountDetailsSection({
    required this.accountInfo,
    required this.balanceFormatter,
  });

  final AccountInfo accountInfo;
  final BalanceFormatter balanceFormatter;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _AccountTitleWidget(accountInfo: accountInfo),
        const SizedBox(height: 8),
        _BalanceDisplayWidget(
          balance: accountInfo.balance,
          currency: accountInfo.currency,
          balanceFormatter: balanceFormatter,
        ),
        const SizedBox(height: 8),
        _AccountTagsWidget(accountInfo: accountInfo),
      ],
    );
  }
}

// Widget responsible for displaying account title
class _AccountTitleWidget extends StatelessWidget {
  const _AccountTitleWidget({required this.accountInfo});

  final AccountInfo accountInfo;

  @override
  Widget build(BuildContext context) {
    final duploTextStyles = DuploTextStyles.of(context);
    final theme = DuploTheme.of(context);
    final localization = EquitiLocalization.of(context);

    final titleText =
        accountInfo.isWallet
            ? localization.duplo_balance
            : "${accountInfo.nickName} ${localization.duplo_balance}";

    return DuploText(
      text: titleText,
      style: duploTextStyles.textXs,
      fontWeight: DuploFontWeight.medium,
      color: theme.text.textSecondary,
    );
  }
}

// Widget responsible for displaying balance with integer and decimal parts
class _BalanceDisplayWidget extends StatelessWidget {
  const _BalanceDisplayWidget({
    required this.balance,
    required this.currency,
    required this.balanceFormatter,
  });

  final double balance;
  final String currency;
  final BalanceFormatter balanceFormatter;

  @override
  Widget build(BuildContext context) {
    final duploTextStyles = DuploTextStyles.of(context);
    final theme = DuploTheme.of(context);

    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        DuploText(
          text: balanceFormatter.formatIntegerPart(balance),
          style: duploTextStyles.displayXs,
          fontWeight: DuploFontWeight.semiBold,
          color: theme.text.textPrimary,
        ),
        DuploText(
          text: balanceFormatter.formatDecimalPart(balance),
          style: duploTextStyles.textMd,
          fontWeight: DuploFontWeight.regular,
          color: theme.text.textSecondary,
        ),
        const SizedBox(width: 2),
        DuploText(
          text: currency,
          style: duploTextStyles.textMd,
          fontWeight: DuploFontWeight.regular,
          color: theme.text.textSecondary,
        ),
      ],
    );
  }
}

// Widget responsible for displaying account tags
class _AccountTagsWidget extends StatelessWidget {
  const _AccountTagsWidget({required this.accountInfo});

  final AccountInfo accountInfo;

  @override
  Widget build(BuildContext context) {
    return Wrap(
      direction: Axis.horizontal,
      crossAxisAlignment: WrapCrossAlignment.center,
      spacing: 8,
      runSpacing: 8,
      children: [
        DuploTagContainer.sm(
          text: accountInfo.currency,
          leading: accountInfo.currencyImage,
        ),
        DuploTagContainer.xs(text: "#${accountInfo.accountNumber}"),
        if (!accountInfo.isWallet) ...[
          DuploTagContainer.xs(text: accountInfo.platform),
          DuploTagContainer.xs(text: accountInfo.type),
        ],
      ],
    );
  }
}

// Widget responsible for displaying selection indicator
class _SelectionIndicator extends StatelessWidget {
  const _SelectionIndicator({required this.isSelected});

  final bool isSelected;

  @override
  Widget build(BuildContext context) {
    return isSelected
        ? Assets.images.radioBase.svg()
        : Assets.images.emptyRadioBase.svg();
  }
}
