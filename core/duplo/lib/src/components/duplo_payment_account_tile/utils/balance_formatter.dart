/// Interface for balance formatting following Interface Segregation Principle
///
/// This interface defines the contract for formatting balance values,
/// allowing different implementations for various formatting needs.
abstract class BalanceFormatter {
  /// Formats the integer part of a balance value
  String formatIntegerPart(double balance);

  /// Formats the decimal part of a balance value
  String formatDecimalPart(double balance);
}

/// Default implementation of balance formatter
///
/// This concrete implementation provides standard balance formatting
/// with integer and decimal parts separated.
class DefaultBalanceFormatter implements BalanceFormatter {
  const DefaultBalanceFormatter();

  @override
  String formatIntegerPart(double balance) {
    return balance.truncate().toString();
  }

  @override
  String formatDecimalPart(double balance) {
    final decimalPart = ((balance - balance.truncate()) * 100).round();
    return '.${decimalPart.toString().padLeft(2, '0')}';
  }
}

/// Currency-aware balance formatter
///
/// This implementation can format balances with currency-specific rules
class CurrencyAwareBalanceFormatter implements BalanceFormatter {
  const CurrencyAwareBalanceFormatter({
    this.decimalPlaces = 2,
    this.thousandsSeparator = ',',
  });

  final int decimalPlaces;
  final String thousandsSeparator;

  @override
  String formatIntegerPart(double balance) {
    final integerPart = balance.truncate();
    return _addThousandsSeparator(integerPart.toString());
  }

  @override
  String formatDecimalPart(double balance) {
    final multiplier = _pow10(decimalPlaces);
    final decimalPart = ((balance - balance.truncate()) * multiplier).round();
    return '.${decimalPart.toString().padLeft(decimalPlaces, '0')}';
  }

  String _addThousandsSeparator(String number) {
    if (number.length <= 3) return number;

    final buffer = StringBuffer();
    final reversed = number.split('').reversed.toList();

    for (int i = 0; i < reversed.length; i++) {
      if (i > 0 && i % 3 == 0) {
        buffer.write(thousandsSeparator);
      }
      buffer.write(reversed[i]);
    }

    return buffer.toString().split('').reversed.join('');
  }

  int _pow10(int exponent) {
    int result = 1;
    for (int i = 0; i < exponent; i++) {
      result *= 10;
    }
    return result;
  }
}

/// Compact balance formatter for large numbers
///
/// This implementation formats large balances with K, M, B suffixes
class CompactBalanceFormatter implements BalanceFormatter {
  const CompactBalanceFormatter();

  @override
  String formatIntegerPart(double balance) {
    final absBalance = balance.abs();

    if (absBalance >= 1000000000) {
      return '${(balance / 1000000000).toStringAsFixed(1)}B';
    } else if (absBalance >= 1000000) {
      return '${(balance / 1000000).toStringAsFixed(1)}M';
    } else if (absBalance >= 1000) {
      return '${(balance / 1000).toStringAsFixed(1)}K';
    }
    return balance.truncate().toString();
  }

  @override
  String formatDecimalPart(double balance) {
    final absBalance = balance.abs();

    // For compact format, we don't show decimal parts for large numbers
    if (absBalance >= 1000) {
      return '';
    }

    final decimalPart = ((balance - balance.truncate()) * 100).round();
    return '.${decimalPart.toString().padLeft(2, '0')}';
  }
}
