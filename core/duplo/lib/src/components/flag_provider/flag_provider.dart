import 'dart:developer';

import 'package:duplo/src/assets/assets.gen.dart';
import 'package:flutter_svg/svg.dart';

/// A provider class for retrieving flag images based on country names, country codes, and currency codes.
///
/// This class contains static methods to fetch flag images in the form of
/// `SvgPicture` from hardcoded mappings of country names, country codes, and currency codes to
/// their respective SVG assets. If a flag for a given input is not found,
/// a flagPlaceholder flag is returned.
///
/// Example usage:
/// ```dart
/// // Fetching a flag image using a currency code
/// SvgPicture currencyFlag = FlagProvider.getFlagFromCurrencyCode('USD');
///
/// // Fetching a flag image using a country code
/// SvgPicture countryFlag = FlagProvider.getFlagFromCountryCode('IND');
///
/// // Fetching a flag image using a country name
/// SvgPicture countryFlag = FlagProvider.getFlagFromCountryName('India');
///
/// // Fetching the SVG path for a currency code
/// String currencyFlagPath = FlagProvider.getFlagPathFromCurrencyCode('EUR');
///
/// // Fetching the SVG path for a country code
/// String countryFlagPath = FlagProvider.getFlagPathFromCountryCode('IND');
///
/// // Fetching the SVG path for a country name
/// String countryFlagPath = FlagProvider.getFlagPathFromCountryName('India');
/// ```
///
class FlagProvider {
  // Hardcoded mapping of country codes to flag SVG paths
  static Map<String, SvgGenImage> _flagPathsToCountryCodeMap = {
    "AND": Assets.flags.andorra,
    "ARE": Assets.flags.unitedArabEmirates,
    "AFG": Assets.flags.afghanistan,
    "ATG": Assets.flags.antiguaAndBarbuda,
    "AIA": Assets.flags.anguilla,
    "ALB": Assets.flags.albania,
    "ARM": Assets.flags.armenia,
    "ANT": Assets.flags.netherlandsAntilles,
    "AGO": Assets.flags.angola,
    "ATA": Assets.flags.antartica,
    "ARG": Assets.flags.argentina,
    "ASM": Assets.flags.americanSamoa,
    "AUT": Assets.flags.austria,
    "AUS": Assets.flags.australia,
    "ABW": Assets.flags.aruba,
    "ALA": Assets.flags.alandIslands,
    "AZE": Assets.flags.azerbaijan,
    "BIH": Assets.flags.bosniaHerzegovina,
    "BRB": Assets.flags.barbados,
    "BGD": Assets.flags.bangladesh,
    "BEL": Assets.flags.belgium,
    "BFA": Assets.flags.burkinaFaso,
    "BGR": Assets.flags.bulgaria,
    "BHR": Assets.flags.bahrain,
    "BDI": Assets.flags.burundi,
    "BEN": Assets.flags.benin,
    "BMU": Assets.flags.bermuda,
    "BRN": Assets.flags.bruneiDarussalam,
    "BOL": Assets.flags.bolivia,
    "BRA": Assets.flags.brazil,
    "BHS": Assets.flags.bahamas,
    "BTN": Assets.flags.bhutan,
    "BVT": Assets.flags.bouvetIsland,
    "BWA": Assets.flags.botswana,
    "BLR": Assets.flags.belarus,
    "BLZ": Assets.flags.belize,
    "CAN": Assets.flags.canada,
    "CCK": Assets.flags.cocosIslands,
    "COD": Assets.flags.congoDemocraticRepublic,
    "CAF": Assets.flags.centralAfricanRepublic,
    "COG": Assets.flags.congoRepublic,
    "CHE": Assets.flags.switzerland,
    "CIV": Assets.flags.ivoryCoast,
    "COK": Assets.flags.cookIslands,
    "CHL": Assets.flags.chile,
    "CMR": Assets.flags.cameroon,
    "CHN": Assets.flags.china,
    "COL": Assets.flags.colombia,
    "CRI": Assets.flags.costaRica,
    "CUB": Assets.flags.cuba,
    "CPV": Assets.flags.capeVerde,
    "CXR": Assets.flags.christmasIsland,
    "CYP": Assets.flags.cyprus,
    "CZE": Assets.flags.czechRepublic,
    "DEU": Assets.flags.germany,
    "DJI": Assets.flags.djibouti,
    "DNK": Assets.flags.denmark,
    "DMA": Assets.flags.dominica,
    "DOM": Assets.flags.dominicanRepublic,
    "DZA": Assets.flags.algeria,
    "ECU": Assets.flags.ecuador,
    "EST": Assets.flags.estonia,
    "EGY": Assets.flags.egypt,
    "ESH": Assets.flags.westernSahara,
    "ERI": Assets.flags.eritrea,
    "ESP": Assets.flags.spain,
    "ETH": Assets.flags.ethiopia,
    "FIN": Assets.flags.finland,
    "FJI": Assets.flags.fiji,
    "FLK": Assets.flags.flagPlaceholder,
    "FSM": Assets.flags.micronesia,
    "FRO": Assets.flags.faroeIslands,
    "FRA": Assets.flags.france,
    "GAB": Assets.flags.gabon,
    "GBR": Assets.flags.unitedKingdom,
    "GRD": Assets.flags.grenada,
    "GEO": Assets.flags.georgia,
    "GUF": Assets.flags.frenchGuiana,
    "GGY": Assets.flags.guernsey,
    "GHA": Assets.flags.ghana,
    "GIB": Assets.flags.gibraltar,
    "GRL": Assets.flags.greenland,
    "GMB": Assets.flags.gambia,
    "GIN": Assets.flags.guinea,
    "GLP": Assets.flags.guadeloupe,
    "GNQ": Assets.flags.equatorialGuinea,
    "GRC": Assets.flags.greece,
    "SGS": Assets.flags.flagPlaceholder,
    "GTM": Assets.flags.guatemala,
    "GUM": Assets.flags.guam,
    "GNB": Assets.flags.guineaBissau,
    "GUY": Assets.flags.guyana,
    "HKG": Assets.flags.hongKong,
    "HMD": Assets.flags.heardIslandAndMcDonaldIslands,
    "HND": Assets.flags.honduras,
    "HRV": Assets.flags.croatia,
    "HTI": Assets.flags.haiti,
    "HUN": Assets.flags.hungary,
    "IDN": Assets.flags.indonesia,
    "IRL": Assets.flags.ireland,
    "ISR": Assets.flags.israel,
    "IMN": Assets.flags.isleOfMan,
    "IND": Assets.flags.india,
    "IOT": Assets.flags.britishIndianOceanTerritory,
    "IRQ": Assets.flags.iraq,
    "IRN": Assets.flags.iran,
    "ISL": Assets.flags.iceland,
    "ITA": Assets.flags.italy,
    "JEY": Assets.flags.jersey,
    "JAM": Assets.flags.jamaica,
    "JOR": Assets.flags.jordan,
    "JPN": Assets.flags.japan,
    "KEN": Assets.flags.kenya,
    "KGZ": Assets.flags.kyrgyzstan,
    "KHM": Assets.flags.cambodia,
    "KIR": Assets.flags.kiribati,
    "COM": Assets.flags.comoros,
    "KNA": Assets.flags.saintKittsAndNevis,
    "PRK": Assets.flags.northKorea,
    "KOR": Assets.flags.southKorea,
    "KWT": Assets.flags.kuwait,
    "CYM": Assets.flags.caymanIslands,
    "KAZ": Assets.flags.kazakhstan,
    "LAO": Assets.flags.laos,
    "LBN": Assets.flags.lebanon,
    "LCA": Assets.flags.saintLucia,
    "LIE": Assets.flags.liechtenstein,
    "LKA": Assets.flags.sriLanka,
    "LBR": Assets.flags.liberia,
    "LSO": Assets.flags.lesotho,
    "LTU": Assets.flags.lithuania,
    "LUX": Assets.flags.luxembourg,
    "LVA": Assets.flags.latvia,
    "LBY": Assets.flags.libya,
    "MAR": Assets.flags.morocco,
    "MCO": Assets.flags.monaco,
    "MDA": Assets.flags.moldova,
    "MNE": Assets.flags.montenegro,
    "MDG": Assets.flags.madagascar,
    "MHL": Assets.flags.marshallIslands,
    "MKD": Assets.flags.macedonia,
    "MLI": Assets.flags.mali,
    "MMR": Assets.flags.myanmar,
    "MNG": Assets.flags.mongolia,
    "MAC": Assets.flags.macau,
    "MNP": Assets.flags.northernMarianaIslands,
    "MTQ": Assets.flags.martinique,
    "MRT": Assets.flags.mauritania,
    "MSR": Assets.flags.montserrat,
    "MLT": Assets.flags.malta,
    "MUS": Assets.flags.mauritius,
    "MDV": Assets.flags.maldives,
    "MWI": Assets.flags.malawi,
    "MEX": Assets.flags.mexico,
    "MYS": Assets.flags.malaysia,
    "MOZ": Assets.flags.mozambique,
    "NAM": Assets.flags.namibia,
    "NCL": Assets.flags.newCaledonia,
    "NER": Assets.flags.niger,
    "NFK": Assets.flags.norfolkIsland,
    "NGA": Assets.flags.nigeria,
    "NIC": Assets.flags.nicaragua,
    "NLD": Assets.flags.netherlands,
    "NOR": Assets.flags.norway,
    "NPL": Assets.flags.nepal,
    "NRU": Assets.flags.nauru,
    "NIU": Assets.flags.niue,
    "NZL": Assets.flags.newZealand,
    "OMN": Assets.flags.oman,
    "PAN": Assets.flags.panama,
    "PER": Assets.flags.peru,
    "PYF": Assets.flags.frenchPolynesia,
    "PNG": Assets.flags.papuaNewGuinea,
    "PHL": Assets.flags.philippines,
    "PAK": Assets.flags.pakistan,
    "POL": Assets.flags.poland,
    "SPM": Assets.flags.saintPierreAndMiquelon,
    "PCN": Assets.flags.pitcairn,
    "PRI": Assets.flags.puertoRico,
    "PSE": Assets.flags.palestine,
    "PRT": Assets.flags.portugal,
    "PLW": Assets.flags.palau,
    "PRY": Assets.flags.paraguay,
    "QAT": Assets.flags.qatar,
    "REU": Assets.flags.rUnion,
    "ROU": Assets.flags.romania,
    "SRB": Assets.flags.serbia,
    "RUS": Assets.flags.russia,
    "RWA": Assets.flags.rwanda,
    "SAU": Assets.flags.saudiArabia,
    "SLB": Assets.flags.solomonIslands,
    "SYC": Assets.flags.seychelles,
    "SDN": Assets.flags.sudan,
    "SWE": Assets.flags.sweden,
    "SGP": Assets.flags.singapore,
    "SHN": Assets.flags.saintHelena,
    "SVN": Assets.flags.slovenia,
    "SJM": Assets.flags.svalbardAndJanMayen,
    "SVK": Assets.flags.slovakia,
    "SLE": Assets.flags.sierraLeone,
    "SMR": Assets.flags.sanMarino,
    "SEN": Assets.flags.senegal,
    "SOM": Assets.flags.somalia,
    "SUR": Assets.flags.suriname,
    "STP": Assets.flags.saoTomeAndPrincipe,
    "SLV": Assets.flags.elSalvador,
    "SYR": Assets.flags.syria,
    "SWZ": Assets.flags.swaziland,
    "TCA": Assets.flags.flagPlaceholder,
    "TCD": Assets.flags.chad,
    "ATF": Assets.flags.flagPlaceholder,
    "TGO": Assets.flags.togo,
    "THA": Assets.flags.thailand,
    "TJK": Assets.flags.tajikistan,
    "TKL": Assets.flags.flagPlaceholder,
    "TLS": Assets.flags.timorLeste,
    "TKM": Assets.flags.turkmenistan,
    "TUN": Assets.flags.tunisia,
    "TON": Assets.flags.tonga,
    "TUR": Assets.flags.turkey,
    "TTO": Assets.flags.trinidadAndTobago,
    "TUV": Assets.flags.tuvalu,
    "TWN": Assets.flags.taiwan,
    "TZA": Assets.flags.tanzania,
    "UKR": Assets.flags.ukraine,
    "UGA": Assets.flags.uganda,
    "UMI": Assets.flags.unitedStatesMinorOutlyingIslands,
    "USA": Assets.flags.unitedStates,
    "URY": Assets.flags.uruguay,
    "UZB": Assets.flags.uzbekistan,
    "VAT": Assets.flags.vaticanCityState,
    "VCT": Assets.flags.saintVincentAndTheGrenadines,
    "VEN": Assets.flags.venezuela,
    "VGB": Assets.flags.britishVirginIslands,
    "VIR": Assets.flags.uSVirginIslands,
    "VNM": Assets.flags.vietnam,
    "VUT": Assets.flags.vanuatu,
    "WLF": Assets.flags.flagPlaceholder,
    "WSM": Assets.flags.samoa,
    "XXK": Assets.flags.kosovo,
    "YEM": Assets.flags.yemen,
    "MYT": Assets.flags.mayotte,
    "ZAF": Assets.flags.southAfrica,
    "ZMB": Assets.flags.zambia,
    "ZWE": Assets.flags.zimbabwe,
  };

  static Map<String, SvgGenImage> _flagPathsToCurrencyCodeMap = {
    "AUD": Assets.flags.australia,
    "AED": Assets.flags.unitedArabEmirates,
    "CAD": Assets.flags.canada,
    "CHF": Assets.flags.switzerland,
    "CNY": Assets.flags.china,
    "GBP": Assets.flags.unitedKingdom,
    "HKD": Assets.flags.hongKong,
    "INR": Assets.flags.india,
    "JPY": Assets.flags.japan,
    "KRW": Assets.flags.southKorea,
    "MXN": Assets.flags.mexico,
    "NOK": Assets.flags.norway,
    "NZD": Assets.flags.newZealand,
    "RUB": Assets.flags.russia,
    "SGD": Assets.flags.singapore,
    "SEK": Assets.flags.sweden,
    "USD": Assets.flags.unitedStates,
    "ZAR": Assets.flags.southAfrica,
    "JOD": Assets.flags.jordan,
    "UGX": Assets.flags.uganda,
    "TRY": Assets.flags.turkey,
    "QAR": Assets.flags.qatar,
    "TND": Assets.flags.tunisia,
    "THB": Assets.flags.thailand,
    "EUR": Assets.flags.europeanUnion,
  };

  // Hardcoded mapping of country names to country codes
  static Map<String, String> _countryNameToCodeMap = {
    "Andorra": "AND",
    "United Arab Emirates": "ARE",
    "Afghanistan": "AFG",
    "Antigua and Barbuda": "ATG",
    "Anguilla": "AIA",
    "Albania": "ALB",
    "Armenia": "ARM",
    "Angola": "AGO",
    "Argentina": "ARG",
    "American Samoa": "ASM",
    "Austria": "AUT",
    "Australia": "AUS",
    "Aruba": "ABW",
    "Azerbaijan": "AZE",
    "Bosnia and Herzegovina": "BIH",
    "Barbados": "BRB",
    "Bangladesh": "BGD",
    "Belgium": "BEL",
    "Burkina Faso": "BFA",
    "Bulgaria": "BGR",
    "Bahrain": "BHR",
    "Burundi": "BDI",
    "Benin": "BEN",
    "Brunei Darussalam": "BRN",
    "Bolivia": "BOL",
    "Brazil": "BRA",
    "Bahamas": "BHS",
    "Bhutan": "BTN",
    "Botswana": "BWA",
    "Belarus": "BLR",
    "Belize": "BLZ",
    "Canada": "CAN",
    "Congo, Democratic Republic of the": "COD",
    "Central African Republic": "CAF",
    "Congo": "COG",
    "Switzerland": "CHE",
    "Cote d'Ivoire": "CIV",
    "Cook Islands": "COK",
    "Chile": "CHL",
    "Cameroon": "CMR",
    "China": "CHN",
    "Colombia": "COL",
    "Costa Rica": "CRI",
    "Cuba": "CUB",
    "Cape Verde": "CPV",
    "Cyprus": "CYP",
    "Czech Republic": "CZE",
    "Germany": "DEU",
    "Djibouti": "DJI",
    "Denmark": "DNK",
    "Dominica": "DMA",
    "Dominican Republic": "DOM",
    "Algeria": "DZA",
    "Ecuador": "ECU",
    "Estonia": "EST",
    "Egypt": "EGY",
    "Western Sahara": "ESH",
    "Eritrea": "ERI",
    "Spain": "ESP",
    "Ethiopia": "ETH",
    "Finland": "FIN",
    "Fiji": "FJI",
    "Falkland Islands (Malvinas)": "FLK",
    "Micronesia, Federated States of": "FSM",
    "Faroe Islands": "FRO",
    "France": "FRA",
    "Gabon": "GAB",
    "United Kingdom": "GBR",
    "Grenada": "GRD",
    "Georgia": "GEO",
    "French Guiana": "GUF",
    "Ghana": "GHA",
    "Gibraltar": "GIB",
    "Greenland": "GRL",
    "Gambia": "GMB",
    "Guinea": "GIN",
    "Guadeloupe": "GLP",
    "Equatorial Guinea": "GNQ",
    "Greece": "GRC",
    "Guatemala": "GTM",
    "Guam": "GUM",
    "Guinea-Bissau": "GNB",
    "Guyana": "GUY",
    "Honduras": "HND",
    "Croatia": "HRV",
    "Haiti": "HTI",
    "Hungary": "HUN",
    "Indonesia": "IDN",
    "Ireland": "IRL",
    "Israel": "ISR",
    "India": "IND",
    "Iraq": "IRQ",
    "Iran, Islamic Republic of": "IRN",
    "Iceland": "ISL",
    "Italy": "ITA",
    "Jamaica": "JAM",
    "Jordan": "JOR",
    "Japan": "JPN",
    "Kenya": "KEN",
    "Kyrgyzstan": "KGZ",
    "Cambodia": "KHM",
    "Kiribati": "KIR",
    "Comoros": "COM",
    "Saint Kitts and Nevis": "KNA",
    "Korea, Democratic People's Republic of": "PRK",
    "Korea, Republic of": "KOR",
    "Kosovo": "XXK",
    "Kuwait": "KWT",
    "Cayman Islands": "CYM",
    "Kazakhstan": "KAZ",
    "Lao People's Democratic Republic": "LAO",
    "Lebanon": "LBN",
    "Saint Lucia": "LCA",
    "Liechtenstein": "LIE",
    "Sri Lanka": "LKA",
    "Liberia": "LBR",
    "Lesotho": "LSO",
    "Lithuania": "LTU",
    "Luxembourg": "LUX",
    "Latvia": "LVA",
    "Libya": "LBY",
    "Morocco": "MAR",
    "Monaco": "MCO",
    "Moldova, Republic of": "MDA",
    "Montenegro": "MNE",
    "Madagascar": "MDG",
    "Marshall Islands": "MHL",
    "Macedonia, the former Yugoslav Republic of": "MKD",
    "Mali": "MLI",
    "Myanmar": "MMR",
    "Mongolia": "MNG",
    "Macao": "MAC",
    "Northern Mariana Islands": "MNP",
    "Martinique": "MTQ",
    "Mauritania": "MRT",
    "Malta": "MLT",
    "Mauritius": "MUS",
    "Maldives": "MDV",
    "Malawi": "MWI",
    "Mexico": "MEX",
    "Malaysia": "MYS",
    "Mozambique": "MOZ",
    "Namibia": "NAM",
    "New Caledonia": "NCL",
    "Niger": "NER",
    "Nigeria": "NGA",
    "Nicaragua": "NIC",
    "Netherlands": "NLD",
    "Norway": "NOR",
    "Nepal": "NPL",
    "Nauru": "NRU",
    "Niue": "NIU",
    "New Zealand": "NZL",
    "Oman": "OMN",
    "Panama": "PAN",
    "Peru": "PER",
    "French Polynesia": "PYF",
    "Papua New Guinea": "PNG",
    "Philippines": "PHL",
    "Pakistan": "PAK",
    "Poland": "POL",
    "Pitcairn": "PCN",
    "Puerto Rico": "PRI",
    "Palestinian Territory, Occupied": "PSE",
    "Portugal": "PRT",
    "Palau": "PLW",
    "Paraguay": "PRY",
    "Qatar": "QAT",
    "Reunion": "REU",
    "Romania": "ROU",
    "Serbia": "SRB",
    "Russian Federation": "RUS",
    "Rwanda": "RWA",
    "Saudi Arabia": "SAU",
    "Solomon Islands": "SLB",
    "Seychelles": "SYC",
    "Sudan": "SDN",
    "Sweden": "SWE",
    "Singapore": "SGP",
    "Saint Helena": "SHN",
    "Slovenia": "SVN",
    "Slovakia": "SVK",
    "Sierra Leone": "SLE",
    "San Marino": "SMR",
    "Senegal": "SEN",
    "Somalia": "SOM",
    "Suriname": "SUR",
    "Sao Tome and Principe": "STP",
    "El Salvador": "SLV",
    "Syrian Arab Republic": "SYR",
    "Swaziland": "SWZ",
    "Chad": "TCD",
    "Togo": "TGO",
    "Thailand": "THA",
    "Tajikistan": "TJK",
    "Timor-Leste": "TLS",
    "Turkmenistan": "TKM",
    "Tunisia": "TUN",
    "Tonga": "TON",
    "Turkey": "TUR",
    "Trinidad and Tobago": "TTO",
    "Tuvalu": "TUV",
    "Taiwan, Province of China": "TWN",
    "Tanzania, United Republic of": "TZA",
    "Ukraine": "UKR",
    "Uganda": "UGA",
    "United States": "USA",
    "Uruguay": "URY",
    "Uzbekistan": "UZB",
    "Holy See (Vatican City State)": "VAT",
    "Saint Vincent and the Grenadines": "VCT",
    "Venezuela": "VEN",
    "Virgin Islands, British": "VGB",
    "Virgin Islands, U.S.": "VIR",
    "Viet Nam": "VNM",
    "Vanuatu": "VUT",
    "Samoa": "WSM",
    "Yemen": "YEM",
    "South Africa": "ZAF",
    "Zambia": "ZMB",
    "Zimbabwe": "ZWE",
  };

  static SvgPicture getFlagFromCurrencyCode(
    String currencyCode, {
    double width = 20,
    double height = 20,
  }) {
    return _flagPathsToCurrencyCodeMap[currencyCode]?.svg(
          width: width,
          height: height,
        ) ??
        Assets.flags.flagPlaceholder.svg(width: width, height: height);
  }

  static SvgPicture getFlagFromCountryCode(
    String countryCode, {
    double width = 20,
    double height = 20,
  }) {
    try {
      return _flagPathsToCountryCodeMap[countryCode]?.svg(
            width: width,
            height: height,
          ) ??
          Assets.flags.flagPlaceholder.svg(width: width, height: height);
    } on Exception catch (e) {
      log("Error in getFlagFromCountryCode: $e");
      return Assets.flags.flagPlaceholder.svg(width: width, height: height);
    }
  }

  static String getFlagPathFromCurrencyCode(String currencyCode) {
    return _flagPathsToCurrencyCodeMap[currencyCode]?.keyName ??
        Assets.flags.flagPlaceholder.keyName;
  }

  static String getFlagPathFromCountryCode(String countryCode) {
    return _flagPathsToCountryCodeMap[countryCode]?.keyName ??
        Assets.flags.flagPlaceholder.keyName;
  }

  /// Fetches a flag image using a country name.
  ///
  /// This method takes a country name (e.g., "United States", "India") and
  /// returns the corresponding flag as an `SvgPicture`. If the country name
  /// is not found in the mapping, a flagPlaceholder flag is returned.
  ///
  /// The country name matching is case-sensitive and should match exactly
  /// with the names defined in the internal mapping.
  ///
  /// Example usage:
  /// ```dart
  /// SvgPicture flag = FlagProvider.getFlagFromCountryName('United States');
  /// SvgPicture flag = FlagProvider.getFlagFromCountryName('India');
  /// ```
  ///
  /// Parameters:
  /// - [countryName]: The full name of the country
  /// - [width]: The width of the flag image (default: 20)
  /// - [height]: The height of the flag image (default: 20)
  ///
  /// Returns an `SvgPicture` widget containing the flag image.
  static SvgPicture getFlagFromCountryName(
    String countryName, {
    double width = 20,
    double height = 20,
  }) {
    final countryCode = _countryNameToCodeMap[countryName];
    if (countryCode != null) {
      return getFlagFromCountryCode(countryCode, width: width, height: height);
    }
    return Assets.flags.flagPlaceholder.svg(width: width, height: height);
  }

  /// Fetches the SVG path for a country name.
  ///
  /// This method takes a country name and returns the corresponding flag's
  /// SVG asset path. If the country name is not found, the flagPlaceholder
  /// flag path is returned.
  ///
  /// Example usage:
  /// ```dart
  /// String flagPath = FlagProvider.getFlagPathFromCountryName('United States');
  /// ```
  ///
  /// Parameters:
  /// - [countryName]: The full name of the country
  ///
  /// Returns a String containing the SVG asset path.
  static String getFlagPathFromCountryName(String countryName) {
    final countryCode = _countryNameToCodeMap[countryName];
    if (countryCode != null) {
      return getFlagPathFromCountryCode(countryCode);
    }
    return Assets.flags.flagPlaceholder.keyName;
  }
}
