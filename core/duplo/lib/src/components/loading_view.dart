import 'package:duplo/src/assets/assets.gen.dart';
import 'package:duplo/src/constants/duplo_spacing.dart';
import 'package:duplo/src/theming/duplo_theme_context_extension.dart';
import 'package:duplo/src/typography/duplo_font_weight.dart';
import 'package:duplo/src/typography/duplo_text.dart';
import 'package:duplo/src/typography/duplo_typography_context_extension.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';

class LoadingView extends StatelessWidget {
  const LoadingView({super.key, this.title, this.subtitle});
  final String? title;
  final String? subtitle;
  @override
  Widget build(BuildContext context) {
    final style = context.duploTextStyles;
    final theme = context.duploTheme;
    final localization = EquitiLocalization.of(context);

    return Scaffold(
      backgroundColor: theme.background.bgPrimary,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            LottieBuilder.asset(
              Assets.animations.loading,
              width: 150,
              height: 150,
            ),
            SizedBox(height: DuploSpacing.spacing_3xl_24),
            //todo: add localization
            Column(
              children: [
                DuploText(
                  text: title ?? localization.duplo_loading_title,
                  style: style.textMd,
                  fontWeight: DuploFontWeight.semiBold,
                  color: theme.text.textSecondary,
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: DuploSpacing.spacing_xs_4),
                DuploText(
                  text: subtitle ?? localization.duplo_loading_message,
                  style: style.textXs,
                  fontWeight: DuploFontWeight.regular,
                  color: theme.text.textTertiary,
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
