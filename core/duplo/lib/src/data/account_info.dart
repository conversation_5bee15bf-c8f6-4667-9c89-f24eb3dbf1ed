import 'package:flutter_svg/svg.dart';

/// Data model for account information
///
/// This model encapsulates all the necessary information about a payment account
/// following the Single Responsibility Principle by only handling account data.
/// Provides immutability through final fields and copyWith functionality.
class AccountInfo {
  const AccountInfo({
    required this.nickName,
    required this.balance,
    required this.currency,
    required this.accountNumber,
    required this.platform,
    required this.type,
    required this.currencyImage,
    required this.isWallet,
  });

  final String nickName;
  final double balance;
  final String currency;
  final String accountNumber;
  final String platform;
  final String type;
  final SvgPicture currencyImage;
  final bool isWallet;

  /// Creates a copy of this AccountInfo with the given fields replaced with new values
  AccountInfo copyWith({
    String? nickName,
    double? balance,
    String? currency,
    String? accountNumber,
    String? platform,
    String? type,
    SvgPicture? currencyImage,
    bool? isWallet,
  }) {
    return AccountInfo(
      nickName: nickName ?? this.nickName,
      balance: balance ?? this.balance,
      currency: currency ?? this.currency,
      accountNumber: accountNumber ?? this.accountNumber,
      platform: platform ?? this.platform,
      type: type ?? this.type,
      currencyImage: currencyImage ?? this.currencyImage,
      isWallet: isWallet ?? this.isWallet,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AccountInfo &&
        other.nickName == nickName &&
        other.balance == balance &&
        other.currency == currency &&
        other.accountNumber == accountNumber &&
        other.platform == platform &&
        other.type == type &&
        other.currencyImage == currencyImage &&
        other.isWallet == isWallet;
  }

  @override
  int get hashCode {
    return Object.hash(
      nickName,
      balance,
      currency,
      accountNumber,
      platform,
      type,
      currencyImage,
      isWallet,
    );
  }

  @override
  String toString() {
    return 'AccountInfo(nickName: $nickName, balance: $balance, currency: $currency, accountNumber: $accountNumber, platform: $platform, type: $type, currencyImage: $currencyImage, isWallet: $isWallet)';
  }
}
