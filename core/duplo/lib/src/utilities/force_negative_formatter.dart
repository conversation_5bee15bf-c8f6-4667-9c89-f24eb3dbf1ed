import 'package:flutter/services.dart';

class ForceNegativeFormatter extends TextInputFormatter {
  const ForceNegativeFormatter();

  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    final text = newValue.text;

    // Allow empty input or just minus sign
    if (text.isEmpty || text == '-') {
      return newValue;
    }

    // Parse the number
    final numValue = double.tryParse(text);
    if (numValue == null) {
      return oldValue; // Invalid input, keep old value
    }

    // Reject zero
    if (numValue == 0) {
      return oldValue;
    }

    // Convert positive to negative
    if (numValue > 0) {
      final negativeText = '-$text';
      return TextEditingValue(
        text: negativeText,
        selection: TextSelection.collapsed(offset: negativeText.length),
      );
    }

    // Already negative, allow it
    return newValue;
  }
}
