import 'package:duplo/duplo.dart';
import 'package:duplo/src/di/di_container.dart';
import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';
import 'package:locale_manager/locale_manager.dart';
import 'package:theme_manager/theme_manager.dart';

import '../mocks/locale_manager_mock.dart';
import '../mocks/theme_manager_mock.dart';

Future<void> setupDi() async {
  final gh = GetItHelper(diContainer);

  gh.lazySingleton<LocaleManager>(() => LocaleManagerMock());
  gh.lazySingleton<ThemeManager>(() => ThemeManagerMock());
  gh.lazySingleton(() => GlobalKey<NavigatorState>());
  await DuploPackageModule().init(gh);
}
