import 'package:equiti_test/equiti_test.dart';
import 'duplo_alert_message_data.dart';

Feature: DuploAlertMessage Golden Tests

@testMethodName: testGoldens
Scenario Outline: Duplo Alert Message
  Given The <widget> app is rendered
  Then screenshot verified <golden_file_name>

  Examples:
    | widget | golden_file_name             |
    | infoAlerts | 'duplo_info_alert_messages'   |
    | warningAlerts | 'duplo_warning_alert_messages'   |
    | errorAlerts | 'duplo_error_alert_messages'   |
