import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';

final infoAlerts = Scaffold(
  body: Padding(
    padding: const EdgeInsets.all(16.0),
    child: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        DuploAlertMessage.info(
          title:
              'This is demo text. Fontweight can be changed. Lorem ipsum dolor sit amet consectetur adipisicing elit.',
        ),
        SizedBox(height: 16),
        DuploAlertMessage.info(
          title: 'This is demo text.',
          expandable: true,
          children: [
            Text(
              'Lorem ipsum dolor sit amet consectetur adipisicing elit. Aliquid pariatur, ipsum dolor.',
            ),
          ],
        ),
        Sized<PERSON><PERSON>(height: 16),
        DuploAlertMessage.info(
          title: 'This is demo text. ',
          subtitle: 'This is demo subtitle. It is nullable.',
          expandable: true,
          children: [
            Text(
              'Lorem ipsum dolor sit amet consectetur adipisicing elit. Aliquid pariatur, ipsum dolor.',
            ),
          ],
        ),
        Sized<PERSON>ox(height: 16),
        DuploAlertMessage.info(
          title: 'This is demo text. ',
          subtitle: 'This is demo subtitle. It is nullable.',
          primaryAction: 'Dismiss',
          secondaryAction: 'Do Action',
          expandable: true,
          children: [
            Text(
              'Lorem ipsum dolor sit amet consectetur adipisicing elit. Aliquid pariatur, ipsum dolor.',
            ),
          ],
        ),
      ],
    ),
  ),
);

final warningAlerts = Scaffold(
  body: Padding(
    padding: EdgeInsets.all(16),
    child: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        DuploAlertMessage.warning(
          title:
              'This is demo text. Fontweight can be changed. Lorem ipsum dolor sit amet consectetur adipisicing elit.',
        ),
        SizedBox(height: 16),
        DuploAlertMessage.warning(
          title: 'This is demo text. ',
          expandable: true,
          children: [
            Text(
              'Lorem ipsum dolor sit amet consectetur adipisicing elit. Aliquid pariatur, ipsum dolor.',
            ),
          ],
        ),
        SizedBox(height: 16),
        DuploAlertMessage.warning(
          title: 'This is demo text. ',
          subtitle: 'This is demo subtitle. It is nullable.',
          expandable: true,
          children: [
            Text(
              'Lorem ipsum dolor sit amet consectetur adipisicing elit. Aliquid pariatur, ipsum dolor.',
            ),
          ],
        ),
        SizedBox(height: 16),
        DuploAlertMessage.warning(
          title: 'This is demo text. ',
          subtitle: 'This is demo subtitle. It is nullable.',
          primaryAction: 'Dismiss',
          secondaryAction: 'Do Action',
          expandable: true,
          children: [
            Text(
              'Lorem ipsum dolor sit amet consectetur adipisicing elit. Aliquid pariatur, ipsum dolor.',
            ),
          ],
        ),
      ],
    ),
  ),
);

final errorAlerts = Scaffold(
  body: Padding(
    padding: EdgeInsets.all(16),
    child: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        DuploAlertMessage.error(
          title:
              'This is demo text. Fontweight can be changed. Lorem ipsum dolor sit amet consectetur adipisicing elit.',
        ),
        SizedBox(height: 16),
        DuploAlertMessage.error(
          title: 'This is demo text. ',
          expandable: true,
          children: [
            Text(
              'Lorem ipsum dolor sit amet consectetur adipisicing elit. Aliquid pariatur, ipsum dolor.',
            ),
          ],
        ),
        SizedBox(height: 16),
        DuploAlertMessage.error(
          title: 'This is demo text. ',
          subtitle: 'This is demo subtitle. It is nullable.',
          expandable: true,
          children: [
            Text(
              'Lorem ipsum dolor sit amet consectetur adipisicing elit. Aliquid pariatur, ipsum dolor.',
            ),
          ],
        ),
        SizedBox(height: 16),
        DuploAlertMessage.error(
          title: 'This is demo text. ',
          subtitle: 'This is demo subtitle. It is nullable.',
          primaryAction: 'Dismiss',
          secondaryAction: 'Do Action',
          expandable: true,
          children: [
            Text(
              'Lorem ipsum dolor sit amet consectetur adipisicing elit. Aliquid pariatur, ipsum dolor.',
            ),
          ],
        ),
      ],
    ),
  ),
);
