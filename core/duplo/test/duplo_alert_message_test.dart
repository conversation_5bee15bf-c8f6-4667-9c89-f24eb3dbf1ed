// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: unused_import, directives_ordering

import 'package:equiti_test/equiti_test.dart';
import 'duplo_alert_message_data.dart';

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import './bdd_hooks/hooks.dart';
import './step/the_app_is_rendered.dart';
import 'package:bdd_steps/step/screenshot_verified.dart';

void main() {
  setUpAll(() async {
    await Hooks.beforeAll();
  });
  tearDownAll(() async {
    await Hooks.afterAll();
  });

  group('''DuploAlertMessage Golden Tests''', () {
    Future<void> beforeEach(String title, [List<String>? tags]) async {
      await Hooks.beforeEach(title, tags);
    }

    Future<void> afterEach(
      String title,
      bool success, [
      List<String>? tags,
    ]) async {
      await Hooks.afterEach(title, success, tags);
    }

    testGoldens(
      '''Outline: Duplo Alert Message (infoAlerts, 'duplo_info_alert_messages')''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''Outline: Duplo Alert Message (infoAlerts, 'duplo_info_alert_messages')''',
          );
          await theAppIsRendered(tester, infoAlerts);
          await screenshotVerified(tester, 'duplo_info_alert_messages');
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''Outline: Duplo Alert Message (infoAlerts, 'duplo_info_alert_messages')''',
            success,
          );
        }
      },
    );
    testGoldens(
      '''Outline: Duplo Alert Message (warningAlerts, 'duplo_warning_alert_messages')''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''Outline: Duplo Alert Message (warningAlerts, 'duplo_warning_alert_messages')''',
          );
          await theAppIsRendered(tester, warningAlerts);
          await screenshotVerified(tester, 'duplo_warning_alert_messages');
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''Outline: Duplo Alert Message (warningAlerts, 'duplo_warning_alert_messages')''',
            success,
          );
        }
      },
    );
    testGoldens(
      '''Outline: Duplo Alert Message (errorAlerts, 'duplo_error_alert_messages')''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''Outline: Duplo Alert Message (errorAlerts, 'duplo_error_alert_messages')''',
          );
          await theAppIsRendered(tester, errorAlerts);
          await screenshotVerified(tester, 'duplo_error_alert_messages');
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''Outline: Duplo Alert Message (errorAlerts, 'duplo_error_alert_messages')''',
            success,
          );
        }
      },
    );
  });
}
