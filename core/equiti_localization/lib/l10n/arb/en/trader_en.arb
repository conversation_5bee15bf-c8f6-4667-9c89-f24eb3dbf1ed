{"@@locale": "en", "@@prefix_key": "trader", "trader_withdrawTitle": "Withdraw", "@trader_withdrawTitle": {}, "trader_buy": "Buy", "@trader_buy": {}, "trader_sell": "<PERSON>ll", "@trader_sell": {}, "trader_spread": "Spread", "@trader_spread": {}, "trader_viewOptions": "View Options", "@trader_viewOptions": {}, "trader_sortMarkets": "Sort Markets", "@trader_sortMarkets": {}, "trader_view": "View", "@trader_view": {}, "trader_sortListBy": "Sort List By", "@trader_sortListBy": {}, "trader_categories": "Categories", "@trader_categories": {}, "trader_sort": "Sort", "@trader_sort": {}, "trader_applyView": "Apply View", "@trader_applyView": {}, "trader_aToZ": "A - Z", "@trader_aToZ": {}, "trader_zToA": "Z - A", "@trader_zToA": {}, "trader_noDataAvailable": "No data available ", "@trader_noDataAvailable": {}, "trader_somethingWentWrong": "Something went wrong", "@trader_somethingWentWrong": {}, "trader_error": "Error", "@trader_error": {}, "trader_nothingToShow": "Nothing to show", "@trader_nothingToShow": {}, "trader_market": "Market", "@trader_market": {}, "trader_switchAccount": "Switch account", "@trader_switchAccount": {}, "trader_totalPortfolioValue": "Total Portfolio Value", "@trader_totalPortfolioValue": {}, "trader_todaysPNL": "Today''s PNL", "@trader_todaysPNL": {}, "trader_cfdTradingAccount": "CFD trading account", "@trader_cfdTradingAccount": {}, "trader_noAccountsAvailable": "No accounts available.", "@trader_noAccountsAvailable": {}, "trader_exploreAnotherProducts": "Explore another products", "@trader_exploreAnotherProducts": {}, "trader_goldPromoBanner": "Gold promo banner", "@trader_goldPromoBanner": {}, "trader_mamPromoBanner": "MAM promo banner", "@trader_mamPromoBanner": {}, "trader_addNewAccount": "Add new account", "@trader_addNewAccount": {}, "trader_addNewWallet": "Add new wallet", "@trader_addNewWallet": {}, "trader_chooseAccountCurrency": "Choose account currency", "@trader_chooseAccountCurrency": {}, "trader_defaultCurrencyDescription": "This will be the default currency for your wallet.", "@trader_defaultCurrencyDescription": {}, "trader_walletCurrency": "Wallet currency", "@trader_walletCurrency": {}, "trader_walletCreatedSuccessfully": "Wallet Created Successfully", "@trader_walletCreatedSuccessfully": {}, "trader_walletCreatedDescription": "Your new wallet has been created and is ready to use.", "@trader_walletCreatedDescription": {}, "trader_walletCreationFailed": "Wallet Creation Failed", "@trader_walletCreationFailed": {}, "trader_walletCreationFailedDescription": "We couldn't create your wallet at this time. Please try again.", "@trader_walletCreationFailedDescription": {}, "trader_live": "Live", "@trader_live": {}, "trader_demo": "Demo", "@trader_demo": {}, "trader_usedMargin": "Used margin", "@trader_usedMargin": {}, "trader_freeMargin": "Free margin:", "@trader_freeMargin": {}, "trader_todaysPL": "Today''s P/L", "@trader_todaysPL": {}, "trader_placeholderText": "Something went wrong while closing the trade. Please try again later.", "@trader_placeholderText": {}, "trader_filter": "Filter", "@trader_filter": {}, "trader_placing": "Placing...", "@trader_placing": {}, "trader_placed": "Placed", "@trader_placed": {}, "trader_tradingHours": "Trading hours", "@trader_tradingHours": {}, "trader_upComingHolidayHour": "Upcoming Holiday Hours", "@trader_upComingHolidayHour": {}, "trader_itWillCloseIn": "It will close in:", "@trader_itWillCloseIn": {}, "trader_itWillOpen": "It will open in:", "@trader_itWillOpen": {}, "trader_marketIsHoliday": "Market is closed due to holiday hours", "@trader_marketIsHoliday": {}, "trader_marketIs": "Market is", "@trader_marketIs": {}, "trader_open": "Open", "@trader_open": {}, "trader_close": "Close", "@trader_close": {}, "trader_day": "{count, plural, =0 {Day} =1 {Day} other {Days}}", "trader_hour": "{count, plural, =0 {Hour} =1 {Hour} other {Hours}}", "trader_minute": "{count, plural, =0 {Minute} =1 {Minute} other {Minutes}}", "trader_second": "{count, plural, =0 {Second} =1 {Second} other {Seconds}}", "trader_sunday": "Sunday", "@trader_sunday": {}, "trader_monday": "Monday", "@trader_monday": {}, "trader_tuesday": "Tuesday", "@trader_tuesday": {}, "trader_wednesday": "Wednesday", "@trader_wednesday": {}, "trader_thursday": "Thursday", "@trader_thursday": {}, "trader_friday": "Friday", "@trader_friday": {}, "trader_saturday": "Saturday", "@trader_saturday": {}, "trader_noInformationAvailable": "No Information Available", "@trader_noInformationAvailable": {}, "trader_searchMarkets": "Search Markets", "trader_resultsFound": "results found", "@trader_resultsFound": {}, "trader_previousSearches": "Previous Searches", "@trader_previousSearches": {}, "trader_suggestedSearches": "Suggested Searches", "@trader_suggestedSearches": {}, "trader_selectCardView": "Select Card View", "trader_nothingFound": "Nothing found", "trader_noSearchResults": "It doesn’t look like we have the market you are looking for.", "trader_searchError": "We seem to be having an issue preforming your search at the moment, Please try again later.", "trader_searching": "Searching...", "trader_viewAll": "View all", "trader_productDetails": "Product Details", "trader_classifications": "Classifications", "trader_assetClass": "Asset Class", "trader_assetType": "Asset Type", "trader_general": "General", "trader_baseCurrency": "Base currency", "trader_contractSize": "Contract Size", "trader_contractSizeUnit": "Contract Size Unit", "trader_spreadValue": "Spread Value", "trader_marginRate": "Margin Rate", "trader_positionLimits": "Position Limits", "trader_minimumLot": "Minimum Lot", "trader_maximumLot": "Maximum Lot", "trader_lotSteps": "Lot Steps", "trader_productFinancing": "Product Financing", "trader_commisionChanges": "Commision Changes", "trader_productExpiry": "Product Expiry", "trader_expiryDate": "Expiry Date", "trader_expiryCode": "Expiry Code", "trader_swaps": "Swaps", "trader_swapsLong": "Swa<PERSON> Long", "trader_swapsShort": "Swaps Short", "trader_high": "High", "trader_low": "Low", "trader_closed": "Closed", "trader_marketLoadFailed": "Cannot load market session!", "trader_marketExpiryDate": "Market Expiry Date: ", "trader_noHolidays": "None announced at this time", "trader_noMarketSessionToday": "No session today", "trader_overview": "Overview", "trader_events": "Events", "trader_news": "News", "trader_sortMarketsby": "Sort Markets by", "trader_quickTrade": "Quick Trade", "trader_quickTradeTurnedOn": "You have quick trade turned on, this will place a ", "trader_quickTradeMarketLimit": "Market Limit Trade right away at {lotSize} Lots.", "trader_quickTradeSelect": "Select place trade to make your trade & this message will not show again.", "trader_placeTrade": "Place trade", "trader_cancel": "Cancel", "trader_unableToUpdateWatchlistPleaseTryAgain": "Unable to update watchlist. Please try again.", "@trader_unableToUpdateWatchlistPleaseTryAgain": {}, "trader_distance": "Distance", "trader_enterPrice": "Enter price", "trader_alertPrice": "<PERSON><PERSON>", "trader_setAlert": "Set an alert", "trader_activeAlerts": "Active alerts", "trader_priceAlert": "Price Alert", "trader_priceMinRange": "Price must be greater than", "trader_showMarketDetails": "Show Market Details", "trader_marketHours": "Market Hours", "trader_done": "Done", "trader_marketsLoadFailed": "We were not able to load these markets at this time.", "trader_noSymbolsFound": "We don’t have any markets available in this category", "trader_noMarkets": "No Markets", "trader_noMarketAdded": "No Markets added yet", "trader_noMarketAddedDescription": "You don’t have any markets added to your watchlist", "trader_midPriceBanner": "Mid Price / 1D Change", "trader_discover": "Discover", "@trader_discover": {}, "trader_markets": "Markets", "@trader_markets": {}, "trader_portfolio": "Portfolio", "@trader_portfolio": {}, "trader_performance": "Activity", "@trader_performance": {}, "trader_more": "More", "@trader_more": {}, "trader_loadingError": "Loading error", "trader_loadingErrorDescription": "We apologies it seems some of the information has failed to load.", "trader_reload": "Reload", "trader_opens": "Opens", "trader_saveAlert": "Save alert", "trader_tradeDetails": "Trade Details", "trader_grossProfit": "Gross profit", "trader_netProfit": "Net profit", "trader_commision": "Commission", "trader_takeProfit": "Take profit", "trader_stopLoss": "Stop loss", "trader_tradeIDNumber": "Trade ID number", "trader_product": "Product", "trader_marketClass": "Market class", "trader_marketType": "Market type", "trader_execution": "Execution", "trader_opened": "Opened", "trader_openPrice": "Open Price", "trader_direction": "Direction", "trader_lotSize": "Lot size", "trader_notionalValue": "Notional value:", "trader_pipValue": "PIP Value", "trader_leverage": "Leverage", "trader_midPrice1DayChange": "Mid Price / 1D Change", "trader_viewChart": "View Chart", "trader_modifiyTrade": "Modify Trade", "trader_partialClose": "Partial Close", "trader_quickClose": "Quick Close", "trader_insights": "Insights", "trader_orders": "Orders", "trader_alerts": "<PERSON><PERSON><PERSON>", "trader_noInsightsYet": "No Insights Yet", "trader_noTradesYetDescription": "You don’t have any trades yet.", "trader_noOrders": "No Orders", "trader_noActiveOrdersDescription": "You have no active orders at the moment.", "trader_noAlerts": "No Alerts", "trader_noActiveAlertsDescription": "You don’t have any active alerts.", "trader_positions": "Positions", "trader_trades": "trades", "trader_portfolio_trades": "Trades", "trader_search": "Search", "trader_contactSupportTeam": "Contact Support team", "trader_contactSupportDescription": "It seems we are facing some problems right now, you can raise a support ticket with our teams so they can help you resolve your issue.", "trader_raiseSupportTicket": "Raise Support Ticket", "trader_tradePartiallyClosed": "Trade partially closed", "trader_tradeNotClosed": "Trade not closed", "trader_ordersDetails": "Orders Details", "trader_orderAt": "Order at", "trader_errorDescription": "Sorry we can’t load this information at the moment something seems to have gone wrong.\nPlease try to reload the page, if the problem continues you can raise a support ticket and our team will get back to you.", "trader_raiseTicket": "Raise Support Ticket", "trader_noInsightsDescription": "You haven’t made any trades yet so we have no data", "trader_profit": "Profit", "trader_closing": "Closing", "trader_tradeClosed": "Trade Closed", "trader_tradePlaced": "trade placed", "trader_viewAllTrades": "View all trades", "trader_viewMyPortfolio": "View my portfolio", "trader_orderPlaced": "order placed", "trader_tradingPreferences": "Trading Preferences", "@trader_tradingPreferences": {}, "trader_interfaceCustomisation": "Interface Customisation", "@trader_interfaceCustomisation": {}, "trader_securitySettings": "Security Settings", "@trader_securitySettings": {}, "trader_generalSettings": "General Settings", "@trader_generalSettings": {}, "trader_smartLogin": "<PERSON>gin", "@trader_smartLogin": {}, "trader_enableBiometricLogin": "Enable biometric login", "@trader_enableBiometricLogin": {}, "trader_biometricLoginInfo": "Some information", "@trader_biometricLoginInfo": {}, "trader_accountSecurity": "Account Security", "@trader_accountSecurity": {}, "trader_privacySettings": "Privacy Settings", "@trader_privacySettings": {}, "trader_notificationSettings": "Notification Settings", "@trader_notificationSettings": {}, "trader_customerSupport": "Customer Support", "@trader_customerSupport": {}, "trader_legalDocuments": "Legal Documents", "@trader_legalDocuments": {}, "trader_accountActions": "Account Actions", "@trader_accountActions": {}, "trader_logout": "Logout", "@trader_logout": {}, "trader_changeLeverage": "Change Leverage", "@trader_changeLeverage": {}, "trader_changeAccountLeverage": "Change Account Lev<PERSON>ge", "@trader_changeAccountLeverage": {}, "@trader_quickTrade": {}, "trader_rememberDealSize": "Remember deal size", "@trader_rememberDealSize": {}, "trader_rememberStopsAndLimits": "Remember stops and limits", "@trader_rememberStopsAndLimits": {}, "trader_tradingAccountSettings": "Trading Account <PERSON><PERSON>s", "@trader_tradingAccountSettings": {}, "trader_viewAllOrders": "View all orders", "trader_tradeModificationSaved": "Trade Modification Saved", "trader_countryPickerTitle": "What country do you live in?", "trader_countryPickerBody": "The terms and services applicable to you will depend on your selected country of residence.", "trader_countryOfResidence": "Country of residence", "trader_countryPickerHelper": "We’ve pre-filled your country based on your location. If this is incorrect, please select your country manually.", "trader_countryPickerCheckBoxTitle": "I am not a citizen of the United States", "trader_countryPickerCheckBoxBody": "For regulatory reasons, we do not accept citizens or residents of the United States", "trader_confirm": "Confirm", "trader_selectCountry": "Select Country", "trader_selectCity": "Choose your city or Emirate", "trader_cityPickerTitle": "What city or Emirate do you live in?", "trader_cityPickerBody": "We ask for your city or Emirate to personalise your experience and provide the best service based on your location.", "trader_cityEmirate": "City/Emirate", "trader_quickCloseLoading": "We are closing your trade and you will be notified when it is complete.", "trader_transfer": "Transfer", "@trader_transfer": {}, "trader_deposit": "<PERSON><PERSON><PERSON><PERSON>", "@trader_deposit": {}, "trader_darkTheme": "Dark Theme", "@trader_darkTheme": {}, "trader_hapticFeedback": "Haptic <PERSON>", "@trader_hapticFeedback": {}, "trader_accountNickName": "[Account nickname]", "@trader_accountNickName": {}, "trader_language": "language", "@trader_language": {}, "trader_positionDetails": "Position Details", "@trader_positionDetails": {}, "trader_profitMargin": "Profit / Margin", "@trader_profitMargin": {}, "trader_noOpenPositions": "No Open Positions", "@trader_noOpenPositions": {}, "trader_noOpenPositionsDescription": "You have no open positions at the moment", "@trader_noOpenPositionsDescription": {}, "trader_marketDetails": "Market Details", "@trader_marketDetails": {}, "trader_minSize": "Min size:", "@trader_minSize": {}, "trader_maxSize": "Max size:", "@trader_maxSize": {}, "trader_enterAnumber": "Enter a number", "@trader_enterAnumber": {}, "trader_pleaseSpecifyTheLotSize": "Please specify a valid lot size to execute the trade", "@trader_pleaseSpecifyTheLotSize": {}, "trader_marginRequirement": "Margin requirement:", "@trader_marginRequirement": {}, "@trader_notionalValue": {}, "trader_pointValue": "Point value:", "@trader_pointValue": {}, "trader_price": "Price", "@trader_price": {}, "trader_loss": "Loss", "@trader_loss": {}, "trader_enterDistance": "Enter distance", "@trader_enterDistance": {}, "trader_enterProfit": "Enter profit", "trader_enterLoss": "Enter loss", "trader_invalidInput": "Invalid input", "@trader_invalidInput": {}, "trader_invalidPrice": "Invalid price", "@trader_invalidPrice": {}, "trader_invalidDistance": "Invalid distance", "@trader_invalidDistance": {}, "trader_invalidProfitLoss": "Invalid profit/loss", "@trader_invalidProfitLoss": {}, "trader_profitTitle": " Profit:", "@trader_profitTitle": {}, "trader_lossTitle": "Loss:", "@trader_lossTitle": {}, "trader_distanceTitle": "Distance:", "@trader_distanceTitle": {}, "trader_priceTitle": "Price:", "@trader_priceTitle": {}, "trader_marketOrder": "Trade", "@trader_marketOrder": {}, "trader_pendingOrder": "Order", "@trader_pendingOrder": {}, "trader_order": "Order", "@trader_order": {}, "trader_alert": "<PERSON><PERSON>", "@trader_alert": {}, "trader_saving": "Saving", "@trader_saving": {}, "trader_takeProfitMessage": "Automatically closes your trade when the price reaches your target, securing your profits.", "@trader_takeProfitMessage": {}, "trader_stopLossMessage": "Automatically closes your trade if the price moves against you, limiting potential losses.", "@trader_stopLossMessage": {}, "trader_orderPrice": "Order price", "@trader_orderPrice": {}, "trader_orderPriceDescription": "The price at which you choose to buy or sell an asset, your pending order will be executed when the market reaches your set price.", "@trader_orderPriceDescription": {}, "trader_validPriceMessage": "Please specify a valid price to execute the trade", "@trader_validPriceMessage": {}, "trader_save": "Save", "@trader_save": {}, "trader_raiseATicket": " Raise a ticket", "@trader_raiseATicket": {}, "trader_countryLoadingMessage": "We couldn't load the countries. Please try again later.'", "@trader_countryLoadingMessage": {}, "trader_tryAgain": "Try Again", "@trader_tryAgain": {}, "trader_pleaseSelectCountry": "Please select a country", "@trader_pleaseSelectCountry": {}, "trader_countryNotSelected": "Country not selected", "@trader_countryNotSelected": {}, "trader_buyLimit": "Buy Limit", "@trader_buyLimit": {}, "trader_sellLimit": "<PERSON><PERSON>", "@trader_sellLimit": {}, "trader_buyStop": "Buy Stop", "@trader_buyStop": {}, "trader_sellStop": "Sell Stop", "@trader_sellStop": {}, "trader_modifyOrder": "Modify Order", "@trader_modifyOrder": {}, "trader_saveOrder": "Save Order", "@trader_saveOrder": {}, "trader_delete": "Delete", "@trader_delete": {}, "trader_orderModified": "Order Modified", "@trader_orderModified": {}, "trader_orderDeleted": "Order Deleted", "@trader_orderDeleted": {}, "trader_deleteOrderFailed": "Delete order failed", "@trader_deleteOrderFailed": {}, "trader_openPriceTitle": "Open price:", "@trader_openPriceTitle": {}, "trader_currentPriceTitle": "Current price:", "@trader_currentPriceTitle": {}, "trader_takeProfitTitle": "Take profit:", "@trader_takeProfitTitle": {}, "trader_stopLossTitle": "Stop loss:", "@trader_stopLossTitle": {}, "trader_deleting": "Deleting", "@trader_deleting": {}, "trader_orderDetails": "Order Details", "@trader_orderDetails": {}, "trader_noLegalDocumentsFound": "No legal documents found!", "@trader_noLegalDocumentsFound": {}, "trader_pleaseWait": "Please wait....", "@trader_pleaseWait": {}, "trader_select": "Select", "@trader_select": {}, "trader_someInformation": "Some information", "@trader_someInformation": {}, "trader_marketName": "Market name", "@trader_marketName": {}, "trader_midPriceValue": "Mid Price Value", "@trader_midPriceValue": {}, "trader_onedChange": "1D Change%", "@trader_onedChange": {}, "trader_noOpenTrades": "No open trades", "@trader_noOpenTrades": {}, "trader_noOpenTradesMessage": "You have no open trades at the moment", "@trader_noOpenTradesMessage": {}, "trader_closePositionBy": "Close position by", "@trader_closePositionBy": {}, "trader_allBuyTrades": "All Buy Trades", "@trader_allBuyTrades": {}, "trader_allSellTrades": "All Sell Trades", "@trader_allSellTrades": {}, "trader_allTrades": "All Trades", "@trader_allTrades": {}, "trader_allLosingTrades": "All Losing Trades", "@trader_allLosingTrades": {}, "trader_allWinningTrades": "All Winning Trades", "@trader_allWinningTrades": {}, "trader_automaticallyCloseTradesMessage": "Don’t show this again, automatically close all trades without confirmation from now on.", "@trader_automaticallyCloseTradesMessage": {}, "trader_lots": "Lots", "@trader_lots": {}, "trader_hedged": "Hedged", "@trader_hedged": {}, "trader_alertAt": "<PERSON><PERSON>", "@trader_alertAt": {}, "trader_modifyAlert": "Mo<PERSON><PERSON>", "@trader_modifyAlert": {}, "trader_alertHasBeenDeleted": "<PERSON><PERSON> has beed deleted", "@trader_alertHasBeenDeleted": {}, "trader_alertHasBeenModified": "Alert has been modified", "@trader_alertHasBeenModified": {}, "trader_alertNotDeleted": "<PERSON><PERSON> not deleted", "@trader_alertNotDeleted": {}, "trader_alertNotSaved": "<PERSON><PERSON> not saved", "@trader_alertNotSaved": {}, "trader_unableToDeleteAlertMessage": "Sorry! We are unable to delete this alert. Please try sometime later", "@trader_unableToDeleteAlertMessage": {}, "trader_unableToModifyAlertMessage": "Sorry! We are unable to modify this alert. Please try sometime later", "@trader_unableToModifyAlertMessage": {}, "trader_alertSaved": "<PERSON><PERSON>", "@trader_alertSaved": {}, "trader_quickTradeMessage": "Quick trade is active, tapping buy or sell will place a trade at the market price right away. Are you sure you want to place an active trade now?", "@trader_quickTradeMessage": {}, "trader_yesDontShowAgain": "Yes, <PERSON><PERSON> show again", "@trader_yesDontShowAgain": {}, "trader_noCancelTrade": "No, cancel trade", "@trader_noCancelTrade": {}, "trader_viewPortfolio": "View Portfolio", "@trader_viewPortfolio": {}, "trader_cannotPlaceOrder": "Cannot Place Order", "@trader_cannotPlaceOrder": {}, "trader_amountShouldBeHint": "Should be between 250 and 50,000.", "@trader_amountShouldBeHint": {}, "trader_specifyAmount": "Specify Amount", "@trader_specifyAmount": {}, "trader_invalidAmount": "<PERSON><PERSON><PERSON>", "@trader_invalidAmount": {}, "trader_ok": "OK", "@trader_ok": {}, "trader_unableToResetBalance": "Unable to reset Balance", "@trader_unableToResetBalance": {}, "trader_resetBalanceSuccessful": "Reset Balance is successful", "@trader_resetBalanceSuccessful": {}, "trader_resetBalanceTitle": "Reset balance", "@trader_resetBalanceTitle": {}, "trader_apply": "Apply", "@trader_apply": {}, "trader_selectView": "Select View", "@trader_selectView": {}, "trader_trade": "Trade", "@trader_trade": {}, "trader_transferFunds": "Transfer Funds", "@trader_transferFunds": {}, "trader_sourceAccount": "Source Account", "@trader_sourceAccount": {}, "trader_destinationAccount": "Destination Account", "@trader_destinationAccount": {}, "trader_amount": "Amount", "@trader_amount": {}, "trader_rateTitle": "rate:", "@trader_rateTitle": {}, "trader_amountTitle": "amount:", "@trader_amountTitle": {}, "trader_transactionSuccessful": "transaction Successful", "@trader_transactionSuccessful": {}, "trader_transactionFailed": "Transaction failed", "@trader_transactionFailed": {}, "trader_chooseWithdrawalMethod": "Choose a withdrawal method", "@trader_chooseWithdrawalMethod": {}, "trader_marginHealth": "Margin Health", "trader_marginAllocation": "Margin allocation:", "trader_marketsAllocationDescriptionFirstText": "Markets your active margin is allocated to", "trader_marketsAllocationDescriptionSecondText": "for open trades.", "trader_value": "Value", "trader_used": "used", "trader_insightsSomethingWentWrong": "Something Went Wrong", "trader_insightsErrorDescription": "Sorry we can’t load this information at the moment something seems to have gone wrong.", "trader_noActive": "No active", "trader_marginCall": "<PERSON><PERSON>", "trader_stop": "Stop", "trader_transferFundsTitle": "Transfer funds", "@trader_transferFundsTitle": {}, "trader_trading": "Trading", "trader_funding": "Funding", "trader_statements": "Statements", "trader_selectChartTime": "Select a chart time", "trader_loadingChart": "Loading Chart...", "trader_fiveMin": "5 Minutes", "trader_thirtyMin": "30 Minutes", "trader_oneHour": "1 Hour", "trader_fourHour": "4 Hours", "trader_oneDay": "1 Day", "trader_oneWeek": "1 Week", "trader_oneMonth": "1 Month", "trader_oneYear": "1 Year", "trader_oneMin": "1 Minutes", "trader_noStatements": "No Statements", "trader_noStatementsDescription": "You have no funding activity or started trading, once you have activity this will be presented in your statements", "trader_failedToLoad": "Failed to load", "trader_failedToLoadDescription": "We apologies it seems some of the content failed to load at this point, you can still view available content until this is resolved or try to reload.", "trader_statement": "Statement", "trader_daily": "Daily", "trader_searchEvents": "Search Events", "trader_searchNews": "Search News", "trader_today": "Today", "trader_yesterday": "Yesterday", "trader_tomorrow": "Tomorrow", "trader_corporate": "Corporate", "trader_economic": "Economic", "trader_all": "All", "trader_newestToOldest": "Newest to oldest", "trader_oldestToNewest": "Oldest to newest", "trader_sortNewsBy": "Sort News by", "trader_sortEventsBy": "Sort Events by", "trader_noResults": "No results", "trader_noResultsDescription": "Sorry we didn’t find anything matching your search", "trader_somethingWentWrongDescription": "Unable to perform a search at this time please try again.", "trader_permissionDenied": "Permission Denied", "trader_noCalendarFound": "No Calendar Found", "trader_eventAddedToCalendar": "Event added to your calendar.", "trader_failedToAddEvent": "Unable to add event to calendar. Please try again.", "trader_previous": "Previous", "trader_forecast": "Forecast", "trader_actual": "Actual", "trader_affectedMarkets": "Affected Markets", "trader_sortStatementsBy": "Sort statements by", "trader_sortStatements": "Sort statements", "trader_filterStatementsBy": "Filter statements by", "trader_selectCustomDatesDesc": "Select custom dates, just add the from and to dates below to filter and see your statements between these dates.", "trader_from": "From", "trader_to": "To", "trader_clearAll": "Clear all", "trader_applyFilters": "Apply filters", "trader_watchlist": "Watchlist", "trader_emptyWatchlist": "No Markets added yet", "trader_emptyWatchlistDescription": "You don’t have any markets added to your watchlist.", "trader_noTrading": "No trading", "trader_noTradingDescription": "You have not made any trades yet, once you have activity this will show here", "trader_sortFundingBy": "Sort funding by", "trader_sortFunding": "Sort funding", "trader_filterFundingBy": "Filter funding by", "trader_noFunding": "No funding", "trader_noFundingDescription": "You have no funding activity once you have activity this will show here", "trader_fundingDetails": "Funding details", "trader_fundFlow": "Fund flow", "trader_status": "Status", "trader_TransactionType": "Transaction type", "trader_details": "Details", "trader_date": "Date", "trader_time": "Time", "trader_account": "Account", "trader_paymentMethod": "Payment Method", "trader_needHelpWithThis": "Need help with this?", "trader_contactUs": "Contact us", "trader_marginAllocationNotValid": "Not enough margin to make this trade.", "trader_lessThan": "less than", "trader_moreThan": "more than", "trader_tpPriceValidation": "Take profit price is {condition} current price.", "trader_slPriceValidation": "Stop loss price is {condition} current price.", "trader_tpOrderPriceValidation": "Take profit price is {condition} order price.", "trader_slOrderPriceValidation": "Stop loss price is {condition} order price.", "trader_marketIsClosed": "Market is closed", "trader_closeTrade_marketIsClosedDescription": "The market is closed right now. You'll be able to close your position when it reopens.", "trader_openTrade_marketIsClosedDescription": "The market is closed right now. You'll be able to create your position when it reopens.", "trader_modifyTrade_marketIsClosedDescription": "The market is closed right now. You'll be able to modify your position when it reopens.", "trader_modifyPendingOrder_marketIsClosedDescription": "The market is closed right now. You'll be able to modify your pending order when it reopens.", "trader_createPendingOrder_marketIsClosedDescription": "The market is closed right now. You'll be able to place your pending order when it reopens.", "trader_closeAllTrades_marketIsClosedDescription": "The market is closed right now. You'll be able to close all your trades when it reopens.", "trader_banner_take_the_win_podcast_url": "https://www.youtube.com/playlist?list=PLjxX-Ou-3c0Py9RLbngOfg5fRtXpCzcMA", "@trader_banner_take_the_win_podcast_url": {}, "trader_banner_trade_without_swap_charges_url": "https://www.equiti.com/uae-en/accounts/swap-free-mini-account/", "@trader_banner_trade_without_swap_charges_url": {}, "trader_risk_management_url": "https://www.equiti.com/uae-en/education/risk-management/", "@trader_risk_management_url": {}, "trader_market_analysis_url": "https://www.equiti.com/uae-en/education/market-analysis/", "@trader_market_analysis_url": {}, "trader_popular_stocks_url": "https://www.equiti.com/uae-en/education/risk-management/", "@trader_popular_stocks_url": {}, "trader_trading_101_url": "https://www.equiti.com/uae-en/education/trading-101/", "@trader_trading_101_url": {}, "trader_trading_ideas_url": "https://www.equiti.com/uae-en/news/trading-ideas/", "@trader_trading_ideas_url": {}, "trader_market_insights_url": "https://www.equiti.com/uae-en/news/market-insights/", "@trader_market_insights_url": {}, "trader_breaking_data_url": "https://www.equiti.com/uae-en/news/breaking-data/", "@trader_breaking_data_url": {}, "trader_market_news_url": "https://www.equiti.com/uae-en/news/market-news/", "@trader_market_news_url": {}, "trader_market_minutes_url": "https://www.equiti.com/uae-en/news/market-news/", "@trader_market_minutes_url": {}, "trader_balance": "Balance", "trader_credit": "Credit", "trader_margin": "<PERSON><PERSON>", "trader_accountFreeMargin": "Free Margin", "trader_accountUsedMargin": "Used Margin", "trader_accountMarginLevel": "Margin Level", "trader_platform": "Platform", "trader_accountType": "Account Type", "trader_server": "Server", "trader_accountID": "Account ID", "trader_currency": "<PERSON><PERSON><PERSON><PERSON>", "trader_swapProfile": "Swap Profile", "trader_accountDetails": "Account Details", "trader_settings": "Settings", "trader_free": "Free", "trader_standard": "Standard", "trader_howToConnect": "How to connect to MetaTrader", "trader_howToConnectDescription": "Copy the server name and MT login, then enter your password.", "trader_copiedTOClipBoard": "Copied to clipboard!", "trader_appStore": "on App Store", "trader_googlePlay": "on Google Play", "trader_walletDetails": "Wallet Details", "trader_walletBalance": "Wallet Balance", "trader_walletId": "Wallet ID:", "trader_withdraw": "Withdraw", "trader_fundingActivity": "Funding activity", "trader_noTransactionsYet": "No transactions yet", "trader_walletActivityDescription": "Your wallet activity will appear here once you start depositing, withdrawing, or transferring funds.", "trader_unableToLoadTransactions": "Unable to load transactions", "trader_transactionLoadError": "Something went wrong while fetching your funding activity.", "trader_reloadScreen": "Reload screen", "trader_sortTradesBy": "Sort trades by", "trader_sortTrades": "Sort trades", "trader_filterTradesBy": "Filter closed trades by", "trader_selectCustomDatesTradingDesc": "Select custom dates, just add the from and to dates below to filter and see your closed trades between these dates.", "trader_tradeType": "Trade type", "trader_closedTradeDetails": "Closed trade details", "trader_tradeClosedOn": "Trade closed on:", "trader_swap": "<PERSON><PERSON><PERSON>", "trader_closePriceTitle": "Close price:", "trader_turnOffQuickTrade": "Turn quick trade off", "trader_performanceTitle": "Look Back. Move forward", "trader_performancesSubTitle": "Performance history, as of yesterday", "trader_equity": "Equity", "trader_sixMonths": "For the last 6 months", "trader_realisedProfit": "Realised profit", "trader_volume": "Volume", "trader_moneyIn": "Money In", "trader_moneyOut": "Money Out", "trader_accepted": "Accepted", "trader_pending": "Pending", "trader_rejected": "Rejected", "trader_bonus": "Bonus", "trader_dividend": "Dividend", "trader_campaign": "Campaign", "trader_type": "Type", "trader_lotsizeInfo": "Choose the trade size you want to open. Larger lot sizes increase both your potential profit and your risk. They also require a higher margin to open the trade.", "trader_notifications": "Notifications", "trader_noNotifications": "No notifications yet", "trader_noNotificationsDesc": "We'll let you know when updates arrive!", "trader_closedAllBuyTrades": "Closed all buy trades", "trader_closedAllSellTrades": "Closed all sell trades", "trader_closedAllLosingTrades": "Closed all losing trades", "trader_closedAllWinningTrades": "Closed all winning trades", "trader_closedAllTrades": "Closed all trades", "trader_changeAccountPasswordTitle": "Change Account Password", "trader_changeAccountPasswordDescription": "Enter the password you use to login to the app to validate a changing the trading account password.", "trader_changeAccountPasswordWarning": "Your password is private to you. <PERSON><PERSON><PERSON> will never ask for your password at any point.", "trader_platformPassword": "Platform Password", "trader_setNewPassword": "Set New Password", "trader_currentPassword": "Current Password", "trader_confirmPassword": "Confirm Password", "trader_passwordChangedSuccessfully": "Password changed successfully", "trader_incorrectPlatformPassword": "Platform password is incorrect", "trader_passwordDoesNotMatch": "Password does not match", "trader_continue": "Continue", "@trader_continue": {}, "trader_mt4_app_store_url": "https://apps.apple.com/ae/app/metatrader-4/id496212596", "@trader_mt4_app_store_url": {}, "trader_mt5_app_store_url": "https://apps.apple.com/ae/app/metatrader-5/id413251709", "@trader_mt5_app_store_url": {}, "trader_mt4_play_store_url": "https://play.google.com/store/apps/details?id=net.metaquotes.metatrader4&hl=en", "@trader_mt4_play_store_url": {}, "trader_mt5_play_store_url": "https://play.google.com/store/apps/details?id=net.metaquotes.metatrader5&hl=en", "@trader_mt5_play_store_url": {}, "trader_mt5_web_terminal_url": "https://web.metatrader.app/terminal", "@trader_mt5_web_terminal_url": {}, "trader_orderloss": "Loss", "trader_orderprofit": "Profit", "trader_buyOrderModified": "Buy Order Modified", "trader_sellOrderModified": "Sell Order Modified", "trader_newNickname": "New Nickname", "@trader_newNickname": {}, "trader_newNicknameHint": "This is my new Nickname", "@trader_newNicknameHint": {}, "trader_renameYourAccount": "Rename your account", "@trader_renameYourAccount": {}, "trader_renameAccount": "Rename account", "@trader_renameAccount": {}, "trader_accountRenamedSuccessfully": "Account renamed successfully", "@trader_accountRenamedSuccessfully": {}, "trader_metaTraderServerDetails": "MetaTrader server details", "@trader_metaTraderServerDetails": {}, "trader_accountInformation": "Account information", "@trader_accountInformation": {}, "trader_resetBalance": "Reset balance", "@trader_resetBalance": {}, "trader_cfdTradingAccounts": "CFD Trading Accounts", "@trader_cfdTradingAccounts": {}, "trader_demoTradingAccounts": "Demo Trading Accounts", "@trader_demoTradingAccounts": {}, "trader_switchAccountDescription": "Select an account to view performance, manage funds, or monitor margin health", "@trader_switchAccountDescription": {}, "trader_noWalletsFound": "No Wallets Found", "@trader_noWalletsFound": {}, "trader_createWalletDescription": "Create a wallet to start depositing funds.", "@trader_createWalletDescription": {}, "trader_noAccountsMessage": "No {environment} accounts", "@trader_noAccountsMessage": {}, "trader_noLiveAccountsDescription": "You don't have any live accounts yet. Open a live account to start trading with real funds.", "@trader_noLiveAccountsDescription": {}, "trader_totalEquity": "Total Equity", "@trader_totalEquity": {}, "trader_accountEquity": "Account Equity", "@trader_accountEquity": {}, "trader_totalBalance": "Total Balance", "@trader_totalBalance": {}, "trader_totalCredit": "Total Credit", "@trader_totalCredit": {}, "trader_totalProfit": "Total Profit", "@trader_totalProfit": {}, "trader_accounts": "Accounts", "@trader_accounts": {}, "trader_wallets": "Wallets", "@trader_wallets": {}, "trader_issueClosingTrades": "Issue closing trades", "@trader_issueClosingTrades": {}, "trader_close_title": "Close", "@trader_close_title": {}, "trader_datePlaceholder": "DD - MM - YYYY", "trader_funding_details": "details", "trader_selectCustomDatesFundingDesc": "Select custom dates, just add the from and to dates below to filter and see your fundings between these dates.", "trader_close_placeholder_price": "Price", "trader_unable_to_change_leverage": "Sorry! We are unable to change your leverage", "trader_open_trades": "Open Trades", "trader_account_currency_type": "Account <PERSON><PERSON><PERSON>cy Type", "trader_winners": "Winners", "trader_losers": "Losers", "trader_buys": "Buys", "trader_sells": "Sells"}