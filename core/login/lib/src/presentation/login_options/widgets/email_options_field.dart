import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:login/src/assets/assets.gen.dart' as trader;
import 'package:login/src/presentation/login_options/bloc/login_options_bloc.dart';

class EmailOptionsField extends StatelessWidget {
  const EmailOptionsField({super.key, required this.emailController});
  final TextEditingController emailController;
  @override
  Widget build(BuildContext context) {
    final localization = EquitiLocalization.of(context);
    return DuploTextField(
      semanticsIdentifier: "login_options_email_field",
      prefixIcon: trader.Assets.images.emailIc.svg(),
      controller: emailController,
      hint: localization.login_emailAddress,
      label: localization.login_emailAddress,
      onEditingComplete: () {
        FocusManager.instance.primaryFocus?.unfocus();
      },
      onChanged: (value) {
        context.read<LoginOptionsBloc>().add(
          LoginOptionsEvent.validateEmail(value),
        );
      },
    );
  }
}
