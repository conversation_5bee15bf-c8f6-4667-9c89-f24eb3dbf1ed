import 'package:domain/domain.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:onboarding/src/domain/enums/account_creation_enums.dart';

part 'account_creation_request_model.freezed.dart';
part 'account_creation_request_model.g.dart';

@freezed
sealed class AccountCreationRequestModel with _$AccountCreationRequestModel {
  const factory AccountCreationRequestModel({
    @JsonKey(name: "platform") required AccountCreationPlatform platform,
    @JsonKey(name: "platform_account_type")
    required AccountCreationType platformAccountType,
    @<PERSON>son<PERSON><PERSON>(name: "currency") required String currency,
    @<PERSON><PERSON><PERSON><PERSON>(name: "swap_free_account") bool? swapFreeAccount,
    @<PERSON><PERSON><PERSON><PERSON>(name: "leverage") String? leverage,
    @Json<PERSON>ey(name: "account_nickname") required String accountNickname,
    @<PERSON>son<PERSON>ey(name: "account_password") required String password,
    @<PERSON><PERSON><PERSON><PERSON>(name: "account_type", includeIfNull: false) String? accountType,
    @<PERSON><PERSON><PERSON><PERSON>(name: "is_initial_account", includeIfNull: false)
    bool? isInitialAccount,
    @<PERSON><PERSON><PERSON><PERSON>(name: "reason", includeIfNull: false) String? reason,
    @<PERSON>son<PERSON>ey(name: "reason_details", includeIfNull: false)
    String? reasonDetails,
  }) = _AccountCreationRequestModel;

  factory AccountCreationRequestModel.fromJson(Map<String, dynamic> json) =>
      _$AccountCreationRequestModelFromJson(json);
}
