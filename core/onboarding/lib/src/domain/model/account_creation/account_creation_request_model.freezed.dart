// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'account_creation_request_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$AccountCreationRequestModel {

@JsonKey(name: "platform") AccountCreationPlatform get platform;@JsonKey(name: "platform_account_type") AccountCreationType get platformAccountType;@JsonKey(name: "currency") String get currency;@JsonKey(name: "swap_free_account") bool? get swapFreeAccount;@JsonKey(name: "leverage") String? get leverage;@JsonKey(name: "account_nickname") String get accountNickname;@JsonKey(name: "account_password") String get password;@JsonKey(name: "account_type", includeIfNull: false) String? get accountType;@JsonKey(name: "is_initial_account", includeIfNull: false) bool? get isInitialAccount;@JsonKey(name: "reason", includeIfNull: false) String? get reason;@JsonKey(name: "reason_details", includeIfNull: false) String? get reasonDetails;
/// Create a copy of AccountCreationRequestModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AccountCreationRequestModelCopyWith<AccountCreationRequestModel> get copyWith => _$AccountCreationRequestModelCopyWithImpl<AccountCreationRequestModel>(this as AccountCreationRequestModel, _$identity);

  /// Serializes this AccountCreationRequestModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AccountCreationRequestModel&&(identical(other.platform, platform) || other.platform == platform)&&(identical(other.platformAccountType, platformAccountType) || other.platformAccountType == platformAccountType)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.swapFreeAccount, swapFreeAccount) || other.swapFreeAccount == swapFreeAccount)&&(identical(other.leverage, leverage) || other.leverage == leverage)&&(identical(other.accountNickname, accountNickname) || other.accountNickname == accountNickname)&&(identical(other.password, password) || other.password == password)&&(identical(other.accountType, accountType) || other.accountType == accountType)&&(identical(other.isInitialAccount, isInitialAccount) || other.isInitialAccount == isInitialAccount)&&(identical(other.reason, reason) || other.reason == reason)&&(identical(other.reasonDetails, reasonDetails) || other.reasonDetails == reasonDetails));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,platform,platformAccountType,currency,swapFreeAccount,leverage,accountNickname,password,accountType,isInitialAccount,reason,reasonDetails);

@override
String toString() {
  return 'AccountCreationRequestModel(platform: $platform, platformAccountType: $platformAccountType, currency: $currency, swapFreeAccount: $swapFreeAccount, leverage: $leverage, accountNickname: $accountNickname, password: $password, accountType: $accountType, isInitialAccount: $isInitialAccount, reason: $reason, reasonDetails: $reasonDetails)';
}


}

/// @nodoc
abstract mixin class $AccountCreationRequestModelCopyWith<$Res>  {
  factory $AccountCreationRequestModelCopyWith(AccountCreationRequestModel value, $Res Function(AccountCreationRequestModel) _then) = _$AccountCreationRequestModelCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: "platform") AccountCreationPlatform platform,@JsonKey(name: "platform_account_type") AccountCreationType platformAccountType,@JsonKey(name: "currency") String currency,@JsonKey(name: "swap_free_account") bool? swapFreeAccount,@JsonKey(name: "leverage") String? leverage,@JsonKey(name: "account_nickname") String accountNickname,@JsonKey(name: "account_password") String password,@JsonKey(name: "account_type", includeIfNull: false) String? accountType,@JsonKey(name: "is_initial_account", includeIfNull: false) bool? isInitialAccount,@JsonKey(name: "reason", includeIfNull: false) String? reason,@JsonKey(name: "reason_details", includeIfNull: false) String? reasonDetails
});




}
/// @nodoc
class _$AccountCreationRequestModelCopyWithImpl<$Res>
    implements $AccountCreationRequestModelCopyWith<$Res> {
  _$AccountCreationRequestModelCopyWithImpl(this._self, this._then);

  final AccountCreationRequestModel _self;
  final $Res Function(AccountCreationRequestModel) _then;

/// Create a copy of AccountCreationRequestModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? platform = null,Object? platformAccountType = null,Object? currency = null,Object? swapFreeAccount = freezed,Object? leverage = freezed,Object? accountNickname = null,Object? password = null,Object? accountType = freezed,Object? isInitialAccount = freezed,Object? reason = freezed,Object? reasonDetails = freezed,}) {
  return _then(_self.copyWith(
platform: null == platform ? _self.platform : platform // ignore: cast_nullable_to_non_nullable
as AccountCreationPlatform,platformAccountType: null == platformAccountType ? _self.platformAccountType : platformAccountType // ignore: cast_nullable_to_non_nullable
as AccountCreationType,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,swapFreeAccount: freezed == swapFreeAccount ? _self.swapFreeAccount : swapFreeAccount // ignore: cast_nullable_to_non_nullable
as bool?,leverage: freezed == leverage ? _self.leverage : leverage // ignore: cast_nullable_to_non_nullable
as String?,accountNickname: null == accountNickname ? _self.accountNickname : accountNickname // ignore: cast_nullable_to_non_nullable
as String,password: null == password ? _self.password : password // ignore: cast_nullable_to_non_nullable
as String,accountType: freezed == accountType ? _self.accountType : accountType // ignore: cast_nullable_to_non_nullable
as String?,isInitialAccount: freezed == isInitialAccount ? _self.isInitialAccount : isInitialAccount // ignore: cast_nullable_to_non_nullable
as bool?,reason: freezed == reason ? _self.reason : reason // ignore: cast_nullable_to_non_nullable
as String?,reasonDetails: freezed == reasonDetails ? _self.reasonDetails : reasonDetails // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _AccountCreationRequestModel implements AccountCreationRequestModel {
  const _AccountCreationRequestModel({@JsonKey(name: "platform") required this.platform, @JsonKey(name: "platform_account_type") required this.platformAccountType, @JsonKey(name: "currency") required this.currency, @JsonKey(name: "swap_free_account") this.swapFreeAccount, @JsonKey(name: "leverage") this.leverage, @JsonKey(name: "account_nickname") required this.accountNickname, @JsonKey(name: "account_password") required this.password, @JsonKey(name: "account_type", includeIfNull: false) this.accountType, @JsonKey(name: "is_initial_account", includeIfNull: false) this.isInitialAccount, @JsonKey(name: "reason", includeIfNull: false) this.reason, @JsonKey(name: "reason_details", includeIfNull: false) this.reasonDetails});
  factory _AccountCreationRequestModel.fromJson(Map<String, dynamic> json) => _$AccountCreationRequestModelFromJson(json);

@override@JsonKey(name: "platform") final  AccountCreationPlatform platform;
@override@JsonKey(name: "platform_account_type") final  AccountCreationType platformAccountType;
@override@JsonKey(name: "currency") final  String currency;
@override@JsonKey(name: "swap_free_account") final  bool? swapFreeAccount;
@override@JsonKey(name: "leverage") final  String? leverage;
@override@JsonKey(name: "account_nickname") final  String accountNickname;
@override@JsonKey(name: "account_password") final  String password;
@override@JsonKey(name: "account_type", includeIfNull: false) final  String? accountType;
@override@JsonKey(name: "is_initial_account", includeIfNull: false) final  bool? isInitialAccount;
@override@JsonKey(name: "reason", includeIfNull: false) final  String? reason;
@override@JsonKey(name: "reason_details", includeIfNull: false) final  String? reasonDetails;

/// Create a copy of AccountCreationRequestModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AccountCreationRequestModelCopyWith<_AccountCreationRequestModel> get copyWith => __$AccountCreationRequestModelCopyWithImpl<_AccountCreationRequestModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$AccountCreationRequestModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AccountCreationRequestModel&&(identical(other.platform, platform) || other.platform == platform)&&(identical(other.platformAccountType, platformAccountType) || other.platformAccountType == platformAccountType)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.swapFreeAccount, swapFreeAccount) || other.swapFreeAccount == swapFreeAccount)&&(identical(other.leverage, leverage) || other.leverage == leverage)&&(identical(other.accountNickname, accountNickname) || other.accountNickname == accountNickname)&&(identical(other.password, password) || other.password == password)&&(identical(other.accountType, accountType) || other.accountType == accountType)&&(identical(other.isInitialAccount, isInitialAccount) || other.isInitialAccount == isInitialAccount)&&(identical(other.reason, reason) || other.reason == reason)&&(identical(other.reasonDetails, reasonDetails) || other.reasonDetails == reasonDetails));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,platform,platformAccountType,currency,swapFreeAccount,leverage,accountNickname,password,accountType,isInitialAccount,reason,reasonDetails);

@override
String toString() {
  return 'AccountCreationRequestModel(platform: $platform, platformAccountType: $platformAccountType, currency: $currency, swapFreeAccount: $swapFreeAccount, leverage: $leverage, accountNickname: $accountNickname, password: $password, accountType: $accountType, isInitialAccount: $isInitialAccount, reason: $reason, reasonDetails: $reasonDetails)';
}


}

/// @nodoc
abstract mixin class _$AccountCreationRequestModelCopyWith<$Res> implements $AccountCreationRequestModelCopyWith<$Res> {
  factory _$AccountCreationRequestModelCopyWith(_AccountCreationRequestModel value, $Res Function(_AccountCreationRequestModel) _then) = __$AccountCreationRequestModelCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: "platform") AccountCreationPlatform platform,@JsonKey(name: "platform_account_type") AccountCreationType platformAccountType,@JsonKey(name: "currency") String currency,@JsonKey(name: "swap_free_account") bool? swapFreeAccount,@JsonKey(name: "leverage") String? leverage,@JsonKey(name: "account_nickname") String accountNickname,@JsonKey(name: "account_password") String password,@JsonKey(name: "account_type", includeIfNull: false) String? accountType,@JsonKey(name: "is_initial_account", includeIfNull: false) bool? isInitialAccount,@JsonKey(name: "reason", includeIfNull: false) String? reason,@JsonKey(name: "reason_details", includeIfNull: false) String? reasonDetails
});




}
/// @nodoc
class __$AccountCreationRequestModelCopyWithImpl<$Res>
    implements _$AccountCreationRequestModelCopyWith<$Res> {
  __$AccountCreationRequestModelCopyWithImpl(this._self, this._then);

  final _AccountCreationRequestModel _self;
  final $Res Function(_AccountCreationRequestModel) _then;

/// Create a copy of AccountCreationRequestModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? platform = null,Object? platformAccountType = null,Object? currency = null,Object? swapFreeAccount = freezed,Object? leverage = freezed,Object? accountNickname = null,Object? password = null,Object? accountType = freezed,Object? isInitialAccount = freezed,Object? reason = freezed,Object? reasonDetails = freezed,}) {
  return _then(_AccountCreationRequestModel(
platform: null == platform ? _self.platform : platform // ignore: cast_nullable_to_non_nullable
as AccountCreationPlatform,platformAccountType: null == platformAccountType ? _self.platformAccountType : platformAccountType // ignore: cast_nullable_to_non_nullable
as AccountCreationType,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,swapFreeAccount: freezed == swapFreeAccount ? _self.swapFreeAccount : swapFreeAccount // ignore: cast_nullable_to_non_nullable
as bool?,leverage: freezed == leverage ? _self.leverage : leverage // ignore: cast_nullable_to_non_nullable
as String?,accountNickname: null == accountNickname ? _self.accountNickname : accountNickname // ignore: cast_nullable_to_non_nullable
as String,password: null == password ? _self.password : password // ignore: cast_nullable_to_non_nullable
as String,accountType: freezed == accountType ? _self.accountType : accountType // ignore: cast_nullable_to_non_nullable
as String?,isInitialAccount: freezed == isInitialAccount ? _self.isInitialAccount : isInitialAccount // ignore: cast_nullable_to_non_nullable
as bool?,reason: freezed == reason ? _self.reason : reason // ignore: cast_nullable_to_non_nullable
as String?,reasonDetails: freezed == reasonDetails ? _self.reasonDetails : reasonDetails // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
