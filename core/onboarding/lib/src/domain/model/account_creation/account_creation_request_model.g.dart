// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'account_creation_request_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_AccountCreationRequestModel _$AccountCreationRequestModelFromJson(
  Map<String, dynamic> json,
) => $checkedCreate(
  '_AccountCreationRequestModel',
  json,
  ($checkedConvert) {
    final val = _AccountCreationRequestModel(
      platform: $checkedConvert(
        'platform',
        (v) => $enumDecode(_$AccountCreationPlatformEnumMap, v),
      ),
      platformAccountType: $checkedConvert(
        'platform_account_type',
        (v) => $enumDecode(_$AccountCreationTypeEnumMap, v),
      ),
      currency: $checkedConvert('currency', (v) => v as String),
      swapFreeAccount: $checkedConvert('swap_free_account', (v) => v as bool?),
      leverage: $checkedConvert('leverage', (v) => v as String?),
      accountNickname: $checkedConvert('account_nickname', (v) => v as String),
      password: $checkedConvert('account_password', (v) => v as String),
      accountType: $checkedConvert('account_type', (v) => v as String?),
      isInitialAccount: $checkedConvert(
        'is_initial_account',
        (v) => v as bool?,
      ),
      reason: $checkedConvert('reason', (v) => v as String?),
      reasonDetails: $checkedConvert('reason_details', (v) => v as String?),
    );
    return val;
  },
  fieldKeyMap: const {
    'platformAccountType': 'platform_account_type',
    'swapFreeAccount': 'swap_free_account',
    'accountNickname': 'account_nickname',
    'password': 'account_password',
    'accountType': 'account_type',
    'isInitialAccount': 'is_initial_account',
    'reasonDetails': 'reason_details',
  },
);

Map<String, dynamic> _$AccountCreationRequestModelToJson(
  _AccountCreationRequestModel instance,
) => <String, dynamic>{
  'platform': _$AccountCreationPlatformEnumMap[instance.platform]!,
  'platform_account_type':
      _$AccountCreationTypeEnumMap[instance.platformAccountType]!,
  'currency': instance.currency,
  if (instance.swapFreeAccount case final value?) 'swap_free_account': value,
  if (instance.leverage case final value?) 'leverage': value,
  'account_nickname': instance.accountNickname,
  'account_password': instance.password,
  if (instance.accountType case final value?) 'account_type': value,
  if (instance.isInitialAccount case final value?) 'is_initial_account': value,
  if (instance.reason case final value?) 'reason': value,
  if (instance.reasonDetails case final value?) 'reason_details': value,
};

const _$AccountCreationPlatformEnumMap = {
  AccountCreationPlatform.dulcimer: 'dulcimer',
  AccountCreationPlatform.mt4: 'mt4',
  AccountCreationPlatform.mt5: 'mt5',
};

const _$AccountCreationTypeEnumMap = {
  AccountCreationType.standard: 'Standard',
  AccountCreationType.premier: 'Premier',
  AccountCreationType.crypto: 'Crypto',
};
