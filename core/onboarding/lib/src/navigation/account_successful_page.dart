import 'package:equiti_router/equiti_router.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:onboarding/src/navigation/arguments/account_successful_args.dart';
import 'package:onboarding/src/navigation/onboarding_route_schema.dart';
import 'package:onboarding/src/presentation/account_creation/account_successful_screen.dart';

class AccountSuccessfulPage extends EquitiPage {
  const AccountSuccessfulPage();
  @override
  Widget builder(
    BuildContext context,
    EquitiRoute routeData,
    Map<String, dynamic> args,
  ) {
    final arguments = routeData.arguments as AccountSuccessfulArgs;
    return AccountSuccessfulScreen(
      data: arguments.data,
      createAccountFlow: arguments.createAccountFlow,
    );
  }

  @override
  String get label => OnboardingRouteSchema.accountSuccessfulRoute.label;

  @override
  String get url => OnboardingRouteSchema.accountSuccessfulRoute.url;
}
