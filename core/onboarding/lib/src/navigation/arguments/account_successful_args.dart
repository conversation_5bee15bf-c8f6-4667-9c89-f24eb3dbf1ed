import 'package:domain/domain.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:onboarding/src/domain/model/account_creation/account_creation_request_model.dart';

part 'account_successful_args.freezed.dart';
part 'account_successful_args.g.dart';

@freezed
abstract class AccountSuccessfulArgs with _$AccountSuccessfulArgs {
  const factory AccountSuccessfulArgs({
    required AccountCreationRequestModel data,
    required CreateAccountFlow createAccountFlow,
  }) = _AccountSuccessfulArgs;

  factory AccountSuccessfulArgs.fromJson(Map<String, dynamic> json) =>
      _$AccountSuccessfulArgsFromJson(json);
}
