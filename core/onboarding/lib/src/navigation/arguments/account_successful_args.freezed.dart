// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'account_successful_args.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$AccountSuccessfulArgs {

 AccountCreationRequestModel get data; CreateAccountFlow get createAccountFlow;
/// Create a copy of AccountSuccessfulArgs
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AccountSuccessfulArgsCopyWith<AccountSuccessfulArgs> get copyWith => _$AccountSuccessfulArgsCopyWithImpl<AccountSuccessfulArgs>(this as AccountSuccessfulArgs, _$identity);

  /// Serializes this AccountSuccessfulArgs to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AccountSuccessfulArgs&&(identical(other.data, data) || other.data == data)&&(identical(other.createAccountFlow, createAccountFlow) || other.createAccountFlow == createAccountFlow));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,data,createAccountFlow);

@override
String toString() {
  return 'AccountSuccessfulArgs(data: $data, createAccountFlow: $createAccountFlow)';
}


}

/// @nodoc
abstract mixin class $AccountSuccessfulArgsCopyWith<$Res>  {
  factory $AccountSuccessfulArgsCopyWith(AccountSuccessfulArgs value, $Res Function(AccountSuccessfulArgs) _then) = _$AccountSuccessfulArgsCopyWithImpl;
@useResult
$Res call({
 AccountCreationRequestModel data, CreateAccountFlow createAccountFlow
});


$AccountCreationRequestModelCopyWith<$Res> get data;

}
/// @nodoc
class _$AccountSuccessfulArgsCopyWithImpl<$Res>
    implements $AccountSuccessfulArgsCopyWith<$Res> {
  _$AccountSuccessfulArgsCopyWithImpl(this._self, this._then);

  final AccountSuccessfulArgs _self;
  final $Res Function(AccountSuccessfulArgs) _then;

/// Create a copy of AccountSuccessfulArgs
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? data = null,Object? createAccountFlow = null,}) {
  return _then(_self.copyWith(
data: null == data ? _self.data : data // ignore: cast_nullable_to_non_nullable
as AccountCreationRequestModel,createAccountFlow: null == createAccountFlow ? _self.createAccountFlow : createAccountFlow // ignore: cast_nullable_to_non_nullable
as CreateAccountFlow,
  ));
}
/// Create a copy of AccountSuccessfulArgs
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AccountCreationRequestModelCopyWith<$Res> get data {
  
  return $AccountCreationRequestModelCopyWith<$Res>(_self.data, (value) {
    return _then(_self.copyWith(data: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _AccountSuccessfulArgs implements AccountSuccessfulArgs {
  const _AccountSuccessfulArgs({required this.data, required this.createAccountFlow});
  factory _AccountSuccessfulArgs.fromJson(Map<String, dynamic> json) => _$AccountSuccessfulArgsFromJson(json);

@override final  AccountCreationRequestModel data;
@override final  CreateAccountFlow createAccountFlow;

/// Create a copy of AccountSuccessfulArgs
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AccountSuccessfulArgsCopyWith<_AccountSuccessfulArgs> get copyWith => __$AccountSuccessfulArgsCopyWithImpl<_AccountSuccessfulArgs>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$AccountSuccessfulArgsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AccountSuccessfulArgs&&(identical(other.data, data) || other.data == data)&&(identical(other.createAccountFlow, createAccountFlow) || other.createAccountFlow == createAccountFlow));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,data,createAccountFlow);

@override
String toString() {
  return 'AccountSuccessfulArgs(data: $data, createAccountFlow: $createAccountFlow)';
}


}

/// @nodoc
abstract mixin class _$AccountSuccessfulArgsCopyWith<$Res> implements $AccountSuccessfulArgsCopyWith<$Res> {
  factory _$AccountSuccessfulArgsCopyWith(_AccountSuccessfulArgs value, $Res Function(_AccountSuccessfulArgs) _then) = __$AccountSuccessfulArgsCopyWithImpl;
@override @useResult
$Res call({
 AccountCreationRequestModel data, CreateAccountFlow createAccountFlow
});


@override $AccountCreationRequestModelCopyWith<$Res> get data;

}
/// @nodoc
class __$AccountSuccessfulArgsCopyWithImpl<$Res>
    implements _$AccountSuccessfulArgsCopyWith<$Res> {
  __$AccountSuccessfulArgsCopyWithImpl(this._self, this._then);

  final _AccountSuccessfulArgs _self;
  final $Res Function(_AccountSuccessfulArgs) _then;

/// Create a copy of AccountSuccessfulArgs
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? data = null,Object? createAccountFlow = null,}) {
  return _then(_AccountSuccessfulArgs(
data: null == data ? _self.data : data // ignore: cast_nullable_to_non_nullable
as AccountCreationRequestModel,createAccountFlow: null == createAccountFlow ? _self.createAccountFlow : createAccountFlow // ignore: cast_nullable_to_non_nullable
as CreateAccountFlow,
  ));
}

/// Create a copy of AccountSuccessfulArgs
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AccountCreationRequestModelCopyWith<$Res> get data {
  
  return $AccountCreationRequestModelCopyWith<$Res>(_self.data, (value) {
    return _then(_self.copyWith(data: value));
  });
}
}

// dart format on
