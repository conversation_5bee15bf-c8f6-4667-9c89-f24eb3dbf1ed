// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'account_successful_args.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_AccountSuccessfulArgs _$AccountSuccessfulArgsFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('_AccountSuccessfulArgs', json, ($checkedConvert) {
  final val = _AccountSuccessfulArgs(
    data: $checkedConvert(
      'data',
      (v) => AccountCreationRequestModel.fromJson(v as Map<String, dynamic>),
    ),
    createAccountFlow: $checkedConvert(
      'createAccountFlow',
      (v) => $enumDecode(_$CreateAccountFlowEnumMap, v),
    ),
  );
  return val;
});

Map<String, dynamic> _$AccountSuccessfulArgsToJson(
  _AccountSuccessfulArgs instance,
) => <String, dynamic>{
  'data': instance.data.toJson(),
  'createAccountFlow': _$CreateAccountFlowEnumMap[instance.createAccountFlow]!,
};

const _$CreateAccountFlowEnumMap = {
  CreateAccountFlow.firstLiveAccount: 'firstLiveAccount',
  CreateAccountFlow.additionalLiveAccount: 'additionalLiveAccount',
  CreateAccountFlow.demoAccount: 'demoAccount',
};
