// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'create_account_main_args.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_CreateAccountMainArgs _$CreateAccountMainArgsFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('_CreateAccountMainArgs', json, ($checkedConvert) {
  final val = _CreateAccountMainArgs(
    createAccountFlow: $checkedConvert(
      'createAccountFlow',
      (v) => $enumDecode(_$CreateAccountFlowEnumMap, v),
    ),
  );
  return val;
});

Map<String, dynamic> _$CreateAccountMainArgsToJson(
  _CreateAccountMainArgs instance,
) => <String, dynamic>{
  'createAccountFlow': _$CreateAccountFlowEnumMap[instance.createAccountFlow]!,
};

const _$CreateAccountFlowEnumMap = {
  CreateAccountFlow.firstLiveAccount: 'firstLiveAccount',
  CreateAccountFlow.additionalLiveAccount: 'additionalLiveAccount',
  CreateAccountFlow.demoAccount: 'demoAccount',
};
