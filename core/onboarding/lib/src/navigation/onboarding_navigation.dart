import 'package:domain/domain.dart';
import 'package:onboarding/src/data/send_otp_response_model/send_otp_response_model.dart';
import 'package:onboarding/src/domain/model/account_creation/account_creation_request_model.dart';
import 'package:onboarding/src/domain/model/mobile_otp_verification_args/mobile_otp_verification_args.dart';
import 'package:onboarding/src/navigation/arguments/signup_options_args.dart';

abstract class OnboardingNavigation {
  void goToSignupOptions({required SignupOptionsArgs args});
  void goToLoginOptions();
  void goToLogin();
  void goToSignup({String? email});
  void navigateToUserRegistration();
  void goToPersonalDetailsIntroPage();
  void navigateToVerifyMobile({required MobileOtpVerificationArgs args});
  void navigateToMobileNumberInput();
  void navigateToOtpInput({
    required MobileOtpVerificationArgs args,
    required SendOtpResponseData sendOtpModel,
  });
  void removeUntilMobileNumberInput();
  void navigateToPhoneNumberVerified();
  void navigateToCountrySelector();
  void navigateToProgressTracker();
  void navigateToVerifyIdentity({bool replaceRoute = false});
  void navigateToMorphFormBuilder();
  void navigateToCreateAccountIntro();
  void navigateToCreateAccountMain({
    required CreateAccountFlow createAccountFlow,
  });
  void navigateToAccountSuccessful({
    required AccountCreationRequestModel data,
    required CreateAccountFlow createAccountFlow,
    bool replace = false,
  });
  void navigateToDepositIntro();
  void navigateToDepositPaymentOptions();
  void navigateToHub({bool replace = false});
  void goToCitySelection();
  void navigateToSwitchAccounts({bool replace = false});
  void navigateBackToAccountsScreen();
}
