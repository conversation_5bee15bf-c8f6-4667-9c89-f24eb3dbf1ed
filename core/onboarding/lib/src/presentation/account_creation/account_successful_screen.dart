import 'package:domain/domain.dart';
import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:onboarding/src/di/di_container.dart';
import 'package:onboarding/src/domain/enums/account_creation_enums.dart';
import 'package:onboarding/src/domain/model/account_creation/account_creation_request_model.dart';
import 'package:onboarding/src/navigation/onboarding_navigation.dart';
import 'package:onboarding/src/assets/assets.gen.dart' as onboarding;

class AccountSuccessfulScreen extends StatelessWidget {
  const AccountSuccessfulScreen({
    super.key,
    this.data,
    required this.createAccountFlow,
  });

  // todo (aakash): Check if we need to change this to response model
  final AccountCreationRequestModel? data;
  final CreateAccountFlow createAccountFlow;

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final textStyles = DuploTextStyles.of(context);
    final localization = EquitiLocalization.of(context);
    return Scaffold(
      backgroundColor: theme.background.bgPrimary,
      body: SafeArea(
        top: false,
        child: Column(
          children: [
            Expanded(
              flex: 40,
              child: DuploLottieView.asset(
                lightAnimation:
                    onboarding.Assets.lotties.accountCreationSuccessful,
                darkAnimation:
                    onboarding.Assets.lotties.accountCreationSuccessful,
                alignment: Alignment.topCenter,
              ),
            ),
            Expanded(
              flex: 60,
              child: Semantics(
                identifier: "account_successful_scroll",
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      const SizedBox(height: 28),
                      DuploText(
                        text:
                            localization.onboarding_accountSuccessfullyCreated,
                        style: textStyles.textXl,
                        fontWeight: DuploFontWeight.semiBold,
                        color: theme.text.textPrimary,
                      ),
                      const SizedBox(height: 28),
                      Container(
                        margin: const EdgeInsets.symmetric(horizontal: 16),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 12,
                        ),
                        decoration: BoxDecoration(
                          color: theme.background.bgSecondary,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                DuploText(
                                  text: localization.onboarding_nickname,
                                  style: textStyles.textXs,
                                  fontWeight: DuploFontWeight.medium,
                                  color: theme.text.textSecondary,
                                ),
                                DuploText(
                                  text: data!.accountNickname,
                                  style: textStyles.textXs,
                                  fontWeight: DuploFontWeight.semiBold,
                                  color: theme.text.textSecondary,
                                ),
                              ],
                            ),
                            const SizedBox(height: 26),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                DuploText(
                                  text: localization.onboarding_type,
                                  style: textStyles.textXs,
                                  fontWeight: DuploFontWeight.medium,
                                  color: theme.text.textSecondary,
                                ),
                                DuploText(
                                  text:
                                      data!.platformAccountType ==
                                              AccountCreationType.standard
                                          ? localization.onboarding_standard
                                          : localization.onboarding_premier,
                                  style: textStyles.textXs,
                                  fontWeight: DuploFontWeight.semiBold,
                                  color: theme.text.textSecondary,
                                ),
                              ],
                            ),
                            const SizedBox(height: 26),
                            if (data!.leverage != null) ...[
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  DuploText(
                                    text: localization.onboarding_maxLeverage,
                                    style: textStyles.textXs,
                                    fontWeight: DuploFontWeight.medium,
                                    color: theme.text.textSecondary,
                                  ),
                                  DuploText(
                                    text: '1:${data!.leverage!}',
                                    style: textStyles.textXs,
                                    fontWeight: DuploFontWeight.semiBold,
                                    color: theme.text.textSecondary,
                                  ),
                                ],
                              ),
                              const SizedBox(height: 26),
                            ],

                            if (data!.swapFreeAccount != null) ...[
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  DuploText(
                                    text:
                                        localization.onboarding_swapFreeAccount,
                                    style: textStyles.textXs,
                                    fontWeight: DuploFontWeight.medium,
                                    color: theme.text.textSecondary,
                                  ),
                                  DuploText(
                                    text:
                                        data!.swapFreeAccount!
                                            ? localization.onboarding_yes
                                            : localization.onboarding_no,
                                    style: textStyles.textXs,
                                    fontWeight: DuploFontWeight.semiBold,
                                    color: theme.text.textSecondary,
                                  ),
                                ],
                              ),
                              const SizedBox(height: 26),
                            ],
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                DuploText(
                                  text: localization.onboarding_currency,
                                  style: textStyles.textXs,
                                  fontWeight: DuploFontWeight.medium,
                                  color: theme.text.textSecondary,
                                ),
                                DuploText(
                                  text: data!.currency,
                                  style: textStyles.textXs,
                                  fontWeight: DuploFontWeight.semiBold,
                                  color: theme.text.textSecondary,
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            _CtaButtons(createAccountFlow: createAccountFlow),
          ],
        ),
      ),
    );
  }
}

class _CtaButtons extends StatelessWidget {
  const _CtaButtons({required this.createAccountFlow});

  final CreateAccountFlow createAccountFlow;

  @override
  Widget build(BuildContext context) {
    return switch (createAccountFlow) {
      CreateAccountFlow.firstLiveAccount =>
        const _FirstLiveAccountFlowCtaButtons(),
      CreateAccountFlow.additionalLiveAccount =>
        const _AdditionalLiveAccountFlowCtaButtons(),
      CreateAccountFlow.demoAccount => const _DemoAccountFlowCtaButtons(),
    };
  }
}

class _FirstLiveAccountFlowCtaButtons extends StatelessWidget {
  const _FirstLiveAccountFlowCtaButtons();

  @override
  Widget build(BuildContext context) {
    final localization = EquitiLocalization.of(context);
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: DuploButton.defaultPrimary(
            semanticsIdentifier: 'onboarding_create_account',
            title: localization.onboarding_fundAccount,
            isLoading: false,
            trailingIcon: onboarding.Assets.images.continueIc.keyName,
            onTap: () {
              diContainer<OnboardingNavigation>().navigateToDepositIntro();
            },
            useFullWidth: true,
          ),
        ),
        const SizedBox(height: 12),
        DuploButton.link(
          semanticsIdentifier: "onboarding_create_account_skip",
          title: localization.onboarding_backToHome,
          onTap: () {
            diContainer<OnboardingNavigation>().navigateToHub(replace: true);
          },
        ),
        const SizedBox(height: 16),
      ],
    );
  }
}

class _AdditionalLiveAccountFlowCtaButtons extends StatelessWidget {
  const _AdditionalLiveAccountFlowCtaButtons({super.key});

  @override
  Widget build(BuildContext context) {
    final localization = EquitiLocalization.of(context);
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: DuploButton.defaultPrimary(
            semanticsIdentifier: 'create_account_continue_button',
            title: localization.onboarding_fundAccount,
            isLoading: false,
            trailingIcon: onboarding.Assets.images.continueIc.keyName,
            onTap: () {
              diContainer<OnboardingNavigation>()
                  .navigateToDepositPaymentOptions();
            },
            useFullWidth: true,
          ),
        ),
        const SizedBox(height: 12),
        DuploButton.link(
          semanticsIdentifier: 'create_account_back_button',
          title: localization.onboarding_backToAccounts,
          onTap: () {
            diContainer<OnboardingNavigation>().navigateBackToAccountsScreen();
          },
        ),
        const SizedBox(height: 16),
      ],
    );
  }
}

class _DemoAccountFlowCtaButtons extends StatelessWidget {
  const _DemoAccountFlowCtaButtons();

  @override
  Widget build(BuildContext context) {
    final localization = EquitiLocalization.of(context);
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: DuploButton.defaultPrimary(
            title: localization.onboarding_backToAccounts,
            isLoading: false,
            trailingIcon: onboarding.Assets.images.continueIc.keyName,
            onTap: () {
              diContainer<OnboardingNavigation>()
                  .navigateBackToAccountsScreen();
            },
            useFullWidth: true,
          ),
        ),
        const SizedBox(height: 16),
      ],
    );
  }
}
