import 'package:domain/domain.dart';
import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:onboarding/src/di/di_container.dart';
import 'package:onboarding/src/presentation/account_creation/bloc/account_creation_bloc.dart';
import 'package:onboarding/src/presentation/account_creation/pages/choose_account_page.dart';
import 'package:onboarding/src/presentation/account_creation/pages/choose_account_type_page.dart';
import 'package:onboarding/src/presentation/account_creation/pages/choose_currency_page.dart';
import 'package:onboarding/src/presentation/account_creation/pages/choose_leverage_page.dart';
import 'package:onboarding/src/presentation/account_creation/pages/choose_nickname_page.dart';
import 'package:onboarding/src/presentation/account_creation/pages/choose_password_page.dart';
import 'package:onboarding/src/presentation/account_creation/pages/choose_platform_page.dart';

class CreateAccountMainScreen extends StatefulWidget {
  const CreateAccountMainScreen({super.key, required this.createAccountFlow});

  final CreateAccountFlow createAccountFlow;

  @override
  State<CreateAccountMainScreen> createState() =>
      _CreateAccountMainScreenState();
}

class _CreateAccountMainScreenState extends State<CreateAccountMainScreen> {
  late final PageController pageController;

  late final List<Widget Function(int)> pages;

  @override
  void initState() {
    super.initState();
    pages = _buildPagesForFlow(widget.createAccountFlow);
    pageController = PageController();
  }

  /// Builds the page flow based on the account creation flow type.
  List<Widget Function(int)> _buildPagesForFlow(CreateAccountFlow flow) {
    return switch (flow) {
      CreateAccountFlow.firstLiveAccount => _buildFirstLiveAccountPages(),
      CreateAccountFlow.additionalLiveAccount =>
        _buildAdditionalLiveAccountPages(),
      CreateAccountFlow.demoAccount => _buildDemoAccountPages(),
    };
  }

  /// Pages for first live account creation
  List<Widget Function(int)> _buildFirstLiveAccountPages() {
    return [
      (index) => ChooseAccountPage(
        pageIndex: index,
        tradingEnvironment: widget.createAccountFlow,
      ),
      (index) => ChooseAccountTypePage(pageIndex: index),
      (index) => ChooseLeveragePage(pageIndex: index),
      (index) => ChooseCurrencyPage(pageIndex: index),
      (index) => ChooseNicknamePage(pageIndex: index),
      (index) => ChoosePasswordPage(
        pageIndex: index,
        tradingEnvironment: widget.createAccountFlow,
      ),
    ];
  }

  /// Pages for additional live account creation
  List<Widget Function(int)> _buildAdditionalLiveAccountPages() {
    return [
      (index) => ChoosePlatformPage(pageIndex: index),
      (index) => ChooseAccountPage(
        pageIndex: index,
        tradingEnvironment: widget.createAccountFlow,
      ),
      (index) => ChooseAccountTypePage(pageIndex: index),
      (index) => ChooseLeveragePage(pageIndex: index),
      (index) => ChooseCurrencyPage(pageIndex: index),
      (index) => ChooseNicknamePage(pageIndex: index),
      (index) => ChoosePasswordPage(
        pageIndex: index,
        tradingEnvironment: widget.createAccountFlow,
      ),
    ];
  }

  /// Pages for demo account creation
  List<Widget Function(int)> _buildDemoAccountPages() {
    return [
      (index) => ChoosePlatformPage(pageIndex: index),
      (index) => ChooseAccountPage(
        pageIndex: index,
        tradingEnvironment: widget.createAccountFlow,
      ),
      (index) => ChooseCurrencyPage(pageIndex: index),
      (index) => ChooseNicknamePage(pageIndex: index),
      (index) => ChoosePasswordPage(
        pageIndex: index,
        tradingEnvironment: widget.createAccountFlow,
      ),
    ];
  }

  @override
  void dispose() {
    pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final textStyles = DuploTextStyles.of(context);
    final localization = EquitiLocalization.of(context);
    return BlocProvider<AccountCreationBloc>(
      create:
          (_) =>
              diContainer<AccountCreationBloc>()
                ..add(AccountCreationEvent.fetchAccountCreationData()),
      child: BlocConsumer<AccountCreationBloc, AccountCreationState>(
        buildWhen: (previous, current) {
          return previous != current;
        },
        listener: (ctx, state) {
          switch (state.progressState) {
            case DataLoadedState():
              final bool shouldUpdateInitialAccount =
                  widget.createAccountFlow ==
                  CreateAccountFlow.additionalLiveAccount;

              ctx.read<AccountCreationBloc>().add(
                AccountCreationEvent.updateAccountCreationData(
                  accountCreationRequestModel: state.accountCreationRequestModel
                      .copyWith(
                        isInitialAccount:
                            shouldUpdateInitialAccount ? false : null,
                        accountType:
                            widget.createAccountFlow ==
                                    CreateAccountFlow.demoAccount
                                ? 'demoTradingAccount'
                                : null,
                      ),
                ),
              );

              break;
            case NavigatedNextState():
              if (pageController.hasClients)
                pageController.nextPage(
                  duration: const Duration(milliseconds: 200),
                  curve: Curves.linear,
                );
              break;
            case NavigatedBackState():
              if (pageController.hasClients)
                pageController.previousPage(
                  duration: const Duration(milliseconds: 200),
                  curve: Curves.linear,
                );
              break;
            default:
              break;
          }
        },
        builder: (ctx, state) {
          return switch (state.progressState) {
            DataLoadingState() => LoadingView(),
            //TODO: Update error screen
            DataLoadingError() => Scaffold(
              body: Center(
                child: DuploText(
                  text: 'Something went wrong',
                  style: textStyles.textMd,
                ),
              ),
            ),
            _ => () {
              final pageIndex = state.pageIndex ?? 0;
              return GestureDetector(
                onTap: () => FocusScope.of(context).unfocus(),
                child: Scaffold(
                  backgroundColor: theme.background.bgPrimary,
                  resizeToAvoidBottomInset: false,
                  appBar: DuploAppBar(
                    title: localization.onboarding_accountCreation,
                    leading: IconButton(
                      icon: Icon(
                        Icons.adaptive.arrow_back_outlined,
                        color: theme.foreground.fgSecondary,
                      ),
                      onPressed: () {
                        if (pageIndex == 0) {
                          Navigator.pop(context);
                          return;
                        }
                        ctx.read<AccountCreationBloc>().add(
                          AccountCreationEvent.navigateToPrevious(
                            pageIndex: pageIndex,
                          ),
                        );
                        FocusScope.of(context).unfocus();
                      },
                    ),
                  ),
                  body: Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16.0,
                      vertical: 8.0,
                    ),
                    child: Column(
                      children: [
                        DuploProgressBar(
                          progressValue: (pageIndex + 1) / pages.length,
                          currentBarIndex: 1,
                        ),
                        Expanded(
                          child: PageView.builder(
                            physics: const NeverScrollableScrollPhysics(),
                            controller: pageController,
                            itemCount: pages.length,
                            itemBuilder: (_, index) {
                              return pages[index](index);
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }(),
          };
        },
      ),
    );
  }
}
