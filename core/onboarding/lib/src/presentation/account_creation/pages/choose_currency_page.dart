import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:onboarding/src/assets/assets.gen.dart' as onboarding;
import 'package:onboarding/src/presentation/account_creation/bloc/account_creation_bloc.dart';

class ChooseCurrencyPage extends StatelessWidget {
  const ChooseCurrencyPage({super.key, this.pageIndex});

  final int? pageIndex;

  int getSelectedIndex(
    List<Map<String, String>> countryItems,
    String currencyName,
  ) {
    int index = countryItems.indexWhere((item) => item['name'] == currencyName);
    if (index == -1) {
      index = 0;
    }
    return index;
  }

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final textStyles = DuploTextStyles.of(context);
    final localization = EquitiLocalization.of(context);
    final countryItems = [
      {'currencyCode': "AED", 'name': 'AED'},
      {'currencyCode': "USD", 'name': 'USD'},
    ];
    return BlocBuilder<AccountCreationBloc, AccountCreationState>(
      buildWhen: (previous, current) => previous != current,
      builder: (_, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            DuploText(
              text: localization.onboarding_chooseAccountCurrency,
              style: textStyles.textXl,
              fontWeight: DuploFontWeight.semiBold,
              color: theme.text.textPrimary,
            ),
            const SizedBox(height: 8),
            DuploText(
              text: localization.onboarding_chooseAccountCurrencyDescription,
              style: textStyles.textSm,
              color: theme.text.textSecondary,
            ),
            const SizedBox(height: 24),
            DuploDropDown.selector(
              hint: localization.onboarding_currency,
              dropDownItemModels:
                  countryItems.map((item) {
                    return DropDownItemModel(
                      title: item['name']!,
                      image: FlagProvider.getFlagFromCurrencyCode(
                        item['currencyCode']!,
                      ),
                    );
                  }).toList(),
              bottomSheetTitle: localization.onboarding_selectCurrency,
              onChanged: (index) {
                context.read<AccountCreationBloc>().add(
                  AccountCreationEvent.updateAccountCreationData(
                    accountCreationRequestModel: state
                        .accountCreationRequestModel
                        .copyWith(
                          // ignore: avoid-unsafe-collection-methods
                          currency: countryItems[index]['name']!,
                        ),
                  ),
                );
              },
              selectedIndex: getSelectedIndex(
                countryItems,
                state.accountCreationRequestModel.currency,
              ),
              context: context,
            ),
            Spacer(),
            DuploButton.defaultPrimary(
              semanticsIdentifier: "continue_button",
              title: localization.onboarding_continueButton,
              trailingIcon: onboarding.Assets.images.continueIc.keyName,
              onTap: () {
                context.read<AccountCreationBloc>().add(
                  AccountCreationEvent.navigateToNext(pageIndex: pageIndex),
                );
              },
              useFullWidth: true,
            ),
            const SizedBox(height: 32),
          ],
        );
      },
    );
  }
}
