import 'package:domain/domain.dart';
import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:onboarding/src/assets/assets.gen.dart' as onboarding;
import 'package:onboarding/src/di/di_container.dart';
import 'package:onboarding/src/navigation/onboarding_navigation.dart';
import 'package:onboarding/src/presentation/account_creation/bloc/account_creation_bloc.dart';

class ChoosePasswordPage extends StatefulWidget {
  const ChoosePasswordPage({
    super.key,
    this.pageIndex,
    required this.tradingEnvironment,
  });

  final int? pageIndex;
  final CreateAccountFlow tradingEnvironment;

  @override
  State<ChoosePasswordPage> createState() => _ChoosePasswordPageState();
}

class _ChoosePasswordPageState extends State<ChoosePasswordPage> {
  late final TextEditingController _passwordController;

  @override
  void initState() {
    super.initState();
    _passwordController = TextEditingController();
  }

  @override
  void dispose() {
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final textStyles = DuploTextStyles.of(context);
    final localization = EquitiLocalization.of(context);
    return BlocConsumer<AccountCreationBloc, AccountCreationState>(
      listener: (ctx, state) {
        switch (state.progressState) {
          case NavigatedBackState():
            ctx.read<AccountCreationBloc>().add(
              AccountCreationEvent.updateAccountCreationData(
                accountCreationRequestModel: state.accountCreationRequestModel
                    .copyWith(password: ''),
              ),
            );
          case DataSubmitError():
            DuploErrorSheet.show<void>(
              context: ctx,
              bodyTitle: localization.onboarding_somethingWentWrong,
              bodySubTitle: localization.onboarding_errorDescription,
              actionWidget: DuploButton.secondary(
                title: localization.onboarding_goBack,
                useFullWidth: true,
                onTap: () {
                  Navigator.pop(context);
                },
              ),
            );
            break;
          case DataSubmittedState():
            diContainer<OnboardingNavigation>().navigateToAccountSuccessful(
              data: state.accountCreationRequestModel,
              createAccountFlow: widget.tradingEnvironment,
              replace: true,
            );
            break;
          default:
            break;
        }
      },
      buildWhen: (previous, current) => previous != current,
      builder: (_, state) {
        final isLoading = switch (state.progressState) {
          DataSubmittingState() => true,
          _ => false,
        };
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            DuploText(
              text: localization.onboarding_createPasswordForAccount,
              style: textStyles.textXl,
              fontWeight: DuploFontWeight.semiBold,
              color: theme.text.textPrimary,
            ),
            const SizedBox(height: 8),
            DuploText(
              text: localization.onboarding_makeItDifficult,
              style: textStyles.textSm,
              color: theme.text.textSecondary,
            ),
            const SizedBox(height: 24),
            PasswordFieldValidator(
              semanticsIdentifier: "password_field",
              controller: _passwordController,
              needValidationComponent: true,
              onPasswordValidation: (password, isValid) {
                context.read<AccountCreationBloc>().add(
                  AccountCreationEvent.updateAccountCreationData(
                    accountCreationRequestModel: state
                        .accountCreationRequestModel
                        .copyWith(password: isValid ? password : ''),
                  ),
                );
              },
            ),
            Spacer(),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                onboarding.Assets.images.warningIc.svg(height: 14),
                const SizedBox(width: 6),
                Expanded(
                  child: DuploText(
                    text: localization.onboarding_passwordInfo,
                    style: textStyles.textXs,
                    color: theme.text.textQuaternary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            DuploButton.defaultPrimary(
              semanticsIdentifier: "continue_button",
              isLoading: isLoading,
              isDisabled: state.accountCreationRequestModel.password.isEmpty,
              title: localization.onboarding_continueButton,
              trailingIcon: onboarding.Assets.images.continueIc.keyName,
              onTap: () {
                context.read<AccountCreationBloc>().add(
                  AccountCreationEvent.submitAccountCreationData(),
                );
              },
              useFullWidth: true,
            ),
            const SizedBox(height: 32),
          ],
        );
      },
    );
  }
}
