// ignore_for_file: avoid-unsafe-collection-methods

import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:onboarding/src/data/account_creation_body_model.dart';
import 'package:onboarding/src/domain/enums/account_creation_enums.dart';
import 'package:onboarding/src/presentation/account_creation/bloc/account_creation_bloc.dart';
import 'package:onboarding/src/assets/assets.gen.dart' as onboarding;

class ChoosePlatformPage extends StatelessWidget {
  const ChoosePlatformPage({super.key, this.pageIndex});

  final int? pageIndex;

  SvgPicture getLeadingIcon(AccountCreationPlatform code) {
    switch (code) {
      case AccountCreationPlatform.dulcimer:
        return onboarding.Assets.images.traderCandle.svg();
      case AccountCreationPlatform.mt5:
        return onboarding.Assets.images.mt5Icon.svg();
      case AccountCreationPlatform.mt4:
        return onboarding.Assets.images.mt4Icon.svg();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final textStyles = DuploTextStyles.of(context);
    final localization = EquitiLocalization.of(context);
    return BlocBuilder<AccountCreationBloc, AccountCreationState>(
      buildWhen: (previous, current) => previous != current,
      builder: (_, state) {
        final List<TradingPlatform> newPlatform =
            state.accountCreationBodyModel!.data.platforms;
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            DuploText(
              text: localization.onboarding_choosePlatform,
              style: textStyles.textXl,
              fontWeight: DuploFontWeight.semiBold,
              color: theme.text.textPrimary,
            ),
            const SizedBox(height: 24),
            for (int i = 0; i < newPlatform.length; i++) ...[
              DuploTap(
                onTap: () {
                  context.read<AccountCreationBloc>().add(
                    AccountCreationEvent.updateAccountCreationData(
                      accountCreationRequestModel: state
                          .accountCreationRequestModel
                          .copyWith(platform: newPlatform[i].code),
                    ),
                  );
                },
                child: DuploSelectionContainer(
                  isSelected:
                      state.accountCreationRequestModel.platform ==
                      newPlatform[i].code,
                  borderWidth: 2,
                  borderRadius: 8,
                  backgroundColor: theme.background.bgActive,
                  selectedIconAlignment: CrossAxisAlignment.center,
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 20,
                  ),
                  leading: getLeadingIcon(newPlatform[i].code),
                  title: DuploText(
                    text: newPlatform[i].code.displayName,
                    style: textStyles.textMd,
                    fontWeight: DuploFontWeight.semiBold,
                    color: theme.text.textPrimary,
                  ),
                  subTitle: DuploText(
                    text: newPlatform[i].description,
                    style: textStyles.textXs,
                    color: theme.text.textTertiary,
                  ),
                ),
              ),
              if (i < newPlatform.length - 1) const SizedBox(height: 12),
            ],
            const Spacer(),
            DuploButton.defaultPrimary(
              semanticsIdentifier: "continue_button",
              title: localization.onboarding_continueButton,
              trailingIcon: onboarding.Assets.images.continueIc.keyName,
              onTap: () {
                context.read<AccountCreationBloc>().add(
                  AccountCreationEvent.navigateToNext(pageIndex: pageIndex),
                );
              },
              useFullWidth: true,
            ),
            const SizedBox(height: 32),
          ],
        );
      },
    );
  }
}
