import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:onboarding/src/assets/assets.gen.dart' as onboarding;
import 'package:onboarding/src/di/di_container.dart';
import 'package:onboarding/src/navigation/onboarding_navigation.dart';

class DepositIntroScreen extends StatelessWidget {
  const DepositIntroScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final localization = EquitiLocalization.of(context);
    return Scaffold(
      backgroundColor: theme.background.bgPrimary,
      body: SafeArea(
        top: false,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Expanded(
              flex: 1,
              child: DuploLottieView.asset(
                darkAnimation: onboarding.Assets.lotties.depositIntro,
                lightAnimation: onboarding.Assets.lotties.depositIntro,
              ),
            ),
            Expanded(
              flex: 1,
              child: Column(
                children: [
                  Spacer(flex: 1),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: Column(
                      children: [
                        DuploText(
                          text: localization.onboarding_fundYourTrades,
                          style: DuploTextStyles.of(context).textXl,
                          fontWeight: DuploFontWeight.semiBold,
                          color: theme.text.textPrimary,
                        ),
                        SizedBox(height: 16),
                        DuploText(
                          text: localization.onboarding_depositIntroDescription,
                          style: DuploTextStyles.of(context).textSm,
                          textAlign: TextAlign.center,
                          color: theme.text.textSecondary,
                        ),
                      ],
                    ),
                  ),
                  Spacer(flex: 5),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: DuploButton.defaultPrimary(
                      title: localization.onboarding_addFunds,
                      isLoading: false,
                      trailingIcon:
                          Assets.images
                              .chevronRightDirectional(context)
                              .keyName,
                      onTap: () {
                        diContainer<OnboardingNavigation>()
                            .navigateToDepositPaymentOptions();
                      },
                      useFullWidth: true,
                    ),
                  ),
                  SizedBox(height: 16),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: DuploButton.link(
                      title: localization.onboarding_doThisStepLater,
                      isLoading: false,
                      onTap: () {
                        diContainer<OnboardingNavigation>().navigateToHub();
                      },
                      useFullWidth: true,
                    ),
                  ),
                  SizedBox(height: 16),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
