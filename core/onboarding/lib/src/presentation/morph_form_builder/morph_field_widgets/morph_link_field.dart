import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';
import 'package:onboarding/src/data/form/option_model.dart';

class MorphLinkField extends StatelessWidget {
  const MorphLinkField({super.key, required this.item, required this.onTap});
  final OptionModel item;
  final Future<void> Function() onTap;

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    return Semantics(
      identifier: "link_field",
      child: DuploTap(
        onTap: () => onTap(),
        child: Container(
          padding: EdgeInsets.all(DuploSpacing.spacing_md_8),
          child: DuploLabelInfoChevronWidget(
            title: item.optionLabel ?? item.optionName,
            iconColor: theme.foreground.fgQuaternary,
          ),
        ),
      ),
    );
  }
}
