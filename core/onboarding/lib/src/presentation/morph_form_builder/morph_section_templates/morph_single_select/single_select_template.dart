import 'dart:developer';

import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:leancode_forms/leancode_forms.dart';
import 'package:onboarding/src/data/form/field_model.dart';
import 'package:onboarding/src/data/form/section_submit_result.dart';
import 'package:onboarding/src/presentation/morph_form_builder/bloc/cubits/section_cubit.dart';
import 'package:onboarding/src/presentation/morph_form_builder/morph_field_widgets/morph_info_text.dart';
import 'package:onboarding/src/presentation/morph_form_builder/morph_field_widgets/morph_label_field.dart';
import 'package:onboarding/src/presentation/morph_form_builder/morph_field_widgets/morph_section_description.dart';
import 'package:onboarding/src/presentation/morph_form_builder/morph_field_widgets/morph_section_title.dart';

class SingleSelectTemplate extends StatefulWidget {
  const SingleSelectTemplate({
    super.key,
    required this.appBarTitle,
    required this.title,
    this.description,
    this.radioField,
    required this.onSelect,
    this.infoText,
  });
  final String appBarTitle;
  final String title;
  final String? description, infoText;
  final RadioField? radioField;
  final ValueChanged<SectionSubmitResult> onSelect;

  @override
  State<SingleSelectTemplate> createState() => _SingleSelectTemplateState();
}

class _SingleSelectTemplateState extends State<SingleSelectTemplate> {
  ScrollController _controller = ScrollController();

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final sectionCubit = context.read<SectionCubit>();
    final singleSelectFieldCubit =
        sectionCubit.fieldsMapCubit[widget.radioField?.fieldID]
            as SingleSelectFieldCubit;
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          MorphSectionTitle(text: widget.title),
          const SizedBox(height: 8),
          if (widget.description != null) ...[
            MorphSectionDescription(text: widget.description),
            const SizedBox(height: 24),
          ],
          Expanded(
            child: Scrollbar(
              controller: _controller,
              thumbVisibility: true,
              child: Semantics(
                identifier: "single_select_scroll_view",
                child: ListView.separated(
                  controller: _controller,
                  itemCount: widget.radioField?.options.length ?? 0,
                  padding: EdgeInsets.only(right: 16),
                  separatorBuilder: (ctx, index) {
                    return Container(
                      height: 1,
                      margin: const EdgeInsets.symmetric(
                        vertical: DuploSpacing.spacing_xs_4,
                        horizontal: 16,
                      ),
                      decoration: BoxDecoration(
                        color: theme.border.borderSecondary,
                      ),
                    );
                  },
                  itemBuilder: (listContext, index) {
                    final option = widget.radioField?.options.elementAtOrNull(
                      index,
                    );
                    return MorphLabelField(
                      field: singleSelectFieldCubit,
                      option: option!,
                      onTap: () {
                        try {
                          singleSelectFieldCubit.setValue(option);
                        } catch (e) {
                          log('Error setting value: $e');
                        }
                        widget.onSelect(
                          SectionSubmitResult(
                            nextSectionId: option.nextSectionID,
                          ),
                        );
                      },
                    );
                  },
                ),
              ),
            ),
          ),
          if (widget.infoText != null)
            MorphInfoText(
              text: widget.infoText!,
              bottomPadding: DuploSpacing.spacing_xl_16,
              topPadding: DuploSpacing.spacing_xl_16,
            ),
        ],
      ),
    );
  }
}
