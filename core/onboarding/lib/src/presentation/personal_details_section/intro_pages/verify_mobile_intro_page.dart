import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:onboarding/src/assets/assets.gen.dart' as onboarding;
import 'package:onboarding/src/domain/model/mobile_otp_verification_args/mobile_otp_verification_args.dart';

import '../../../di/di_container.dart';
import '../intro_pages/bloc/intro_page_bloc.dart';
import '../widgets/page_intro_widget.dart';

class VerifyMobileIntroPage extends StatelessWidget {
  const VerifyMobileIntroPage({super.key, required this.args});

  final MobileOtpVerificationArgs args;
  @override
  Widget build(BuildContext context) {
    final localization = EquitiLocalization.of(context);
    return BlocProvider<IntroPageBloc>(
      create: (createContext) => diContainer<IntroPageBloc>(),
      child: <PERSON><PERSON><PERSON>er<IntroPageBloc, IntroPageState>(
        buildWhen: (previous, current) => true,
        builder: (blocContext, state) {
          final bloc = blocContext.read<IntroPageBloc>();
          return PageIntroWidget(
            showBackButton: true,
            title: localization.onboarding_verifyNumberTitle,
            flex1: 38,
            flex2: 62,
            body: localization.onboarding_verifyPhoneNumberSubtitle,
            lightVideo: onboarding.Assets.lotties.confirmMobileNumber,
            darkVideo: onboarding.Assets.lotties.confirmMobileNumber,
            color: Color(0xffC6D7FE),
            actions: [
              DuploButton.primaryBold(
                semanticsIdentifier: "send_code_via_whatsapp",
                useFullWidth: true,
                title: localization.onboarding_sendCodeViaWhatsApp,
                useAssetColor: true,
                isLoading: state.isWhatsAppButtonLoading,
                leadingIcon: onboarding.Assets.images.whatsapp.keyName,
                onTap:
                    () => bloc.add(
                      IntroPageEvent.sendCodeViaWhatsapp(
                        args: args.copyWith(isWhatsapp: true),
                      ),
                    ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 16),
                child: DuploButton.secondary(
                  semanticsIdentifier: "send_code_via_sms",
                  useFullWidth: true,
                  title: localization.onboarding_sendCodeViaSMS,
                  trailingIcon:
                      Assets.images.chevronRightDirectional(context).keyName,
                  isLoading: state.isSsmButtonLoading,
                  onTap:
                      () => bloc.add(
                        IntroPageEvent.sendCodeViaSms(
                          args: args.copyWith(isWhatsapp: false),
                        ),
                      ),
                ),
              ),
              DuploButton.tertiary(
                semanticsIdentifier: "skip_button",
                useFullWidth: true,
                isLoading: state.isSkipButtonLoading,
                title: localization.onboarding_doThisLater,
                onTap: () => bloc.add(IntroPageEvent.willDoThisLater()),
              ),
            ],
          );
        },
      ),
    );
  }
}
