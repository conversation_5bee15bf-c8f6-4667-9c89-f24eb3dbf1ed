import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class DiscoverMoreWidget extends StatelessWidget {
  final String title;
  final String description;
  final String imagePath;
  final String identifier;

  final void Function(String) onButtonPressed;

  const DiscoverMoreWidget({
    required this.description,
    required this.title,
    required this.imagePath,
    required this.identifier,
    required this.onButtonPressed,
  });

  @override
  Widget build(BuildContext context) {
    final textStyle = context.duploTextStyles;
    final theme = context.duploTheme;
    return Semantics(
      identifier: "discover_more",
      child: DuploTap(
        onTap: () => onButtonPressed(identifier),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 16),
          decoration: BoxDecoration(
            color: theme.background.bgPrimaryHover,
            borderRadius: BorderRadius.circular(6),
          ),
          child: Row(
            children: [
              const SizedBox(width: 16),
              Expanded(
                child: <PERSON>umn(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    DuploText(
                      textAlign: TextAlign.start,
                      text: title,
                      style: textStyle.textMd,
                      fontWeight: DuploFontWeight.semiBold,
                      color: theme.text.textPrimary,
                      maxLines: 1,
                    ),
                    DuploText(
                      textAlign: TextAlign.start,
                      text: description,
                      style: textStyle.textXs,
                      fontWeight: DuploFontWeight.regular,
                      color: theme.text.textSecondary,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 2,
                    ),
                  ],
                ),
              ),
              Container(
                width: 80,
                height: 60,
                child: SvgPicture.asset(imagePath),
              ),
              const SizedBox(width: 8),
            ],
          ),
        ),
      ),
    );
  }
}
