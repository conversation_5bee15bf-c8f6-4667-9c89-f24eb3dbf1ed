import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:onboarding/src/data/progress_tracker/progress_tracker_action.dart';

class ProgressTrackerActionWidget extends StatelessWidget {
  final String title;
  final String description;
  final String imagePath;
  final ProgressTrackerAction identifier;
  final bool isHighlighted;

  final void Function(ProgressTrackerAction) onButtonPressed;

  const ProgressTrackerActionWidget({
    required this.description,
    required this.title,
    required this.imagePath,
    required this.identifier,
    required this.onButtonPressed,
    required this.isHighlighted,
  });

  @override
  Widget build(BuildContext context) {
    final textStyle = context.duploTextStyles;
    final theme = context.duploTheme;
    return Semantics(
      identifier: "progress_tracker_action",
      child: DuploTap(
        onTap: () => onButtonPressed(identifier),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 16),
          decoration:
              isHighlighted
                  ? BoxDecoration(
                    color: theme.background.bgPrimaryHover,
                    borderRadius: BorderRadius.circular(6),
                  )
                  : null,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const SizedBox(width: 8),
              Column(
                children: [
                  SvgPicture.asset(
                    imagePath,
                    height: 24,
                    width: 24,
                    colorFilter:
                        isHighlighted
                            ? ColorFilter.mode(
                              theme.border.borderBrand,
                              BlendMode.srcIn,
                            )
                            : ColorFilter.mode(
                              theme.foreground.fgTertiary,
                              BlendMode.srcIn,
                            ),
                  ),
                ],
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    DuploText(
                      textAlign: TextAlign.start,
                      text: title,
                      style: textStyle.textMd,
                      fontWeight: DuploFontWeight.semiBold,
                      color: theme.text.textPrimary,
                      maxLines: 1,
                    ),
                    DuploText(
                      textAlign: TextAlign.start,
                      text: description,
                      style: textStyle.textXs,
                      fontWeight: DuploFontWeight.regular,
                      color: theme.text.textSecondary,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 2,
                    ),
                  ],
                ),
              ),
              Assets.images
                  .chevronRightDirectional(context)
                  .svg(height: 24, width: 24),
              const SizedBox(width: 8),
            ],
          ),
        ),
      ),
    );
  }
}
