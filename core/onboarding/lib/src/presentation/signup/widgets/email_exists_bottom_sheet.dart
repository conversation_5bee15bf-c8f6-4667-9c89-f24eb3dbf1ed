import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:onboarding/src/assets/assets.gen.dart' as trader;
import 'package:onboarding/src/presentation/signup/bloc/signup_flow_bloc.dart';

class EmailExistsBottomSheet extends StatelessWidget {
  const EmailExistsBottomSheet({super.key, required this.blocContext});
  final BuildContext blocContext;

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final localization = EquitiLocalization.of(context);
    return BlocProvider.value(
      value: blocContext.read<SignupFlowBloc>(),
      child: Column(
        children: [
          trader.Assets.images.emailExistsIc.svg(),
          SizedBox(height: 10),
          DuploText(
            text: localization.onboarding_emailExistsTitle,
            style: DuploTextStyles.of(context).textXl,
            fontWeight: DuploFontWeight.semiBold,
            color: theme.text.textPrimary,
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 10),
          DuploText(
            text: localization.onboarding_emailExistsDescription,
            style: DuploTextStyles.of(context).textSm,
            fontWeight: DuploFontWeight.regular,
            color: theme.text.textSecondary,
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 20),
          SizedBox(
            width: MediaQuery.sizeOf(context).width,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 15.0),
              child: DuploButton.defaultPrimary(
                semanticsIdentifier: "sign_in",
                title: localization.onboarding_signIn,
                loadingText: "",
                trailingIcon: trader.Assets.images.continueIc.keyName,
                onTap: () {
                  blocContext.read<SignupFlowBloc>().add(
                    SignupFlowEvent.navigateToLogin(),
                  );
                },
              ),
            ),
          ),
          SizedBox(height: 15),
          SizedBox(
            width: MediaQuery.sizeOf(context).width,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 15.0),
              child: DuploButton.secondary(
                semanticsIdentifier: "use_another_email",
                title: localization.onboarding_useAnotherEmail,
                loadingText: "",
                onTap: () {
                  Navigator.pop(context);
                },
              ),
            ),
          ),
          SizedBox(height: 20),
        ],
      ),
    );
  }
}
