// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'signup_options_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$SignupOptionsEvent {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SignupOptionsEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'SignupOptionsEvent()';
}


}

/// @nodoc
class $SignupOptionsEventCopyWith<$Res>  {
$SignupOptionsEventCopyWith(SignupOptionsEvent _, $Res Function(SignupOptionsEvent) __);
}


/// @nodoc


class _NavigateToLogin implements SignupOptionsEvent {
  const _NavigateToLogin();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _NavigateToLogin);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'SignupOptionsEvent.navigateToLogin()';
}


}




/// @nodoc


class _StartSignup implements SignupOptionsEvent {
  const _StartSignup({required this.country, required this.countryCode, required this.brokerId, this.city});
  

 final  String country;
 final  String countryCode;
 final  String brokerId;
 final  String? city;

/// Create a copy of SignupOptionsEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$StartSignupCopyWith<_StartSignup> get copyWith => __$StartSignupCopyWithImpl<_StartSignup>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _StartSignup&&(identical(other.country, country) || other.country == country)&&(identical(other.countryCode, countryCode) || other.countryCode == countryCode)&&(identical(other.brokerId, brokerId) || other.brokerId == brokerId)&&(identical(other.city, city) || other.city == city));
}


@override
int get hashCode => Object.hash(runtimeType,country,countryCode,brokerId,city);

@override
String toString() {
  return 'SignupOptionsEvent.startSignup(country: $country, countryCode: $countryCode, brokerId: $brokerId, city: $city)';
}


}

/// @nodoc
abstract mixin class _$StartSignupCopyWith<$Res> implements $SignupOptionsEventCopyWith<$Res> {
  factory _$StartSignupCopyWith(_StartSignup value, $Res Function(_StartSignup) _then) = __$StartSignupCopyWithImpl;
@useResult
$Res call({
 String country, String countryCode, String brokerId, String? city
});




}
/// @nodoc
class __$StartSignupCopyWithImpl<$Res>
    implements _$StartSignupCopyWith<$Res> {
  __$StartSignupCopyWithImpl(this._self, this._then);

  final _StartSignup _self;
  final $Res Function(_StartSignup) _then;

/// Create a copy of SignupOptionsEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? country = null,Object? countryCode = null,Object? brokerId = null,Object? city = freezed,}) {
  return _then(_StartSignup(
country: null == country ? _self.country : country // ignore: cast_nullable_to_non_nullable
as String,countryCode: null == countryCode ? _self.countryCode : countryCode // ignore: cast_nullable_to_non_nullable
as String,brokerId: null == brokerId ? _self.brokerId : brokerId // ignore: cast_nullable_to_non_nullable
as String,city: freezed == city ? _self.city : city // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

/// @nodoc
mixin _$SignupOptionsState {

 SignupOptionsProcessState get processState;
/// Create a copy of SignupOptionsState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SignupOptionsStateCopyWith<SignupOptionsState> get copyWith => _$SignupOptionsStateCopyWithImpl<SignupOptionsState>(this as SignupOptionsState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SignupOptionsState&&(identical(other.processState, processState) || other.processState == processState));
}


@override
int get hashCode => Object.hash(runtimeType,processState);

@override
String toString() {
  return 'SignupOptionsState(processState: $processState)';
}


}

/// @nodoc
abstract mixin class $SignupOptionsStateCopyWith<$Res>  {
  factory $SignupOptionsStateCopyWith(SignupOptionsState value, $Res Function(SignupOptionsState) _then) = _$SignupOptionsStateCopyWithImpl;
@useResult
$Res call({
 SignupOptionsProcessState processState
});


$SignupOptionsProcessStateCopyWith<$Res> get processState;

}
/// @nodoc
class _$SignupOptionsStateCopyWithImpl<$Res>
    implements $SignupOptionsStateCopyWith<$Res> {
  _$SignupOptionsStateCopyWithImpl(this._self, this._then);

  final SignupOptionsState _self;
  final $Res Function(SignupOptionsState) _then;

/// Create a copy of SignupOptionsState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? processState = null,}) {
  return _then(_self.copyWith(
processState: null == processState ? _self.processState : processState // ignore: cast_nullable_to_non_nullable
as SignupOptionsProcessState,
  ));
}
/// Create a copy of SignupOptionsState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SignupOptionsProcessStateCopyWith<$Res> get processState {
  
  return $SignupOptionsProcessStateCopyWith<$Res>(_self.processState, (value) {
    return _then(_self.copyWith(processState: value));
  });
}
}


/// @nodoc


class _SignupOptionsState implements SignupOptionsState {
  const _SignupOptionsState({this.processState = const SignupOptionsProcessState.initial()});
  

@override@JsonKey() final  SignupOptionsProcessState processState;

/// Create a copy of SignupOptionsState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SignupOptionsStateCopyWith<_SignupOptionsState> get copyWith => __$SignupOptionsStateCopyWithImpl<_SignupOptionsState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SignupOptionsState&&(identical(other.processState, processState) || other.processState == processState));
}


@override
int get hashCode => Object.hash(runtimeType,processState);

@override
String toString() {
  return 'SignupOptionsState(processState: $processState)';
}


}

/// @nodoc
abstract mixin class _$SignupOptionsStateCopyWith<$Res> implements $SignupOptionsStateCopyWith<$Res> {
  factory _$SignupOptionsStateCopyWith(_SignupOptionsState value, $Res Function(_SignupOptionsState) _then) = __$SignupOptionsStateCopyWithImpl;
@override @useResult
$Res call({
 SignupOptionsProcessState processState
});


@override $SignupOptionsProcessStateCopyWith<$Res> get processState;

}
/// @nodoc
class __$SignupOptionsStateCopyWithImpl<$Res>
    implements _$SignupOptionsStateCopyWith<$Res> {
  __$SignupOptionsStateCopyWithImpl(this._self, this._then);

  final _SignupOptionsState _self;
  final $Res Function(_SignupOptionsState) _then;

/// Create a copy of SignupOptionsState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? processState = null,}) {
  return _then(_SignupOptionsState(
processState: null == processState ? _self.processState : processState // ignore: cast_nullable_to_non_nullable
as SignupOptionsProcessState,
  ));
}

/// Create a copy of SignupOptionsState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SignupOptionsProcessStateCopyWith<$Res> get processState {
  
  return $SignupOptionsProcessStateCopyWith<$Res>(_self.processState, (value) {
    return _then(_self.copyWith(processState: value));
  });
}
}

/// @nodoc
mixin _$SignupOptionsProcessState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SignupOptionsProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'SignupOptionsProcessState()';
}


}

/// @nodoc
class $SignupOptionsProcessStateCopyWith<$Res>  {
$SignupOptionsProcessStateCopyWith(SignupOptionsProcessState _, $Res Function(SignupOptionsProcessState) __);
}


/// @nodoc


class SignupOptionsInitialState implements SignupOptionsProcessState {
  const SignupOptionsInitialState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SignupOptionsInitialState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'SignupOptionsProcessState.initial()';
}


}




/// @nodoc


class SignupOptionsLoadingState implements SignupOptionsProcessState {
  const SignupOptionsLoadingState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SignupOptionsLoadingState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'SignupOptionsProcessState.loading()';
}


}




/// @nodoc


class SignupOptionsErrorState implements SignupOptionsProcessState {
  const SignupOptionsErrorState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SignupOptionsErrorState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'SignupOptionsProcessState.error()';
}


}




// dart format on
