part of 'signup_options_bloc.dart';

@freezed
sealed class SignupOptionsState with _$SignupOptionsState {
  const factory SignupOptionsState({
    @Default(SignupOptionsProcessState.initial())
    SignupOptionsProcessState processState,
  }) = _SignupOptionsState;
}

@freezed
sealed class SignupOptionsProcessState with _$SignupOptionsProcessState {
  const factory SignupOptionsProcessState.initial() = SignupOptionsInitialState;
  const factory SignupOptionsProcessState.loading() = SignupOptionsLoadingState;
  const factory SignupOptionsProcessState.error() = SignupOptionsErrorState;
}
