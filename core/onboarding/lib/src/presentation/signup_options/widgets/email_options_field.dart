import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:onboarding/src/assets/assets.gen.dart' as trader;

class EmailOptionsField extends StatelessWidget {
  const EmailOptionsField({super.key, required this.emailController});
  final TextEditingController emailController;
  @override
  Widget build(BuildContext context) {
    final localization = EquitiLocalization.of(context);
    return DuploTextField(
      prefixIcon: trader.Assets.images.signupWithEmailIc.svg(),
      semanticsIdentifier: "signup_options_email_field",
      controller: emailController,
      hint: localization.onboarding_enterEmail,
      label: localization.onboarding_emailAddress,
      scrollPadding: const EdgeInsets.only(bottom: 100),
      onEditingComplete: () {
        FocusManager.instance.primaryFocus?.unfocus();
      },
    );
  }
}
