import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:onboarding/src/presentation/user_registration/bloc/user_registration_bloc.dart';
import 'package:onboarding/src/assets/assets.gen.dart' as onboarding;

/// User Registration Screen that follows the same UI design and layout as UserNamePage.
///
/// This screen provides a form for users to enter their first name and last name
/// for registration purposes. It follows the project's established patterns for
/// form handling, validation, and navigation.
class UserRegistrationScreen extends StatefulWidget {
  const UserRegistrationScreen({super.key});

  @override
  State<UserRegistrationScreen> createState() => _UserRegistrationScreenState();
}

class _UserRegistrationScreenState extends State<UserRegistrationScreen>
    with AutomaticKeepAliveClientMixin {
  final firstNameFocusNode = FocusNode();
  final TextEditingController firstNameController = TextEditingController();
  final lastNameFocusNode = FocusNode();
  final TextEditingController lastNameController = TextEditingController();

  @override
  void initState() {
    super.initState();
    firstNameFocusNode.requestFocus();
    firstNameFocusNode.addListener(firstNameFocusNodeListener);
    lastNameFocusNode.addListener(lastNameFocusNodeListener);
  }

  @override
  void dispose() {
    firstNameFocusNode.removeListener(firstNameFocusNodeListener);
    lastNameFocusNode.removeListener(lastNameFocusNodeListener);
    firstNameFocusNode.dispose();
    lastNameFocusNode.dispose();
    firstNameController.dispose();
    lastNameController.dispose();
    super.dispose();
  }

  void firstNameFocusNodeListener() {
    if (!firstNameFocusNode.hasFocus) {
      context.read<UserRegistrationBloc>().add(
        UserRegistrationEvent.validateFirstName(firstNameController.text),
      );
    }
  }

  void lastNameFocusNodeListener() {
    if (!lastNameFocusNode.hasFocus) {
      context.read<UserRegistrationBloc>().add(
        UserRegistrationEvent.validateLastName(lastNameController.text),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final localization = EquitiLocalization.of(context);
    final theme = DuploTheme.of(context);

    return Scaffold(
      backgroundColor: theme.background.bgPrimary,
      appBar: DuploAppBar(title: ""),
      body: SafeArea(
        top: false,
        child: BlocListener<UserRegistrationBloc, UserRegistrationState>(
          listener: (listenerContext, state) {
            // Handle registration process state changes
            final ps = state.processState;
            switch (ps) {
              case UserRegistrationErrorProcessState(): // todo (aakash): Show bottom sheet
              case UserRegistrationLoadingProcessState():
              case UserRegistrationSuccessProcessState():
              case UserRegistrationIdleProcessState():
                break;
            }
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20.0),
            child: Stack(
              children: [
                Semantics(
                  identifier: "user_registration_screen",
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 20),
                        DuploText(
                          text: localization.onboarding_tellUsYourName,
                          style: DuploTextStyles.of(context).textXl,
                          fontWeight: DuploFontWeight.semiBold,
                          color: theme.text.textPrimary,
                        ),
                        const SizedBox(height: 8),
                        DuploText(
                          text: localization.onboarding_enterFirstAndLastName,
                          style: DuploTextStyles.of(context).textSm,
                          fontWeight: DuploFontWeight.regular,
                          color: theme.text.textSecondary,
                          textAlign: TextAlign.start,
                        ),
                        const SizedBox(height: 25),
                        BlocBuilder<
                          UserRegistrationBloc,
                          UserRegistrationState
                        >(
                          buildWhen:
                              (previous, current) =>
                                  previous.firstNameErrorCode !=
                                  current.firstNameErrorCode,
                          builder: (builderContext, state) {
                            return DuploTextField(
                              semanticsIdentifier: "first_name_text_field",
                              controller: firstNameController,
                              focusNode: firstNameFocusNode,
                              errorMessage: mapError(state.firstNameErrorCode),
                              hint: localization.onboarding_enterFirstName,
                              suffixIcon: Tooltip(
                                enableFeedback: true,
                                triggerMode: TooltipTriggerMode.tap,
                                message:
                                    localization.onboarding_enterLegalFirstName,
                                child:
                                    onboarding.Assets.images.emailInfoIc.svg(),
                              ),
                              label: localization.onboarding_firstName,
                              onChanged: (value) {
                                context.read<UserRegistrationBloc>().add(
                                  const UserRegistrationEvent.clearFirstNameErrorMessage(),
                                );
                                context.read<UserRegistrationBloc>().add(
                                  UserRegistrationEvent.validateFirstName(
                                    value,
                                  ),
                                );
                              },
                            );
                          },
                        ),
                        const SizedBox(height: 15),
                        BlocBuilder<
                          UserRegistrationBloc,
                          UserRegistrationState
                        >(
                          buildWhen:
                              (previous, current) =>
                                  previous.lastNameErrorCode !=
                                  current.lastNameErrorCode,
                          builder: (builderContext, state) {
                            return DuploTextField(
                              semanticsIdentifier: "last_name_text_field",
                              focusNode: lastNameFocusNode,
                              controller: lastNameController,
                              hint: localization.onboarding_enterLastName,
                              errorMessage: mapError(state.lastNameErrorCode),
                              suffixIcon: Tooltip(
                                enableFeedback: true,
                                triggerMode: TooltipTriggerMode.tap,
                                message:
                                    localization.onboarding_enterLegalLastName,
                                child:
                                    onboarding.Assets.images.emailInfoIc.svg(),
                              ),
                              label: localization.onboarding_lastName,
                              onChanged: (value) {
                                context.read<UserRegistrationBloc>().add(
                                  const UserRegistrationEvent.clearLastNameErrorMessage(),
                                );
                                context.read<UserRegistrationBloc>().add(
                                  UserRegistrationEvent.validateLastName(value),
                                );
                              },
                            );
                          },
                        ),
                        const SizedBox(height: 100),
                      ],
                    ),
                  ),
                ),
                Align(
                  alignment: Alignment.bottomCenter,
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 20),
                    color: theme.background.bgPrimary,
                    child: BlocBuilder<
                      UserRegistrationBloc,
                      UserRegistrationState
                    >(
                      buildWhen:
                          (previous, current) =>
                              (previous.isFirstNameValid !=
                                      current.isFirstNameValid ||
                                  previous.isLastNameValid !=
                                      current.isLastNameValid) ||
                              (previous.processState != current.processState),
                      builder: (builderContext, state) {
                        final isLoading =
                            state.processState
                                is UserRegistrationLoadingProcessState;

                        return SizedBox(
                          width: MediaQuery.sizeOf(context).width,
                          child: DuploButton.defaultPrimary(
                            semanticsIdentifier: "continue_button",
                            isDisabled: !state.isSubmitButtonEnabled,
                            title: localization.onboarding_continueText,
                            trailingIcon:
                                Assets.images
                                    .chevronRightDirectional(context)
                                    .keyName,
                            loadingText: localization.onboarding_loading,
                            isLoading: isLoading,
                            onTap: () {
                              context.read<UserRegistrationBloc>().add(
                                const UserRegistrationEvent.submitRegistration(),
                              );
                            },
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;

  /// Maps error codes to localized error messages.
  ///
  /// This follows the comprehensive validation requirements with specific error messages.
  String? mapError(NameErrorCode? errorCode) {
    switch (errorCode) {
      // Required field validation errors
      case NameErrorCode.enterFirstNameError:
        return 'First name is required. Please enter your first name.';
      case NameErrorCode.enterLastNameError:
        return 'Last name is required. Please enter your last name.';

      // Character validation errors
      case NameErrorCode.invalidCharactersError:
        return 'Name contains invalid characters. Please try again.';

      // Consecutive character validation errors
      case NameErrorCode.consecutiveHyphensError:
        return 'Name contains consecutive hyphens. Please try again.';
      case NameErrorCode.consecutiveApostrophesError:
        return 'Name contains consecutive apostrophes. Please try again.';
      case NameErrorCode.consecutiveSpacesError:
        return 'Name contains consecutive spaces. Please try again.';

      // Length validation errors
      case NameErrorCode.nameTooShortError:
        return 'Name is too short. Please try again.';
      case NameErrorCode.nameTooLongError:
        return 'Name is too long. Please try again.';

      case null:
        return null;
    }
  }
}
