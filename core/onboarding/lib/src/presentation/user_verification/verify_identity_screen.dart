import 'dart:io';

import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:onboarding/src/assets/assets.gen.dart' as onboarding;
import 'package:onboarding/src/di/di_container.dart';
import 'package:onboarding/src/presentation/user_verification/bloc/user_verification_bloc.dart';

class VerifyIdentityScreen extends StatelessWidget {
  const VerifyIdentityScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final locale = Localizations.localeOf(context).toString();
    final localization = EquitiLocalization.of(context);
    final isRunningTest = Platform.environment.containsKey('FLUTTER_TEST');
    return BlocProvider(
      create: (createContext) => diContainer<UserVerificationBloc>(),
      child: BlocBuilder<UserVerificationBloc, UserVerificationState>(
        buildWhen: (previous, current) => previous != current,
        builder: (builderContext, state) {
          final isLoading = switch (state.state) {
            UserVerificationProgressStateLoading() => true,
            _ => false,
          };
          return Scaffold(
            backgroundColor: theme.background.bgPrimary,
            body: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Expanded(
                  flex: 50,
                  // todo add dark mode lottie
                  child:
                      isRunningTest
                          ? SizedBox()
                          : DuploLottieView.asset(
                            lightAnimation:
                                onboarding.Assets.lotties.verifyYourIdentity,
                            darkAnimation:
                                onboarding.Assets.lotties.verifyYourIdentity,
                          ),
                ),
                Expanded(
                  flex: 50,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: Column(
                      children: [
                        Spacer(flex: 1),
                        DuploText(
                          text: localization.onboarding_verifyYourIdentity,
                          style: DuploTextStyles.of(context).textXl,
                          fontWeight: DuploFontWeight.semiBold,
                          color: theme.text.textPrimary,
                        ),
                        SizedBox(height: 16),
                        DuploText(
                          text:
                              localization
                                  .onboarding_verifyIdentitySecurityMessage,
                          style: DuploTextStyles.of(context).textSm,
                          textAlign: TextAlign.center,
                          color: theme.text.textSecondary,
                        ),
                        Spacer(flex: 4),
                        DuploButton.defaultPrimary(
                          semanticsIdentifier: "verify_identity_button",
                          title: localization.onboarding_continueButton,
                          onTap: () {
                            builderContext.read<UserVerificationBloc>().add(
                              UserVerificationEvent.onContinuePressed(
                                theme: theme,
                                locale: locale,
                              ),
                            );
                          },
                          useFullWidth: true,
                          isLoading: isLoading,
                        ),
                        Spacer(flex: 1),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
