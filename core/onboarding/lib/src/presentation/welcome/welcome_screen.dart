import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:onboarding/src/assets/assets.gen.dart' as onBoarding;
import 'package:onboarding/src/di/di_container.dart';
import 'package:onboarding/src/presentation/welcome/bloc/welcome_bloc.dart';

import 'widgets/onboard_page.dart';

class WelcomeScreen extends StatefulWidget {
  const WelcomeScreen();

  @override
  State<WelcomeScreen> createState() => _WelcomeScreenState();
}

class _WelcomeScreenState extends State<WelcomeScreen> {
  final PageController _controller = PageController();
  int _currentPage = 0;

  List<Widget> _pages = [];
  bool _isManualSwipe = false;
  bool _isAnimating = false;
  @override
  void didChangeDependencies() {
    _pages = [
      OnboardPage(
        title: EquitiLocalization.of(context).onboardingWelcomeTitle,
        description:
            EquitiLocalization.of(context).onboardingWelcomeDescription,
        lottieAnimationLight: onBoarding.Assets.lotties.welcomeLottieLight1,
        lottieAnimationDark: onBoarding.Assets.lotties.welcomeLottieDark1,
        onAnimationCompleted: _goToNextPage,
      ),
      OnboardPage(
        title: EquitiLocalization.of(context).onboardingTradeTitle,
        description: EquitiLocalization.of(context).onboardingTradeDescription,
        lottieAnimationLight: onBoarding.Assets.lotties.welcomeLottieLight2,
        lottieAnimationDark: onBoarding.Assets.lotties.welcomeLottieDark2,
        onAnimationCompleted: _goToNextPage,
      ),
      OnboardPage(
        title: EquitiLocalization.of(context).onboardingToolsTitle,
        description: EquitiLocalization.of(context).onboardingToolsDescription,
        lottieAnimationLight: onBoarding.Assets.lotties.welcomeLottieLight3,
        lottieAnimationDark: onBoarding.Assets.lotties.welcomeLottieDark3,
        onAnimationCompleted: _goToNextPage,
      ),
      OnboardPage(
        title: EquitiLocalization.of(context).onboardingFundingTitle,
        description:
            EquitiLocalization.of(context).onboardingFundingDescription,
        lottieAnimationLight: onBoarding.Assets.lotties.welcomeLottieLight4,
        lottieAnimationDark: onBoarding.Assets.lotties.welcomeLottieDark4,
        onAnimationCompleted: _goToNextPage,
      ),
      OnboardPage(
        title: EquitiLocalization.of(context).onboardingSecureTitle,
        description: EquitiLocalization.of(context).onboardingSecureDescription,
        lottieAnimationLight: onBoarding.Assets.lotties.welcomeLottieLight5,
        lottieAnimationDark: onBoarding.Assets.lotties.welcomeLottieDark5,
        onAnimationCompleted: _goToNextPage,
      ),
    ];
    super.didChangeDependencies();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _goToNextPage() {
    if (_isManualSwipe) {
      _isManualSwipe = false;
      return;
    }

    if (_currentPage < _pages.length - 1) {
      _isAnimating = true;
      _controller
          .animateToPage(
            _currentPage + 1,
            duration: Duration(milliseconds: 500),
            curve: Curves.easeInOut,
          )
          .then((_) {
            _isAnimating = false;
          });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    return BlocProvider(
      create: (createContext) => diContainer<WelcomeBloc>(),
      child: BlocBuilder<WelcomeBloc, WelcomeState>(
        buildWhen: (previous, current) => previous != current,
        builder: (builderContext, state) {
          return Scaffold(
            backgroundColor: theme.background.bgPrimary,
            body: SafeArea(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  SizedBox(height: 10),
                  Align(
                    alignment: AlignmentDirectional.topEnd,
                    child: Padding(
                      padding: EdgeInsets.only(right: 10.0),
                      child: DuploLanguageSelector(),
                    ),
                  ),
                  Expanded(
                    flex: 85,
                    child: PageView.builder(
                      controller: _controller,
                      itemCount: _pages.length,
                      onPageChanged: (index) {
                        if (!_isAnimating) {
                          _isManualSwipe = true;
                        }
                        setState(() => _currentPage = index);
                      },
                      itemBuilder: (_, index) => _pages[index],
                    ),
                  ),
                  SizedBox(height: 30),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: List.generate(
                      _pages.length,
                      (index) => Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: Container(
                          width: 10,
                          height: 10,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color:
                                _currentPage == index
                                    ? theme.foreground.fgBrandPrimaryAlt
                                    : theme.background.bgQuaternary,
                          ),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(height: 50),
                  Expanded(
                    flex: 15,
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Spacer(flex: 8),
                        Expanded(
                          flex: 40,
                          child: DuploButton.defaultPrimary(
                            semanticsIdentifier: "signup_button",
                            title:
                                EquitiLocalization.of(
                                  context,
                                ).onboarding_signup,
                            onTap: () {
                              builderContext.read<WelcomeBloc>().add(
                                const WelcomeEvent.navigateToSignUpOptions(),
                              );
                            },
                          ),
                        ),
                        const SizedBox(width: 10),
                        Expanded(
                          flex: 40,
                          child: DuploButton.secondary(
                            semanticsIdentifier: "login_button",
                            title:
                                EquitiLocalization.of(context).onboarding_login,
                            onTap: () {
                              builderContext.read<WelcomeBloc>().add(
                                const WelcomeEvent.navigateToLoginOptions(),
                              );
                            },
                          ),
                        ),
                        const Spacer(flex: 8),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
