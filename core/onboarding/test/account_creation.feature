import 'package:equiti_test/equiti_test.dart';
import 'package:onboarding/onboarding.dart';
import 'package:onboarding/src/domain/enums/account_creation_enums.dart';
import 'package:domain/domain.dart';
import 'data/create_account_test_data.dart';


Feature: Account Creation
   @testMethodName: testGoldens 
   Scenario: show account creation banner screen
        Given The {CreateAccountBannerScreen()} app is rendered
        Then screenshot verified {'create_account_banner_screen'}

   #@testMethodName: testGoldens 
   #Scenario: pageview test
   #    Given The {CreateAccountMainScreen(openedThroughBannerPage:true)} app is rendered
   #    Then screenshot verified {'choose_platform_page'}
   #    Then I tap {"Continue"} text
   #    Then screenshot verified {'choose_account_page'}
   #    Then I tap {"Continue"} text
   #    Then screenshot verified {'choose_account_type_page'}
   #    Then I tap {"Continue"} text
   #    Then screenshot verified {'choose_leverage_page'}
   #    Then I tap {"Continue"} text
   #    Then screenshot verified {'choose_currency_page'}
   #    Then I tap {"Continue"} text
   #    Then screenshot verified {'choose_nickname_page'}
   #    Then I enter {"Shubham"} in the {"Enter a nickname"} text field  
   #    Then I tap {"Continue"} text
   #    Then screenshot verified {'choose_password_page'}
   #    Then I enter {"Hello@123"} in the {"Enter password"} text field
   #    Then I tap {"Continue"} text
   #    Then screenshot verified {'account_creation_error_bottomsheet'}   

    @testMethodName: testGoldens 
    Scenario: show account successful screen
        Given The {AccountSuccessfulScreen(data: kAccountCreationReqModel,createAccountFlow: CreateAccountFlow.additionalLiveAccount)} app is rendered
        Then screenshot verified {'account_successful_screen'}