// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: unused_import, directives_ordering

import 'package:equiti_test/equiti_test.dart';
import 'package:onboarding/onboarding.dart';
import 'package:onboarding/src/domain/enums/account_creation_enums.dart';
import 'package:domain/domain.dart';
import 'data/create_account_test_data.dart';

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import './bdd_hooks/hooks.dart';
import './step/the_app_is_rendered.dart';
import 'package:bdd_steps/step/screenshot_verified.dart';

void main() {
  setUpAll(() async {
    await Hooks.beforeAll();
  });
  tearDownAll(() async {
    await Hooks.afterAll();
  });

  group('''Account Creation''', () {
    Future<void> beforeEach(String title, [List<String>? tags]) async {
      await Hooks.beforeEach(title, tags);
    }

    Future<void> afterEach(
      String title,
      bool success, [
      List<String>? tags,
    ]) async {
      await Hooks.afterEach(title, success, tags);
    }

    testGoldens('''show account creation banner screen''', (tester) async {
      var success = true;
      try {
        await beforeEach('''show account creation banner screen''');
        await theAppIsRendered(tester, CreateAccountBannerScreen());
        await screenshotVerified(tester, 'create_account_banner_screen');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''show account creation banner screen''', success);
      }
    });
    testGoldens('''show account successful screen''', (tester) async {
      var success = true;
      try {
        await beforeEach('''show account successful screen''');
        await theAppIsRendered(
          tester,
          AccountSuccessfulScreen(
            data: kAccountCreationReqModel,
            createAccountFlow: CreateAccountFlow.additionalLiveAccount,
          ),
        );
        await screenshotVerified(tester, 'account_successful_screen');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''show account successful screen''', success);
      }
    });
  });
}
