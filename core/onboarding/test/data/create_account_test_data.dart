import 'package:domain/domain.dart';
import 'package:flutter/widgets.dart';
import 'package:onboarding/src/domain/enums/account_creation_enums.dart';
import 'package:onboarding/src/domain/model/account_creation/account_creation_request_model.dart';
import 'package:onboarding/src/presentation/account_creation/create_account_main_screen.dart';

const kAccountCreationReqModel = AccountCreationRequestModel(
  accountNickname: 'Test Account',
  currency: 'AED',
  leverage: '400',
  password: 'Test@123',
  platform: AccountCreationPlatform.dulcimer,
  platformAccountType: AccountCreationType.standard,
  swapFreeAccount: true,
);

Widget createAccountMainScreen(CreateAccountFlow flow) {
  return CreateAccountMainScreen(createAccountFlow: flow);
}
