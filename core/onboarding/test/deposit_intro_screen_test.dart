// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: unused_import, directives_ordering

import 'package:equiti_test/equiti_test.dart';
import 'package:onboarding/src/presentation/first_deposit/deposit_intro_screen.dart';

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import './bdd_hooks/hooks.dart';
import './step/the_app_is_rendered.dart';
import 'package:bdd_steps/step/screenshot_verified.dart';

void main() {
  setUpAll(() async {
    await Hooks.beforeAll();
  });
  tearDownAll(() async {
    await Hooks.afterAll();
  });

  group('''Deposit Intro Screen Golden Tests''', () {
    Future<void> beforeEach(String title, [List<String>? tags]) async {
      await Hooks.beforeEach(title, tags);
    }

    Future<void> afterEach(
      String title,
      bool success, [
      List<String>? tags,
    ]) async {
      await Hooks.afterEach(title, success, tags);
    }

    testGoldens('''User views deposit intro screen''', (tester) async {
      var success = true;
      try {
        await beforeEach('''User views deposit intro screen''');
        await theAppIsRendered(tester, DepositIntroScreen());
        await screenshotVerified(tester, 'deposit_intro_screen');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''User views deposit intro screen''', success);
      }
    });
  });
}
