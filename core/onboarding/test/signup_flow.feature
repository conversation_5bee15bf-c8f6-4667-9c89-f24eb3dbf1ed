import 'package:equiti_test/equiti_test.dart';
import 'package:onboarding/onboarding.dart';
import 'scenarios/signup_flow_email_success.dart';

Feature: Signup Flow Screen

  @testMethodName: testGoldens
  Scenario: Signup Flow Email Screen Email Valid
    Given The {SignupFlow()} app is rendered
    Then I enter {"<EMAIL>"} in the field with identifier {"enter_email"}
    Then I wait for {3}
    Then screenshot verified {'signup_flow_email_screen_valid_email'}

  @testMethodName: testGoldens
  Scenario: Signup Flow Email Screen Email Invalid
    Given The {SignupFlow()} app is rendered
    Then I enter {"exxample.com"} in the field with identifier {"enter_email"}
    Then I wait for {3}
    Then screenshot verified {'signup_flow_email_screen_invalid_email'}

  # @testMethodName: testGoldens
  # Scenario: Signup Flow Email Screen Navigate To Password Screen
  #   Given The {SignupFlow()} app is rendered {scenarios:[signupEmailSuccess]}
  #   Then I enter {"<EMAIL>"} in the {"Enter email"} text field
  #   Then I wait for {4}
  #   Then I tap {"Continue"} text
  #   Then screenshot verified {'signup_flow_password_screen'}

  # @testMethodName: testGoldens
  # Scenario: Signup Flow Email Screen Navigate To Password Screen With Valid Password
  #   Given The {SignupFlow()} app is rendered {scenarios:[signupEmailSuccess]}
  #   Then I enter {"<EMAIL>"} in the {"Enter email"} text field
  #   Then I wait for {4}
  #   Then I tap {"Continue"} text
  #   Then I wait
  #   Then I enter {"Abcd@123"} in the {"Enter password"} text field
  #   Then screenshot verified {'signup_flow_password_screen_valid_password'}


  # @testMethodName: testGoldens
  # Scenario: Signup Flow Password Screen Navigate To User Name Screen
  #   Given The {SignupFlow()} app is rendered {scenarios:[signupEmailSuccess]}
  #   Then I enter {"<EMAIL>"} in the {"Enter email"} text field
  #   Then I wait for {4}
  #   Then I tap {"Continue"} text
  #   Then I wait
  #   Then I enter {"Abcd@123"} in the {"Enter password"} text field
  #   Then I wait
  #   Then I tap {"Continue"} text
  #   Then screenshot verified {'signup_flow_user_name_screen'}

  # @testMethodName: testGoldens
  # Scenario: Signup Flow Password Screen Navigate To User Name Screen Valid Name
  #   Given The {SignupFlow()} app is rendered {scenarios:[signupEmailSuccess]}
  #   Then I enter {"<EMAIL>"} in the {"Enter email"} text field
  #   Then I wait for {4}
  #   Then I tap {"Continue"} text
  #   Then I wait
  #   Then I enter {"Abcd@123"} in the {"Enter password"} text field
  #   Then I wait
  #   Then I tap {"Continue"} text
  #   Then I wait
  #   Then I enter {"Zaid"} in the {"Enter first name"} text field
  #   Then I enter {"Zaid"} in the {"Enter last name"} text field
  #   Then I wait for {2}
  #   Then screenshot verified {'signup_flow_user_name_screen_valid_name'}


