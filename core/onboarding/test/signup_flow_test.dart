// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: unused_import, directives_ordering

import 'package:equiti_test/equiti_test.dart';
import 'package:onboarding/onboarding.dart';
import 'scenarios/signup_flow_email_success.dart';

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import './bdd_hooks/hooks.dart';
import './step/the_app_is_rendered.dart';
import 'package:bdd_steps/step/i_enter_in_the_field_with_identifier.dart';
import './step/i_wait_for.dart';
import 'package:bdd_steps/step/screenshot_verified.dart';

void main() {
  setUpAll(() async {
    await Hooks.beforeAll();
  });
  tearDownAll(() async {
    await Hooks.afterAll();
  });

  group('''Signup Flow Screen''', () {
    Future<void> beforeEach(String title, [List<String>? tags]) async {
      await Hooks.beforeEach(title, tags);
    }

    Future<void> afterEach(
      String title,
      bool success, [
      List<String>? tags,
    ]) async {
      await Hooks.afterEach(title, success, tags);
    }

    testGoldens('''Signup Flow Email Screen Email Valid''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Signup Flow Email Screen Email Valid''');
        await theAppIsRendered(tester, SignupFlow());
        await iEnterInTheFieldWithIdentifier(
          tester,
          "<EMAIL>",
          "enter_email",
        );
        await iWaitFor(tester, 3);
        await screenshotVerified(
          tester,
          'signup_flow_email_screen_valid_email',
        );
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Signup Flow Email Screen Email Valid''', success);
      }
    });
    testGoldens('''Signup Flow Email Screen Email Invalid''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Signup Flow Email Screen Email Invalid''');
        await theAppIsRendered(tester, SignupFlow());
        await iEnterInTheFieldWithIdentifier(
          tester,
          "exxample.com",
          "enter_email",
        );
        await iWaitFor(tester, 3);
        await screenshotVerified(
          tester,
          'signup_flow_email_screen_invalid_email',
        );
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Signup Flow Email Screen Email Invalid''', success);
      }
    });
  });
}
