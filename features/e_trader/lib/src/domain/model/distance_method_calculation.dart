import 'package:decimal/decimal.dart';
import 'package:e_trader/src/domain/model/method_type.dart';
import 'package:e_trader/src/domain/model/order_limit_error_code.dart';
import 'package:e_trader/src/domain/model/order_limit_type.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';

extension DistanceMethodCalculation on DistanceMethod {
  double getProfitOrLoss({required Decimal distance}) {
    final result =
        (orderLimitType == OrderLimitType.takeProfit)
            ? distance * pipValue
            : distance * pipValue * Decimal.parse('-1');

    return result.toDouble();
  }

  Decimal getPrice({required Decimal limitPrice, required Decimal distance}) {
    final Decimal directionMultiplier = switch (orderLimitType) {
      OrderLimitType.takeProfit =>
        tradeType == TradeType.buy ? Decimal.parse('1') : Decimal.parse('-1'),
      OrderLimitType.stopLoss =>
        tradeType == TradeType.buy ? Decimal.parse('-1') : Decimal.parse('1'),
    };

    final priceAdjustment = distance * pipSize * directionMultiplier;
    final result = limitPrice + priceAdjustment;

    return result;
  }

  OrderLimitErrorCode? isValid({required Decimal distance}) =>
      distance > Decimal.parse('0')
          ? null
          : OrderLimitErrorCode.invalidDistance;
}
