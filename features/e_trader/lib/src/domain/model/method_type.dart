import 'package:decimal/decimal.dart';
import 'package:flutter/foundation.dart';
import 'package:e_trader/src/domain/model/distance_method_calculation.dart';
import 'package:e_trader/src/domain/model/order_limit_calculation.dart';
import 'package:e_trader/src/domain/model/order_limit_error_code.dart';
import 'package:e_trader/src/domain/model/order_limit_type.dart';
import 'package:e_trader/src/domain/model/price_method_calculation.dart';
import 'package:e_trader/src/domain/model/profit_or_loss_calculation.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'method_type.freezed.dart';

@freezed
sealed class MethodType with _$MethodType {
  const factory MethodType.distance({
    required Decimal pipValue,
    required Decimal pipSize,
    required Decimal currentPrice,
    required OrderLimitType orderLimitType,
    required TradeType tradeType,
  }) = DistanceMethod;

  const factory MethodType.price({
    required Decimal pipValue,
    required Decimal pipSize,
    required Decimal pipMultiplier,
    required Decimal currentPrice,
    required OrderLimitType orderLimitType,
    required TradeType tradeType,
  }) = PriceMethod;

  const factory MethodType.profitOrLoss({
    required Decimal pipValue,
    required Decimal pipSize,
    required Decimal currentPrice,
    required OrderLimitType orderLimitType,
    required TradeType tradeType,
  }) = ProfitOrLossMethod;
}

extension MethodTypeX on MethodType {
  OrderLimitCalculation calculate({
    required Decimal distance,
    required Decimal limitPrice,
    required Decimal profitOrLoss,
  }) {
    return switch (this) {
      DistanceMethod method => () {
        final currentPrice = method.currentPrice;

        final calculatedProfitOrLoss = Decimal.parse(
          (method.getProfitOrLoss(distance: distance)).toStringAsFixed(2),
        );
        final price = method.getPrice(
          limitPrice: currentPrice,
          distance: distance,
        );

        final result = OrderLimitCalculation(
          profitOrLoss: calculatedProfitOrLoss,
          price: price,
          distance: Decimal.parse((distance).toStringAsFixed(1)),
        );
        return result;
      }(),
      PriceMethod method => () {
        final price =
            limitPrice != Decimal.parse('0.0')
                ? limitPrice
                : method.getPrice(limitPrice: currentPrice, distance: distance);

        final calculatedDistance = method.getDistance(limitPrice: price);

        final calculatedProfitOrLoss = method.getProfitOrLoss(
          distance: calculatedDistance,
        );

        final result = OrderLimitCalculation(
          profitOrLoss: calculatedProfitOrLoss,
          price: price,
          distance: Decimal.parse(calculatedDistance.toStringAsFixed(1)),
        );

        return result;
      }(),
      ProfitOrLossMethod method => () {
        final calculatedProfitOrLoss = profitOrLoss;

        final calculatedDistance = method.getDistance(
          profitOrLoss: profitOrLoss,
        );
        final price = method.getPrice(
          limitPrice: currentPrice,
          distance: calculatedDistance,
        );

        return OrderLimitCalculation(
          profitOrLoss: calculatedProfitOrLoss,
          price: price,
          distance: Decimal.parse((calculatedDistance).toStringAsFixed(1)),
        );
      }(),
    };
  }

  OrderLimitErrorCode? isValid({required Decimal input}) {
    return switch (this) {
      DistanceMethod method => method.isValid(distance: input),
      PriceMethod method => method.isValid(limitPrice: input),
      ProfitOrLossMethod method => method.isValid(profitOrLoss: input),
    };
  }
}
