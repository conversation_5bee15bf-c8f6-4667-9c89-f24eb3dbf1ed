// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'order_limit_calculation.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$OrderLimitCalculation {

 Decimal get profitOrLoss; Decimal get price; Decimal get distance;
/// Create a copy of OrderLimitCalculation
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$OrderLimitCalculationCopyWith<OrderLimitCalculation> get copyWith => _$OrderLimitCalculationCopyWithImpl<OrderLimitCalculation>(this as OrderLimitCalculation, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is OrderLimitCalculation&&(identical(other.profitOrLoss, profitOrLoss) || other.profitOrLoss == profitOrLoss)&&(identical(other.price, price) || other.price == price)&&(identical(other.distance, distance) || other.distance == distance));
}


@override
int get hashCode => Object.hash(runtimeType,profitOrLoss,price,distance);

@override
String toString() {
  return 'OrderLimitCalculation(profitOrLoss: $profitOrLoss, price: $price, distance: $distance)';
}


}

/// @nodoc
abstract mixin class $OrderLimitCalculationCopyWith<$Res>  {
  factory $OrderLimitCalculationCopyWith(OrderLimitCalculation value, $Res Function(OrderLimitCalculation) _then) = _$OrderLimitCalculationCopyWithImpl;
@useResult
$Res call({
 Decimal profitOrLoss, Decimal price, Decimal distance
});




}
/// @nodoc
class _$OrderLimitCalculationCopyWithImpl<$Res>
    implements $OrderLimitCalculationCopyWith<$Res> {
  _$OrderLimitCalculationCopyWithImpl(this._self, this._then);

  final OrderLimitCalculation _self;
  final $Res Function(OrderLimitCalculation) _then;

/// Create a copy of OrderLimitCalculation
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? profitOrLoss = null,Object? price = null,Object? distance = null,}) {
  return _then(_self.copyWith(
profitOrLoss: null == profitOrLoss ? _self.profitOrLoss : profitOrLoss // ignore: cast_nullable_to_non_nullable
as Decimal,price: null == price ? _self.price : price // ignore: cast_nullable_to_non_nullable
as Decimal,distance: null == distance ? _self.distance : distance // ignore: cast_nullable_to_non_nullable
as Decimal,
  ));
}

}


/// @nodoc


class _OrderLimitCalculation implements OrderLimitCalculation {
  const _OrderLimitCalculation({required this.profitOrLoss, required this.price, required this.distance});
  

@override final  Decimal profitOrLoss;
@override final  Decimal price;
@override final  Decimal distance;

/// Create a copy of OrderLimitCalculation
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OrderLimitCalculationCopyWith<_OrderLimitCalculation> get copyWith => __$OrderLimitCalculationCopyWithImpl<_OrderLimitCalculation>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OrderLimitCalculation&&(identical(other.profitOrLoss, profitOrLoss) || other.profitOrLoss == profitOrLoss)&&(identical(other.price, price) || other.price == price)&&(identical(other.distance, distance) || other.distance == distance));
}


@override
int get hashCode => Object.hash(runtimeType,profitOrLoss,price,distance);

@override
String toString() {
  return 'OrderLimitCalculation(profitOrLoss: $profitOrLoss, price: $price, distance: $distance)';
}


}

/// @nodoc
abstract mixin class _$OrderLimitCalculationCopyWith<$Res> implements $OrderLimitCalculationCopyWith<$Res> {
  factory _$OrderLimitCalculationCopyWith(_OrderLimitCalculation value, $Res Function(_OrderLimitCalculation) _then) = __$OrderLimitCalculationCopyWithImpl;
@override @useResult
$Res call({
 Decimal profitOrLoss, Decimal price, Decimal distance
});




}
/// @nodoc
class __$OrderLimitCalculationCopyWithImpl<$Res>
    implements _$OrderLimitCalculationCopyWith<$Res> {
  __$OrderLimitCalculationCopyWithImpl(this._self, this._then);

  final _OrderLimitCalculation _self;
  final $Res Function(_OrderLimitCalculation) _then;

/// Create a copy of OrderLimitCalculation
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? profitOrLoss = null,Object? price = null,Object? distance = null,}) {
  return _then(_OrderLimitCalculation(
profitOrLoss: null == profitOrLoss ? _self.profitOrLoss : profitOrLoss // ignore: cast_nullable_to_non_nullable
as Decimal,price: null == price ? _self.price : price // ignore: cast_nullable_to_non_nullable
as Decimal,distance: null == distance ? _self.distance : distance // ignore: cast_nullable_to_non_nullable
as Decimal,
  ));
}


}

// dart format on
