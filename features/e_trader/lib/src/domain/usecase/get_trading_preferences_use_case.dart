import 'package:e_trader/src/domain/repository/trader_account_preferences_repository.dart';

class GetTradingPreferencesUseCase {
  final TraderAccountPreferencesRepository preferences;

  const GetTradingPreferencesUseCase({required this.preferences});

  String getLeverage() {
    return preferences.getValue('leverage', '100');
  }

  bool getQuickTrade() {
    return preferences.getValue('quickTrade_isOn', false);
  }

  bool getTPSL() {
    return preferences.getValue('tpsl_isOn', false);
  }

  bool getDealSize() {
    return preferences.getValue('dealSize_isOn', false);
  }
}
