import 'dart:io';

import 'package:duplo/duplo.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;
import 'package:e_trader/src/data/api/platform_type.dart';
import 'package:e_trader/src/data/api/trading_socket_event.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/domain/usecase/update_trading_account_balance_hub_use_case.dart';
import 'package:e_trader/src/presentation/account/account_details/account_details_loading.dart';
import 'package:e_trader/src/presentation/account/bloc/account_details_bloc.dart';
import 'package:e_trader/src/presentation/duplo/primary_button.dart';
import 'package:e_trader/src/presentation/duplo/server_connection_details_widget.dart';
import 'package:e_trader/src/presentation/margin_level/widgets/margin_level_detail_item.dart';
import 'package:e_trader/src/presentation/margin_level/widgets/segment_progress_bar.dart';
import 'package:e_trader/src/presentation/model/margin_segment_level.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:monitoring/monitoring.dart';
import 'package:prelude/prelude.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:visibility_detector/visibility_detector.dart';

class AccountDetailsTab extends StatefulWidget {
  const AccountDetailsTab({super.key});

  @override
  State<AccountDetailsTab> createState() => _AccountDetailsTabContentState();
}

class _AccountDetailsTabContentState extends State<AccountDetailsTab>
    with PerformanceObserverMixin {
  bool _hasSubscribed = false;

  @override
  Widget build(BuildContext context) {
    final l10n = EquitiLocalization.of(context);
    final duploTextStyles = context.duploTextStyles;
    final theme = context.duploTheme;
    final locale = Localizations.localeOf(context).toString();
    final isIOS = Platform.isIOS;

    return VisibilityDetector(
      key: const Key('account_details_tab_content'),
      onVisibilityChanged: (info) {
        if (info.visibleFraction > 0) {
          if (!_hasSubscribed) {
            context.read<AccountDetailsBloc>().add(
              AccountDetailsEvent.updateTradingAccountBalance(
                TradingSocketEvent.accountBalance.subscribe,
              ),
            );
            _hasSubscribed = true;
          }
        } else {
          _unsubscribe();
        }
      },
      child: SizedBox(
        child: CustomScrollView(
          slivers: [
            SliverPadding(
              padding: const EdgeInsets.all(16.0),
              sliver: SliverMainAxisGroup(
                slivers: [
                  BlocConsumer<AccountDetailsBloc, AccountDetailsState>(
                    listenWhen:
                        (previous, current) =>
                            current != previous &&
                            current.accountProcessState
                                is AccountDetailsConnectedProcessState,
                    listener: (listenerContext, state) {
                      listenerContext.read<AccountDetailsBloc>().add(
                        AccountDetailsEvent.updateTradingAccountBalance(
                          TradingSocketEvent.accountBalance.subscribe,
                        ),
                      );
                    },
                    buildWhen: (previous, current) => current != previous,
                    builder:
                        (blocContext, state) => switch (state
                            .accountProcessState) {
                          AccountDetailsLoadingProcessState() =>
                            DuploShimmerList.sliver(
                              hasLeading: false,
                              itemCount: 5,
                              height: 16,
                            ),
                          AccountDetailsConnectedProcessState() ||
                          AccountDetailsSuccessProcessState() => () {
                            final marginInformation = [
                              KeyValuePair(
                                label: l10n.trader_accountFreeMargin,
                                value: EquitiFormatter.formatNumber(
                                  value:
                                      state.tradingAccountUpdates?.freeMargin ??
                                      0,
                                  locale: locale,
                                ),
                              ),
                              KeyValuePair(
                                label: l10n.trader_usedMargin,
                                value: EquitiFormatter.formatNumber(
                                  value:
                                      state.tradingAccountUpdates?.margin ?? 0,
                                  locale: locale,
                                ),
                              ),
                            ];
                            final balanceInformation = [
                              KeyValuePair(
                                label: l10n.trader_equity,
                                value: EquitiFormatter.formatNumber(
                                  value:
                                      state.tradingAccountUpdates?.equity ?? 0,
                                  locale: locale,
                                ),
                              ),
                              KeyValuePair(
                                label: l10n.trader_profit,
                                value: EquitiFormatter.formatNumber(
                                  value:
                                      state.tradingAccountUpdates?.profit ?? 0,
                                  locale: locale,
                                ),
                              ),
                              KeyValuePair(
                                label: l10n.trader_balance,
                                value: EquitiFormatter.formatNumber(
                                  value:
                                      state.tradingAccountUpdates?.balance ?? 0,
                                  locale: locale,
                                ),
                              ),
                              KeyValuePair(
                                label: l10n.trader_credit,
                                value: EquitiFormatter.formatNumber(
                                  value:
                                      state.tradingAccountUpdates?.credit ?? 0,
                                  locale: locale,
                                ),
                              ),
                            ];
                            return SliverMainAxisGroup(
                              slivers: [
                                SliverToBoxAdapter(
                                  child: DuploKeyValueDisplay(
                                    keyValuePairs: balanceInformation,
                                    keyTextStyle: duploTextStyles.textSm,
                                    valueTextStyle: duploTextStyles.textSm,
                                    addBorder: true,
                                    hideLastDivider: true,
                                  ),
                                ),
                                SliverToBoxAdapter(child: SizedBox(height: 16)),
                                SliverToBoxAdapter(
                                  child: DuploKeyValueDisplay(
                                    title: l10n.trader_margin,
                                    keyValuePairs: marginInformation,
                                    keyTextStyle: duploTextStyles.textSm,
                                    valueTextStyle: duploTextStyles.textSm,
                                    addBorder: true,
                                  ),
                                ),
                                SliverToBoxAdapter(
                                  child: DuploMarginProgressBar(
                                    marginLevel:
                                        state
                                            .tradingAccountUpdates
                                            ?.marginLevel ??
                                        0,
                                    balance:
                                        state.tradingAccountUpdates?.balance,
                                    margin: state.tradingAccountUpdates?.margin,
                                    showHeader: true,
                                    showMarginCall: true,
                                    showStopOut: true,
                                    onInfoPressed: () {
                                      showMarginLevelInformationDialog(
                                        l10n,
                                        duploTextStyles,
                                        theme,
                                      );
                                    },
                                  ),
                                ),
                              ],
                            );
                          }(),
                          AccountDetailsErrorProcessState() ||
                          AccountDetailsEmptyProcessState() => SliverFillRemaining(
                            hasScrollBody: false,
                            child: EmptyOrErrorStateComponent.error(
                              description: l10n.trader_errorDescription,
                              title: l10n.trader_somethingWentWrong,
                              svgImage: trader.Assets.images.bug.svg(),
                              retryButtonText: l10n.trader_reload,
                              onTapRetry:
                                  () => blocContext.read<AccountDetailsBloc>().add(
                                    AccountDetailsEvent.subscribeToTradingAccountBalance(),
                                  ),
                            ),
                          ),
                        },
                  ),
                  const SliverToBoxAdapter(child: SizedBox(height: 24)),
                  BlocBuilder<AccountDetailsBloc, AccountDetailsState>(
                    buildWhen:
                        (previous, current) =>
                            current.accountProcessState !=
                            previous.accountProcessState,
                    builder:
                        (blocContext, state) => switch (state
                            .accountProcessState) {
                          AccountDetailsLoadingProcessState() =>
                            SliverToBoxAdapter(child: AccountDetailsLoading()),
                          AccountDetailsSuccessProcessState() => () {
                            final accountDetails = [
                              KeyValuePair(
                                label: l10n.trader_platform,
                                value:
                                    state
                                        .accountdetails
                                        ?.platformType
                                        .displayName
                                        .capitalize() ??
                                    '',
                              ),
                              KeyValuePair(
                                label: l10n.trader_accountType,
                                value:
                                    state
                                        .accountdetails
                                        ?.platformAccountType
                                        .name
                                        .capitalize() ??
                                    '',
                              ),
                              KeyValuePair(
                                label: l10n.trader_leverage,
                                value:
                                    "1:" +
                                    EquitiFormatter.formatNumber(
                                      value:
                                          state.accountdetails?.leverage ?? 0,
                                      locale: locale,
                                    ),
                              ),
                              KeyValuePair(
                                label: l10n.trader_currency,
                                value: state.accountdetails?.homeCurrency ?? '',
                              ),
                              KeyValuePair(
                                label: l10n.trader_swapProfile,
                                value:
                                    (state.accountdetails?.isSwapFree ?? false)
                                        ? l10n.trader_free
                                        : l10n.trader_standard,
                              ),
                            ];
                            return SliverMainAxisGroup(
                              slivers: [
                                SliverToBoxAdapter(
                                  child: DuploKeyValueDisplay(
                                    title: l10n.trader_accountDetails,
                                    keyValuePairs: accountDetails,
                                    keyTextStyle: duploTextStyles.textSm,
                                    valueTextStyle: duploTextStyles.textSm,
                                    addBorder: true,
                                    hideLastDivider: true,
                                  ),
                                ),
                                SliverToBoxAdapter(child: SizedBox(height: 20)),
                                SliverToBoxAdapter(
                                  child: ServerConnectionDetailsWidget(
                                    tradingAccountModel: state.accountdetails!,
                                    title: DuploText(
                                      style: context.duploTextStyles.textLg,
                                      fontWeight: DuploFontWeight.semiBold,
                                      text:
                                          EquitiLocalization.of(
                                            context,
                                          ).trader_howToConnect,
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                      color: theme.text.textSecondary,
                                    ),
                                    subtitle: DuploText(
                                      text:
                                          EquitiLocalization.of(
                                            context,
                                          ).trader_howToConnectDescription,
                                      style: context.duploTextStyles.textXs,
                                      color: theme.text.textSecondary,
                                    ),
                                  ),
                                ),
                                SliverToBoxAdapter(child: SizedBox(height: 20)),
                                SliverToBoxAdapter(
                                  child: PrimaryButton(
                                    title:
                                        isIOS
                                            ? ("MT5 ") + l10n.trader_appStore
                                            : ("MT5 ") + l10n.trader_googlePlay,
                                    textColor: theme.button.buttonSecondaryFg,
                                    // ignore: avoid-passing-async-when-sync-expected
                                    onTap: () async {
                                      // if (state.accountdetails?.platformType ==
                                      //         PlatformType.mt5 ||
                                      //     state.accountdetails?.platformType ==
                                      //         PlatformType.mt4) {
                                      // final platformType =
                                      //     state.accountdetails!.platformType;
                                      final links = PlatformType.mt5.storeLinks;
                                      // platformType.storeLinks; // TODO check the server name aand how to handle dulcimer

                                      if (links != null) {
                                        final url =
                                            Platform.isIOS
                                                ? links.appStoreLink
                                                : links.playStoreLink;
                                        try {
                                          if (!await canLaunchUrl(url)) {
                                            await launchUrl(
                                              url,
                                              mode: LaunchMode.inAppBrowserView,
                                            );
                                          }
                                        } catch (e) {
                                          diContainer<LoggerBase>().logError(
                                            e,
                                            stackTrace: StackTrace.current,
                                          );
                                        }
                                      }
                                    },
                                    borderColor:
                                        theme.button.buttonSecondaryBorder,
                                    bgColor: theme.button.buttonSecondaryBg,
                                  ),
                                ),
                              ],
                            );
                          }(),
                          _ => SliverToBoxAdapter(child: SizedBox.shrink()),
                        },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void showMarginLevelInformationDialog(
    EquitiLocalization l10n,
    TextStyles duploTextStyles,
    DuploThemeData theme,
  ) {
    DuploDialog.showInfoDialog(
      context: context,
      title: l10n.trader_accountMarginLevel,
      description: '',
      content: (_) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            DuploText(
              style: duploTextStyles.textSm,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              color: theme.text.textTertiary,
              text: 'Margin level % = Equity / Total Margin',
              fontWeight: DuploFontWeight.semiBold,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            SegmentedProgressBar(
              isInDialog: true,
              segments: [
                ProgressSegment(
                  value: 30,
                  color: theme.utility.utilityError700,
                  label: '30%',
                ),
                ProgressSegment(
                  value: 70,
                  color: theme.foreground.fgErrorSecondary,
                  label: '100%',
                ),
                ProgressSegment(
                  value: 100,
                  color: theme.foreground.fgWarningSecondary,
                  label: '200%',
                ),
                ProgressSegment(
                  value: 100,
                  color: theme.foreground.fgSuccessSecondary,
                  label: '300%',
                ),
                ProgressSegment(
                  value: 20,
                  color: theme.foreground.fgBrandSecondary,
                  label: '',
                ),
              ],
            ),
            const SizedBox(height: 24),
            Divider(color: theme.border.borderSecondary),
            Column(
              children: [
                MarginLevelDetailItem(
                  title: 'Protected',
                  subTitle: 'Well protected with high a margin level available',
                  segmentLevel: MarginSegmentLevel.protected,
                ),
                Divider(color: theme.border.borderSecondary),
                MarginLevelDetailItem(
                  title: 'Safe',
                  subTitle: 'Margin is healthy, low risk and safe for trading',
                  segmentLevel: MarginSegmentLevel.safe,
                ),
                Divider(color: theme.border.borderSecondary),
                MarginLevelDetailItem(
                  title: 'Caution',
                  subTitle:
                      'Monitor you positions & consider reducing exposure',
                  segmentLevel: MarginSegmentLevel.caution,
                ),
                Divider(color: theme.border.borderSecondary),
                MarginLevelDetailItem(
                  title: 'Margin Call',
                  subTitle: 'Add funds or close positions to avoid liquidation',
                  segmentLevel: MarginSegmentLevel.marginCall,
                ),
                Divider(color: theme.border.borderSecondary),
                MarginLevelDetailItem(
                  title: 'Stop Out',
                  subTitle: 'Forced liquidation will be applied',
                  segmentLevel: MarginSegmentLevel.stopOut,
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  @override
  void onRoutePopped(Route<Object?> route) {
    _subscribe();
  }

  @override
  void onRoutePushed(Route<Object?> route) {
    _unsubscribe();
  }

  void _subscribe() {
    if (!_hasSubscribed) {
      if (mounted)
        context.read<AccountDetailsBloc>().add(
          AccountDetailsEvent.updateTradingAccountBalance(
            TradingSocketEvent.accountBalance.subscribe,
          ),
        );
      _hasSubscribed = true;
    }
  }

  void _unsubscribe() {
    if (_hasSubscribed) {
      try {
        diContainer<UpdateTradingAccountBalanceHubUseCase>().call(
          eventType: TradingSocketEvent.accountBalance.unsubscribe,
        );
      } catch (e) {
        diContainer<LoggerBase>().logError(e, stackTrace: StackTrace.current);
      }
      _hasSubscribed = false;
    }
  }
}
