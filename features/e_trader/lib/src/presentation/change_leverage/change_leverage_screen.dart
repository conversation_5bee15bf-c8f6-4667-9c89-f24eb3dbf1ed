import 'package:duplo/duplo.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/presentation/change_leverage/bloc/change_leverage_bloc.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:prelude/prelude.dart';

class ChangeLeverageScreen extends StatelessWidget {
  final String accountNumber;
  const ChangeLeverageScreen({super.key, required this.accountNumber});

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final duploTextStyles = context.duploTextStyles;
    final localization = EquitiLocalization.of(context);

    return Container(
      child: BlocProvider(
        create:
            (createContext) =>
                diContainer<ChangeLeverageBloc>()..add(
                  ChangeLeverageEvent.onGetLeverages(
                    accountNumber: accountNumber,
                  ),
                ),
        child: BlocConsumer<ChangeLeverageBloc, ChangeLeverageState>(
          buildWhen:
              (previous, current) =>
                  previous.changeLeverage != current.changeLeverage,
          listenWhen:
              (previous, current) =>
                  previous.changeLeverage != current.changeLeverage,
          listener: (listenerContext, state) {
            if (state.changeLeverage is ChangeLeverageSuccess) {
              Navigator.of(listenerContext).pop();
            }
            if (state.changeLeverage is ChangeLeverageError) {
              final toast = DuploToast();

              toast.showToastMessage(
                context: listenerContext,
                widget: DuploToastMessage(
                  titleMessage: localization.trader_somethingWentWrong,
                  descriptionMessage:
                      localization.trader_unable_to_change_leverage,

                  messageType: ToastMessageType.error,
                  onLeadingAction: () {
                    toast.hidesToastMessage();
                  },
                ),
              );
            }
          },
          builder: (builder1Context, state1) {
            return BlocBuilder<ChangeLeverageBloc, ChangeLeverageState>(
              buildWhen:
                  (previous, current) =>
                      previous.processState != current.processState,
              builder: (builderContext, state) {
                return switch (state.processState) {
                  ChangeLeverageLoading() => Center(
                    child: CircularProgressIndicator(),
                  ),
                  ChangeLeverageSuccess() => () {
                    final allOptions = <SelectionOptionModel>[];

                    state.leverages.forEach((element) {
                      final model = SelectionOptionModel(
                        displayText:
                            "1:${EquitiFormatter.formatNumber(value: element, locale: Localizations.localeOf(context).toString())}",
                        identifier: EquitiFormatter.formatNumber(
                          value: element,
                          locale: Localizations.localeOf(context).toString(),
                        ),
                      );

                      allOptions.add(model);
                    });

                    return TextSelectionComponentScreen(
                      pageTitle: "",
                      buttonTitle: EquitiLocalization.of(context).trader_save,
                      options: allOptions,
                      selected:
                          null, //TODO : pass the initial value when we have that value from BE
                      onSelection: (option) {
                        final loc = Localizations.localeOf(builderContext);
                        builderContext.read<ChangeLeverageBloc>().add(
                          ChangeLeverageEvent.onChangeLeverages(
                            selectedLeverage: EquitiFormatter.formatNumber(
                              value:
                                  state.leverages
                                      .where(
                                        (element) =>
                                            element ==
                                            int.parse(option.identifier),
                                      )
                                      .firstOrNull ??
                                  0,
                              locale: loc.toString(),
                            ),
                            accountNumber: accountNumber,
                          ),
                        );
                      },
                    );
                  }(),
                  ChangeLeverageError() => Center(
                    child: DuploText(
                      text: localization.trader_somethingWentWrong,
                      style: duploTextStyles.textMd,
                      color: theme.text.textPrimary,
                    ),
                  ),
                };
              },
            );
          },
        ),
      ),
    );
  }
}
