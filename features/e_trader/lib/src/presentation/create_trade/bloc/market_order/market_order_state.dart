part of 'market_order_bloc.dart';

@unfreezed
// ignore: prefer-immutable-bloc-state
sealed class MarketOrderState with _$MarketOrderState {
  const MarketOrderState._();
  factory MarketOrderState({
    required String accountNumber,
    required MarginInformationModel? marginInformation,
    required SymbolQuoteModel symbolQuoteModel,
    required TradeComponentState<double, OrderSizeErrorCode> orderSizeState,
    required TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode>
    takeProfitState,
    required TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode>
    stopLossState,
    required TradeComponentState<void, TradeError> marginAllocationState,
    required TradeType? tradeType,
    required String symbolCode,
    required double openPrice,
    @Default(MarketOrderProcessState.loading())
    MarketOrderProcessState currentState,
  }) = _MarketOrderState;

  double get currentPrice => switch (tradeType) {
    TradeType.buy => symbolQuoteModel.ask,
    TradeType.sell => symbolQuoteModel.bid,
    null => 0.0,
  };

  double get takeProfit =>
      takeProfitState.isActive() ? takeProfitState.value.price.toDouble() : 0.0;
  double get stopLoss =>
      stopLossState.isActive() ? stopLossState.value.price.toDouble() : 0.0;

  // BuySellButtonState buyButtonState() {
  //   if (tradeType == TradeType.buy) {
  //     return BuySellButtonState.selected(symbolQuoteModel.ask);
  //   }
  //   return BuySellButtonState.active(symbolQuoteModel.ask);
  // }

  // BuySellButtonState sellButtonState() {
  //   if (tradeType == TradeType.sell) {
  //     return BuySellButtonState.selected(symbolQuoteModel.bid);
  //   }
  //   return BuySellButtonState.active(symbolQuoteModel.bid);
  // }

  bool isValid() {
    if (marginInformation == null) return false;
    if (tradeType == null) return false;
    final requiredComponentsValid = [
      orderSizeState,
      marginAllocationState,
    ].every((state) => state.isActive() && state.isValid());
    final optionalComponentsValid = [
      takeProfitState,
      stopLossState,
    ].where((state) => state.isActive()).every((state) => state.isValid());
    return requiredComponentsValid && optionalComponentsValid;
  }
}

@freezed
sealed class MarketOrderProcessState with _$MarketOrderProcessState {
  const factory MarketOrderProcessState.loading() =
      MarketOrderLoadingProcessState;
  const factory MarketOrderProcessState.connected() =
      MarketOrderConnectedProcessState;
  const factory MarketOrderProcessState.disconnected() =
      MarketOrderDisconnectedProcessState;
  const factory MarketOrderProcessState.placingOrder() =
      PlacingMarketOrderProcessState;
  const factory MarketOrderProcessState.orderSuccess() =
      MarketOrderSuccessProcessState;
  const factory MarketOrderProcessState.orderError({String? errorCode}) =
      MarketOrderErrorProcessState;
  const factory MarketOrderProcessState.marketClosed() =
      MarketOrderMarketClosedProcessState;
}
