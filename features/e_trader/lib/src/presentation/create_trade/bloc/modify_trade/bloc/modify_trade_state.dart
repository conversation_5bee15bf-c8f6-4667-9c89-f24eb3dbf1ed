// ignore_for_file: prefer-immutable-bloc-state

part of 'modify_trade_bloc.dart';

@unfreezed
sealed class ModifyTradeState with _$ModifyTradeState {
  ModifyTradeState._();
  factory ModifyTradeState({
    required PositionModel positionModel,
    MarginInformationModel? marginInformation,
    String? accountNumber,
    @Default(
      TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode>.loading(),
    )
    TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode>
    takeProfitState,
    @Default(
      TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode>.loading(),
    )
    TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode>
    stopLossState,
    @Default(ModifyTradeProcessState.loading())
    ModifyTradeProcessState currentState,
  }) = _ModifyTradeState;

  double get takeProfit =>
      takeProfitState.isActive() ? takeProfitState.value.price.toDouble() : 0.0;
  double get stopLoss =>
      stopLossState.isActive() ? stopLossState.value.price.toDouble() : 0.0;

  bool isValid() {
    final optionalComponentsValid = [
      takeProfitState,
      stopLossState,
    ].where((state) => state.isActive()).every((state) => state.isValid());
    return optionalComponentsValid;
  }
}

@freezed
sealed class ModifyTradeProcessState with _$ModifyTradeProcessState {
  const factory ModifyTradeProcessState.loading() =
      ModifyTradeLoadingProcessState;
  const factory ModifyTradeProcessState.connected() =
      ModifyTradeConnectedProcessState;
  const factory ModifyTradeProcessState.disconnected() =
      ModifyTradeDisconnectedProcessState;
  const factory ModifyTradeProcessState.placingOrder() =
      ModifyPlacingTradeProcessState;
  const factory ModifyTradeProcessState.orderSuccess() =
      ModifyTradeSuccessProcessState;
  const factory ModifyTradeProcessState.orderError() =
      ModifyTradeErrorProcessState;
  const factory ModifyTradeProcessState.marketClosed() =
      ModifyTradeMarketClosedProcessState;
}
