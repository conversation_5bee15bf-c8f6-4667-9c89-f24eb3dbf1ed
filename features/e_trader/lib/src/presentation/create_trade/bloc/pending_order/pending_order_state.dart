part of 'pending_order_bloc.dart';

@freezed
sealed class PendingOrderState with _$PendingOrderState {
  const PendingOrderState._();
  const factory PendingOrderState({
    required String accountNumber,
    MarginInformationModel? marginInformation,
    required SymbolQuoteModel symbolQuoteModel,
    required TradeComponentState<double, OrderSizeErrorCode> orderSizeState,
    required TradeComponentState<double, OrderPriceErrorCode> orderPriceState,
    required TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode>
    takeProfitState,
    required TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode>
    stopLossState,
    required TradeComponentState<void, TradeError> marginAllocationState,
    TradeType? tradeType,
    required String symbolCode,
    @Default(PendingOrderProcessState.loading())
    PendingOrderProcessState currentState,
  }) = _PendingOrderState;

  double get currentPrice => switch (tradeType) {
    TradeType.buy => symbolQuoteModel.ask,
    TradeType.sell => symbolQuoteModel.bid,
    null => 0.0,
  };

  double get takeProfit =>
      takeProfitState.isActive() ? takeProfitState.value.price.toDouble() : 0.0;
  double get stopLoss =>
      stopLossState.isActive() ? stopLossState.value.price.toDouble() : 0.0;

  BuySellButtonState buyButtonState() {
    if (tradeType == TradeType.buy) {
      return BuySellButtonState.selected(symbolQuoteModel.ask);
    }
    return BuySellButtonState.active(symbolQuoteModel.ask);
  }

  BuySellButtonState sellButtonState() {
    if (tradeType == TradeType.sell) {
      return BuySellButtonState.selected(symbolQuoteModel.bid);
    }
    return BuySellButtonState.active(symbolQuoteModel.bid);
  }

  bool isValid() {
    if (tradeType == null) return false;
    if (marginInformation == null) return false;
    final requiredComponentsValid = [
      orderSizeState,
      orderPriceState,
      marginAllocationState,
    ].every((state) => state.isActive() && state.isValid());

    final optionalComponentsValid = [
      takeProfitState,
      stopLossState,
    ].where((state) => state.isActive()).every((state) => state.isValid());

    return requiredComponentsValid && optionalComponentsValid;
  }
}

@freezed
sealed class PendingOrderProcessState with _$PendingOrderProcessState {
  const factory PendingOrderProcessState.loading() =
      PendingOrderLoadingProcessState;
  const factory PendingOrderProcessState.connected() =
      PendingOrderConnectedProcessState;
  const factory PendingOrderProcessState.disconnected() =
      PendingOrderDisconnectedProcessState;
  const factory PendingOrderProcessState.placingOrder() =
      PendingOrderPlacingProcessState;
  const factory PendingOrderProcessState.orderSuccess() =
      PendingOrderSuccessProcessState;
  const factory PendingOrderProcessState.orderError({String? errorMessage}) =
      PendingOrderErrorProcessState;
  const factory PendingOrderProcessState.marketClosed() =
      PendingOrderMarketClosedProcessState;
}
