import 'package:duplo/duplo.dart';
import 'package:e_trader/src/presentation/model/margin_information_model.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';

class InformationWidget extends StatelessWidget {
  const InformationWidget({
    super.key,
    required this.marginInformation,
    required this.accountCurrency,
    this.isLoading = false,
    required this.isForex,
  });

  final MarginInformationModel? marginInformation;
  final bool isLoading;
  final bool isForex;
  final String accountCurrency;

  @override
  Widget build(BuildContext context) {
    final localization = EquitiLocalization.of(context);

    return Column(
      children: [
        DuploKeyValueDisplay(
          keyTextStyle: context.duploTextStyles.textXs,
          valueTextStyle: context.duploTextStyles.textXs,
          valueColor: context.duploTheme.text.textSecondary,
          keyColor: context.duploTheme.text.textSecondary,
          contentSpacing: 8.0,
          keyValuePairs: [
            KeyValuePair(
              label: localization.trader_notionalValue,
              value:
                  "${marginInformation?.getNotionalValue ?? 0} $accountCurrency",
            ),
            KeyValuePair(
              label: localization.trader_freeMargin,
              value:
                  "${marginInformation?.getMarginFree ?? 0} $accountCurrency",
            ),
            KeyValuePair(
              label: localization.trader_marginRequirement,
              value:
                  "${marginInformation?.getRequiredMargin ?? 0} $accountCurrency",
            ),
            KeyValuePair(
              label: localization.trader_marginAllocation,
              value: "${marginInformation?.getMarginPercentage ?? 0}",
              color:
                  (marginInformation?.requiredMarginPercentage ?? 0) > 100
                      ? context.duploTheme.text.textErrorPrimary
                      : null,
            ),
            KeyValuePair(
              label:
                  isForex
                      ? localization.trader_pipValue
                      : localization.trader_pointValue,
              value: "${marginInformation?.getPIPValue ?? 0} $accountCurrency",
            ),
          ],
        ),
      ],
    );
  }
}
