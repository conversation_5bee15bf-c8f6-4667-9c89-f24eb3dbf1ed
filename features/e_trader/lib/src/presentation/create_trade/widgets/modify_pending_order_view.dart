import 'package:duplo/duplo.dart';
import 'package:e_trader/src/data/socket/order_model.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/domain/model/order_limit_type.dart';
import 'package:e_trader/src/domain/model/order_type.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/presentation/create_trade/bloc/modify_pending_order/bloc/modify_pending_order_bloc.dart';
import 'package:e_trader/src/presentation/create_trade/widgets/modify_pending_order_loading.dart';
import 'package:e_trader/src/presentation/create_trade/widgets/order_limit_widget.dart';
import 'package:e_trader/src/presentation/create_trade/widgets/order_price_widget.dart';
import 'package:e_trader/src/presentation/positions_and_trades/order_list_tile.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:prelude/prelude.dart';

class ModifyPendingOrderView extends StatelessWidget {
  final OrderModel order;
  const ModifyPendingOrderView({super.key, required this.order});

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final duploTextStyles = context.duploTextStyles;
    final localization = EquitiLocalization.of(context);
    String locale = Localizations.localeOf(context).toString();
    return BlocProvider(
      create:
          (blocProviderContext) =>
              diContainer<ModifyPendingOrderBloc>(param1: order),
      child: BlocConsumer<ModifyPendingOrderBloc, ModifyPendingOrderState>(
        listenWhen:
            (previous, current) =>
                previous.currentState != current.currentState,
        listener: (listenerContext, state) {
          if (state.currentState is ModifyPendingOrderSuccessProcessState) {
            DuploToast().showToastMessage(
              context: listenerContext,
              widget: DuploToastTrade(
                titleMessage:
                    state.orderModel.tradeType == TradeType.buy
                        ? localization.trader_buyOrderModified
                        : localization.trader_sellOrderModified,
                trade: TradeToastModel(
                  symbolImage: state.orderModel.productLogoUrl,
                  symbolName: state.orderModel.symbol,
                  lotSize: EquitiFormatter.formatNumber(
                    value: state.orderModel.lotSize,
                    locale: locale,
                  ),
                  price: EquitiFormatter.decimalPatternDigits(
                    value: state.orderModel.currentPrice,
                    digits: order.digits,
                    locale: locale,
                  ),
                  type:
                      state.orderModel.tradeType == TradeType.buy
                          ? TradeToastType.buy
                          : TradeToastType.sell,
                ),
                type: ToastMessageType.success,
                onLeadingAction: () {
                  DuploToast().hidesToastMessage();
                  if (listenerContext.mounted) Navigator.pop(listenerContext);
                },
              ),
            );
            Navigator.pop(listenerContext);
          } else if (state.currentState is OrderDeletionSuccessProcessState) {
            DuploToast().showToastMessage(
              context: listenerContext,
              widget: DuploToastTrade(
                titleMessage: localization.trader_orderDeleted,
                trade: TradeToastModel(
                  symbolImage: state.orderModel.productLogoUrl,
                  symbolName: state.orderModel.symbol,
                  lotSize: EquitiFormatter.formatNumber(
                    value: state.orderModel.lotSize,
                    locale: locale,
                  ),
                  price: EquitiFormatter.decimalPatternDigits(
                    value: state.orderModel.currentPrice,
                    digits: order.digits,
                    locale: locale,
                  ),
                  type:
                      state.orderModel.tradeType == TradeType.buy
                          ? TradeToastType.buy
                          : TradeToastType.sell,
                ),
                type: ToastMessageType.success,
                onLeadingAction: () {
                  DuploToast().hidesToastMessage();
                  if (listenerContext.mounted) Navigator.pop(listenerContext);
                },
              ),
            );
            Navigator.pop(listenerContext);
          } else if (state.currentState is OrderDeletionFailureProcessState) {
            DuploToast().showToastMessage(
              context: listenerContext,
              widget: DuploToastMessage(
                titleMessage: localization.trader_deleteOrderFailed,
                descriptionMessage: localization.trader_somethingWentWrong,
                messageType: ToastMessageType.error,
                onLeadingAction: () {
                  DuploToast().hidesToastMessage();
                },
              ),
            );
          } else if (state.currentState
              is OrderDeletionMarketClosedProcessState) {
            DuploToast().showToastMessage(
              context: context,
              widget: DuploToastMessage(
                titleMessage: localization.trader_marketIsClosed,
                descriptionMessage:
                    localization
                        .trader_modifyPendingOrder_marketIsClosedDescription,
                messageType: ToastMessageType.error,
                onLeadingAction: () => DuploToast().hidesToastMessage(),
              ),
            );
          }
        },
        buildWhen: (previous, current) => previous != current,
        builder: (blocBuilderContext, state) {
          return switch (state.currentState) {
            ModifyPendingOrderLoadingProcessState() =>
              ModifyPendingOrderLoading(),
            ModifyPendingOrderDisconnectedProcessState() => Center(
              child: Text(localization.trader_somethingWentWrong),
            ),
            _ => Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    physics: ClampingScrollPhysics(),
                    child: Container(
                      color: theme.background.bgSecondary,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          OrderListTile(
                            tpValue: order.takeProfit,
                            slValue: order.stopLoss,
                            digits: order.digits,
                            orderPrice: order.openPrice,
                            lots: order.lotSize,
                            currentPrice: state.orderModel.currentPrice,
                            tradeType: order.tradeType,
                            productIconURL: order.productLogoUrl,
                            productName: order.symbol,
                          ),
                          Divider(
                            color: theme.border.borderSecondary,
                            height: 0,
                          ),
                          Container(
                            decoration: BoxDecoration(
                              color: theme.background.bgPrimary,
                              border: Border(
                                left: BorderSide(
                                  color:
                                      order.tradeType == TradeType.buy
                                          ? theme.foreground.fgSuccessPrimary
                                          : theme.foreground.fgErrorPrimary,
                                  width: 4.0,
                                ),
                              ),
                            ),
                            child: Padding(
                              padding: EdgeInsetsDirectional.all(16),
                              child: DuploKeyValueDisplay(
                                keyTextStyle: duploTextStyles.textXs,
                                valueTextStyle: duploTextStyles.textXs,
                                contentSpacing: 0.0,
                                addBorder: false,
                                keyColor: theme.text.textTertiary,
                                valueFontWeight: DuploFontWeight.regular,
                                keyFontWeight: DuploFontWeight.regular,
                                keyValuePairs: [
                                  KeyValuePair(
                                    label: localization.trader_openPriceTitle,
                                    value: EquitiFormatter.formatDynamicDigits(
                                      value: order.openPrice,
                                      digits: order.digits,
                                      locale: locale,
                                    ),
                                  ),
                                  KeyValuePair(
                                    label:
                                        localization.trader_currentPriceTitle,
                                    value: EquitiFormatter.formatDynamicDigits(
                                      value: state.orderModel.currentPrice,
                                      digits: order.digits,
                                      locale: locale,
                                    ),
                                  ),
                                  KeyValuePair(
                                    label: localization.trader_takeProfitTitle,
                                    value: EquitiFormatter.formatDynamicDigits(
                                      value: order.takeProfit,
                                      digits: order.digits,
                                      locale: locale,
                                    ),
                                  ),
                                  KeyValuePair(
                                    label: localization.trader_stopLossTitle,
                                    value: EquitiFormatter.formatDynamicDigits(
                                      value: order.stopLoss,
                                      digits: order.digits,
                                      locale: locale,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          const SizedBox(height: 8.0),
                          Padding(
                            padding: EdgeInsetsDirectional.only(
                              start: 16.0,
                              end: 16.0,
                            ),
                            child: DuploText(
                              text: localization.trader_modifyOrder,
                              textAlign: TextAlign.left,
                              color: theme.text.textPrimary,
                              style: context.duploTextStyles.textLg,
                              maxLines: 1,
                              fontWeight: DuploFontWeight.semiBold,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsetsDirectional.symmetric(
                              vertical: 8.0,
                              horizontal: 16.0,
                            ),
                            child: _ModifyPendingOrderForm(order: order),
                          ),
                          Padding(
                            padding: EdgeInsetsDirectional.all(16),
                            child: Row(
                              children: [
                                Expanded(
                                  flex: 1,
                                  child: DuploButton.secondary(
                                    title: localization.trader_delete,
                                    loadingText: localization.trader_deleting,
                                    isLoading: switch (state.currentState) {
                                      DeletingOrderProcessState() => true,
                                      _ => false,
                                    },
                                    onTap:
                                        () => blocBuilderContext
                                            .read<ModifyPendingOrderBloc>()
                                            .add(
                                              const ModifyPendingOrderEvent.deleteOrder(),
                                            ),
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  flex: 2,
                                  child: DuploButton.primaryBold(
                                    isLoading: switch (state.currentState) {
                                      ModifyPlacingPendingOrderProcessState() =>
                                        true,
                                      _ => false,
                                    },
                                    title: localization.trader_saveOrder,
                                    loadingText: localization.trader_saving,
                                    isDisabled: switch (state.currentState) {
                                      ModifyPlacingPendingOrderProcessState() =>
                                        false,
                                      _ =>
                                        !(state.isValid() &&
                                            (state.orderPrice !=
                                                    order.openPrice ||
                                                state.stopLoss !=
                                                    order.stopLoss ||
                                                state.takeProfit !=
                                                    order.takeProfit)),
                                    },
                                    onTap: () {
                                      blocBuilderContext
                                          .read<ModifyPendingOrderBloc>()
                                          .add(
                                            const ModifyPendingOrderEvent.submit(),
                                          );
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          };
        },
      ),
    );
  }
}

class _ModifyPendingOrderForm extends StatelessWidget {
  const _ModifyPendingOrderForm({required this.order});

  final OrderModel order;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    return BlocBuilder<ModifyPendingOrderBloc, ModifyPendingOrderState>(
      buildWhen: (previous, current) => previous != current,
      builder: (blocBuilderContext, state) {
        return Card(
          elevation: 0,
          color: theme.background.bgPrimary,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16.0),
            side: BorderSide(color: theme.border.borderSecondary, width: 1.0),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                OrderPriceWidget(
                  args: (
                    initialPrice: order.openPrice,
                    currentPrice: state.orderModel.currentPrice,
                    digits: order.digits,
                    isDisabled: false,
                  ),
                  onOrderPriceChanged:
                      (orderPriceState) =>
                          blocBuilderContext.read<ModifyPendingOrderBloc>().add(
                            ModifyPendingOrderEvent.priceChanged(
                              orderPriceState,
                            ),
                          ),
                  tradeType: order.tradeType,
                ),
                const SizedBox(height: 16.0),
                if (state.orderPriceState.isValid())
                  OrderLimitWidget(
                    orderType: OrderType.pendingOrder,
                    isEnabled: order.takeProfit != 0.0,
                    orderLimitType: OrderLimitType.takeProfit,
                    digits: order.digits,
                    tradeType: order.tradeType,
                    pipValue: state.marginInformation!.pipInformation.pipValue,
                    pipMultipler:
                        state.marginInformation!.pipInformation.pipMultipler,
                    pipSize: state.marginInformation!.pipInformation.onePip,
                    currentPrice: state.orderPriceState.value,
                    initialPrice: order.takeProfit,
                    methodOrder: [
                      MethodTypeEnum.price,
                      MethodTypeEnum.distance,
                      MethodTypeEnum.profitOrLoss,
                    ],
                    onOrderLimitStateChanged:
                        (orderLimitState) => blocBuilderContext
                            .read<ModifyPendingOrderBloc>()
                            .add(
                              ModifyPendingOrderEvent.takeProfitChanged(
                                orderLimitState,
                              ),
                            ),
                  ),
                const SizedBox(height: 16.0),
                Divider(color: theme.border.borderSecondary),
                const SizedBox(height: 16.0),
                if (state.orderPriceState.isValid())
                  OrderLimitWidget(
                    orderType: OrderType.pendingOrder,
                    isEnabled: order.stopLoss != 0.0,
                    orderLimitType: OrderLimitType.stopLoss,
                    digits: order.digits,
                    tradeType: order.tradeType,
                    pipValue: state.marginInformation!.pipInformation.pipValue,
                    pipMultipler:
                        state.marginInformation!.pipInformation.pipMultipler,
                    pipSize: state.marginInformation!.pipInformation.onePip,
                    currentPrice: state.orderPriceState.value,
                    initialPrice: order.stopLoss,
                    methodOrder: [
                      MethodTypeEnum.price,
                      MethodTypeEnum.distance,
                      MethodTypeEnum.profitOrLoss,
                    ],
                    onOrderLimitStateChanged: (orderLimitState) {
                      blocBuilderContext.read<ModifyPendingOrderBloc>().add(
                        ModifyPendingOrderEvent.stopLossChanged(
                          orderLimitState,
                        ),
                      );
                    },
                  ),
              ],
            ),
          ),
        );
      },
    );
  }
}
