import 'package:duplo/duplo.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/presentation/create_trade/bloc/input_order_price/order_price_bloc.dart';
import 'package:e_trader/src/presentation/create_trade/bloc/input_order_price/order_price_error_code.dart';
import 'package:e_trader/src/presentation/create_trade/bloc/trade_component_state.dart';
import 'package:e_trader/src/presentation/create_trade/widgets/buy_sell_limit_container.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:prelude/prelude.dart';

typedef OrderPriceArgs =
    ({double initialPrice, double currentPrice, int digits, bool isDisabled});

class OrderPriceWidget extends StatelessWidget {
  const OrderPriceWidget({
    super.key,
    required this.args,
    required this.onOrderPriceChanged,
    required this.tradeType,
  });

  final void Function(TradeComponentState<double, OrderPriceErrorCode> state)
  onOrderPriceChanged;
  final OrderPriceArgs args;
  final TradeType? tradeType;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => diContainer<OrderPriceBloc>(param1: args),
      child: _OrderPriceContent(
        args: args,
        onOrderPriceChanged: onOrderPriceChanged,
        tradeType: tradeType,
        initLocale: Localizations.localeOf(context).toString(),
      ),
    );
  }
}

class _OrderPriceContent extends StatefulWidget {
  const _OrderPriceContent({
    required this.args,
    required this.onOrderPriceChanged,
    required this.tradeType,
    required this.initLocale,
  });

  final OrderPriceArgs args;
  final void Function(TradeComponentState<double, OrderPriceErrorCode> state)
  onOrderPriceChanged;
  final TradeType? tradeType;
  final String initLocale;

  @override
  State<_OrderPriceContent> createState() => _OrderPriceContentState();
}

class _OrderPriceContentState extends State<_OrderPriceContent> {
  late final TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController();
    _controller.text = EquitiFormatter.formatDynamicDigits(
      value: widget.args.initialPrice,
      digits: widget.args.digits,
      locale: widget.initLocale,
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(covariant _OrderPriceContent oldWidget) {
    super.didUpdateWidget(oldWidget);

    context.read<OrderPriceBloc>().add(
      OrderPriceEvent.marketPriceChanged(
        marketPrice: widget.args.currentPrice,
        inputPrice: _controller.text,
      ),
    );
  }

  @override
  Widget build(BuildContext _) {
    final localization = EquitiLocalization.of(context);

    return BlocConsumer<OrderPriceBloc, OrderPriceState>(
      listenWhen:
          (previous, current) => previous.currentState != current.currentState,
      listener:
          (BuildContext context, OrderPriceState state) =>
              widget.onOrderPriceChanged(state.currentState),
      buildWhen:
          (previous, current) =>
              previous.currentState != current.currentState ||
              previous.isBuyLimitReached != current.isBuyLimitReached ||
              previous.isBuyStopReached != current.isBuyStopReached ||
              previous.isSellLimitReached != current.isSellLimitReached ||
              previous.isSellStopReached != current.isSellStopReached,
      builder: (context, state) {
        return StepperControlWidget(
          leadingWidget: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Flexible(
                    child: DuploText(
                      text: localization.trader_orderPrice,
                      style: context.duploTextStyles.textMd,
                      textAlign: TextAlign.start,
                      maxLines: 1,
                      color: context.duploTheme.text.textPrimary,
                      fontWeight: DuploFontWeight.semiBold,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  const SizedBox(width: 8),
                  if ((widget.tradeType == TradeType.buy &&
                          state.isBuyLimitReached) ||
                      (widget.tradeType == TradeType.sell &&
                          state.isSellLimitReached))
                    Flexible(
                      child:
                          widget.args.isDisabled
                              ? const SizedBox.shrink()
                              : BuySellLimitContainer(
                                tradeType: widget.tradeType!,
                                containerType: ContainerType.buySellLimit,
                              ),
                    ),
                  if ((widget.tradeType == TradeType.buy &&
                          state.isBuyStopReached) ||
                      (widget.tradeType == TradeType.sell &&
                          state.isSellStopReached))
                    Flexible(
                      child:
                          widget.args.isDisabled
                              ? const SizedBox.shrink()
                              : BuySellLimitContainer(
                                tradeType: widget.tradeType!,
                                containerType: ContainerType.buySellStop,
                              ),
                    ),
                ],
              ),
              InkWell(
                onTap: () {
                  DuploDialog.showInfoDialog(
                    context: context,
                    title: localization.trader_orderPrice,
                    description: localization.trader_orderPriceDescription,
                  );
                },
                child: trader.Assets.images.infoCircle.svg(
                  width: 24,
                  height: 24,
                ),
              ),
            ],
          ),
          inputWidget: StepperNumberInputWidget(
            controller: _controller,
            prescisionFactor: widget.args.digits,
            hintText: localization.trader_enterAnumber,
            enabled: !widget.args.isDisabled,
            errorText: switch (state.currentState) {
              TradeComponentErrorState<double, OrderPriceErrorCode>() =>
                localization.trader_validPriceMessage,
              _ => null,
            },
            onValueChange:
                (value) => context.read<OrderPriceBloc>().add(
                  OrderPriceEvent.updateOrderPrice(inputPrice: value),
                ),
            changeFactor: 0.1,
          ),
          bordered: false,
        );
      },
    );
  }
}
