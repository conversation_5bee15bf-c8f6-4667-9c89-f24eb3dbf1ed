import 'package:duplo/duplo.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;
import 'package:e_trader/src/data/api/trading_account_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class ServerConnectionDetailsWidget extends StatelessWidget {
  const ServerConnectionDetailsWidget({
    super.key,
    required this.tradingAccountModel,
    required this.title,
    required this.subtitle,
  });

  final TradingAccountModel tradingAccountModel;
  final DuploText title;
  final DuploText subtitle;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          title,
          subtitle,
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: theme.background.bgPrimary,
              borderRadius: BorderRadius.circular(DuploRadius.radius_md_8),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _ServerDetailItemWidget(
                  title: 'Server',
                  subTitle: tradingAccountModel.serverName,
                  onTap: () {
                    Clipboard.setData(
                      ClipboardData(text: tradingAccountModel.serverName),
                    );
                  },
                ),
                _ServerDetailItemWidget(
                  title:
                      '${tradingAccountModel.platformType.displayName} Login',
                  subTitle: tradingAccountModel.accountNumber,
                  onTap: () {
                    Clipboard.setData(
                      ClipboardData(text: tradingAccountModel.accountNumber),
                    );
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _ServerDetailItemWidget extends StatelessWidget {
  const _ServerDetailItemWidget({
    required this.title,
    required this.subTitle,
    required this.onTap,
  });

  final String title;
  final String subTitle;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final textStyles = context.duploTextStyles;
    return DuploTap(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 8.0),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  DuploText(
                    text: title,
                    style: textStyles.textXs,
                    color: theme.text.textSecondary,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  DuploText(
                    text: subTitle,
                    style: textStyles.textXs,
                    fontWeight: DuploFontWeight.semiBold,
                    color: theme.text.textSecondary,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
            trader.Assets.images.copy.svg(
              height: 24,
              width: 24,
              colorFilter: ColorFilter.mode(
                theme.foreground.fgSecondary,
                BlendMode.srcIn,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
