import 'package:e_trader/src/data/api/platform_account_type.dart';
import 'package:e_trader/src/data/api/platform_type.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'account_view_model.freezed.dart';
part 'account_view_model.g.dart';

@freezed
abstract class AccountViewModel with _$AccountViewModel {
  const factory AccountViewModel({
    String? name,
    String? nickName,
    String? accountNumber,
    @Default(PlatformType.unknown) PlatformType platformType,
    @Default(PlatformAccountType.unknown)
    PlatformAccountType platformAccountType,
    double? equity,
    double? profit,
    required String homeCurrency,
    required String accountId,
    double? balance,
    double? credit,
    double? margin,
    double? marginLevel,
    @Default(false) bool isSelected,
  }) = _AccountViewModel;

  factory AccountViewModel.fromJson(Map<String, dynamic> json) =>
      _$AccountViewModelFromJson(json);
}
