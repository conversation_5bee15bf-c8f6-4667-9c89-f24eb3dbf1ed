// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'account_view_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$AccountViewModel {

 String? get name; String? get nickName; String? get accountNumber; PlatformType get platformType; PlatformAccountType get platformAccountType; double? get equity; double? get profit; String get homeCurrency; String get accountId; double? get balance; double? get credit; double? get margin; double? get marginLevel; bool get isSelected;
/// Create a copy of AccountViewModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AccountViewModelCopyWith<AccountViewModel> get copyWith => _$AccountViewModelCopyWithImpl<AccountViewModel>(this as AccountViewModel, _$identity);

  /// Serializes this AccountViewModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AccountViewModel&&(identical(other.name, name) || other.name == name)&&(identical(other.nickName, nickName) || other.nickName == nickName)&&(identical(other.accountNumber, accountNumber) || other.accountNumber == accountNumber)&&(identical(other.platformType, platformType) || other.platformType == platformType)&&(identical(other.platformAccountType, platformAccountType) || other.platformAccountType == platformAccountType)&&(identical(other.equity, equity) || other.equity == equity)&&(identical(other.profit, profit) || other.profit == profit)&&(identical(other.homeCurrency, homeCurrency) || other.homeCurrency == homeCurrency)&&(identical(other.accountId, accountId) || other.accountId == accountId)&&(identical(other.balance, balance) || other.balance == balance)&&(identical(other.credit, credit) || other.credit == credit)&&(identical(other.margin, margin) || other.margin == margin)&&(identical(other.marginLevel, marginLevel) || other.marginLevel == marginLevel)&&(identical(other.isSelected, isSelected) || other.isSelected == isSelected));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,nickName,accountNumber,platformType,platformAccountType,equity,profit,homeCurrency,accountId,balance,credit,margin,marginLevel,isSelected);

@override
String toString() {
  return 'AccountViewModel(name: $name, nickName: $nickName, accountNumber: $accountNumber, platformType: $platformType, platformAccountType: $platformAccountType, equity: $equity, profit: $profit, homeCurrency: $homeCurrency, accountId: $accountId, balance: $balance, credit: $credit, margin: $margin, marginLevel: $marginLevel, isSelected: $isSelected)';
}


}

/// @nodoc
abstract mixin class $AccountViewModelCopyWith<$Res>  {
  factory $AccountViewModelCopyWith(AccountViewModel value, $Res Function(AccountViewModel) _then) = _$AccountViewModelCopyWithImpl;
@useResult
$Res call({
 String? name, String? nickName, String? accountNumber, PlatformType platformType, PlatformAccountType platformAccountType, double? equity, double? profit, String homeCurrency, String accountId, double? balance, double? credit, double? margin, double? marginLevel, bool isSelected
});




}
/// @nodoc
class _$AccountViewModelCopyWithImpl<$Res>
    implements $AccountViewModelCopyWith<$Res> {
  _$AccountViewModelCopyWithImpl(this._self, this._then);

  final AccountViewModel _self;
  final $Res Function(AccountViewModel) _then;

/// Create a copy of AccountViewModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? name = freezed,Object? nickName = freezed,Object? accountNumber = freezed,Object? platformType = null,Object? platformAccountType = null,Object? equity = freezed,Object? profit = freezed,Object? homeCurrency = null,Object? accountId = null,Object? balance = freezed,Object? credit = freezed,Object? margin = freezed,Object? marginLevel = freezed,Object? isSelected = null,}) {
  return _then(_self.copyWith(
name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,nickName: freezed == nickName ? _self.nickName : nickName // ignore: cast_nullable_to_non_nullable
as String?,accountNumber: freezed == accountNumber ? _self.accountNumber : accountNumber // ignore: cast_nullable_to_non_nullable
as String?,platformType: null == platformType ? _self.platformType : platformType // ignore: cast_nullable_to_non_nullable
as PlatformType,platformAccountType: null == platformAccountType ? _self.platformAccountType : platformAccountType // ignore: cast_nullable_to_non_nullable
as PlatformAccountType,equity: freezed == equity ? _self.equity : equity // ignore: cast_nullable_to_non_nullable
as double?,profit: freezed == profit ? _self.profit : profit // ignore: cast_nullable_to_non_nullable
as double?,homeCurrency: null == homeCurrency ? _self.homeCurrency : homeCurrency // ignore: cast_nullable_to_non_nullable
as String,accountId: null == accountId ? _self.accountId : accountId // ignore: cast_nullable_to_non_nullable
as String,balance: freezed == balance ? _self.balance : balance // ignore: cast_nullable_to_non_nullable
as double?,credit: freezed == credit ? _self.credit : credit // ignore: cast_nullable_to_non_nullable
as double?,margin: freezed == margin ? _self.margin : margin // ignore: cast_nullable_to_non_nullable
as double?,marginLevel: freezed == marginLevel ? _self.marginLevel : marginLevel // ignore: cast_nullable_to_non_nullable
as double?,isSelected: null == isSelected ? _self.isSelected : isSelected // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _AccountViewModel implements AccountViewModel {
  const _AccountViewModel({this.name, this.nickName, this.accountNumber, this.platformType = PlatformType.unknown, this.platformAccountType = PlatformAccountType.unknown, this.equity, this.profit, required this.homeCurrency, required this.accountId, this.balance, this.credit, this.margin, this.marginLevel, this.isSelected = false});
  factory _AccountViewModel.fromJson(Map<String, dynamic> json) => _$AccountViewModelFromJson(json);

@override final  String? name;
@override final  String? nickName;
@override final  String? accountNumber;
@override@JsonKey() final  PlatformType platformType;
@override@JsonKey() final  PlatformAccountType platformAccountType;
@override final  double? equity;
@override final  double? profit;
@override final  String homeCurrency;
@override final  String accountId;
@override final  double? balance;
@override final  double? credit;
@override final  double? margin;
@override final  double? marginLevel;
@override@JsonKey() final  bool isSelected;

/// Create a copy of AccountViewModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AccountViewModelCopyWith<_AccountViewModel> get copyWith => __$AccountViewModelCopyWithImpl<_AccountViewModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$AccountViewModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AccountViewModel&&(identical(other.name, name) || other.name == name)&&(identical(other.nickName, nickName) || other.nickName == nickName)&&(identical(other.accountNumber, accountNumber) || other.accountNumber == accountNumber)&&(identical(other.platformType, platformType) || other.platformType == platformType)&&(identical(other.platformAccountType, platformAccountType) || other.platformAccountType == platformAccountType)&&(identical(other.equity, equity) || other.equity == equity)&&(identical(other.profit, profit) || other.profit == profit)&&(identical(other.homeCurrency, homeCurrency) || other.homeCurrency == homeCurrency)&&(identical(other.accountId, accountId) || other.accountId == accountId)&&(identical(other.balance, balance) || other.balance == balance)&&(identical(other.credit, credit) || other.credit == credit)&&(identical(other.margin, margin) || other.margin == margin)&&(identical(other.marginLevel, marginLevel) || other.marginLevel == marginLevel)&&(identical(other.isSelected, isSelected) || other.isSelected == isSelected));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,nickName,accountNumber,platformType,platformAccountType,equity,profit,homeCurrency,accountId,balance,credit,margin,marginLevel,isSelected);

@override
String toString() {
  return 'AccountViewModel(name: $name, nickName: $nickName, accountNumber: $accountNumber, platformType: $platformType, platformAccountType: $platformAccountType, equity: $equity, profit: $profit, homeCurrency: $homeCurrency, accountId: $accountId, balance: $balance, credit: $credit, margin: $margin, marginLevel: $marginLevel, isSelected: $isSelected)';
}


}

/// @nodoc
abstract mixin class _$AccountViewModelCopyWith<$Res> implements $AccountViewModelCopyWith<$Res> {
  factory _$AccountViewModelCopyWith(_AccountViewModel value, $Res Function(_AccountViewModel) _then) = __$AccountViewModelCopyWithImpl;
@override @useResult
$Res call({
 String? name, String? nickName, String? accountNumber, PlatformType platformType, PlatformAccountType platformAccountType, double? equity, double? profit, String homeCurrency, String accountId, double? balance, double? credit, double? margin, double? marginLevel, bool isSelected
});




}
/// @nodoc
class __$AccountViewModelCopyWithImpl<$Res>
    implements _$AccountViewModelCopyWith<$Res> {
  __$AccountViewModelCopyWithImpl(this._self, this._then);

  final _AccountViewModel _self;
  final $Res Function(_AccountViewModel) _then;

/// Create a copy of AccountViewModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? name = freezed,Object? nickName = freezed,Object? accountNumber = freezed,Object? platformType = null,Object? platformAccountType = null,Object? equity = freezed,Object? profit = freezed,Object? homeCurrency = null,Object? accountId = null,Object? balance = freezed,Object? credit = freezed,Object? margin = freezed,Object? marginLevel = freezed,Object? isSelected = null,}) {
  return _then(_AccountViewModel(
name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,nickName: freezed == nickName ? _self.nickName : nickName // ignore: cast_nullable_to_non_nullable
as String?,accountNumber: freezed == accountNumber ? _self.accountNumber : accountNumber // ignore: cast_nullable_to_non_nullable
as String?,platformType: null == platformType ? _self.platformType : platformType // ignore: cast_nullable_to_non_nullable
as PlatformType,platformAccountType: null == platformAccountType ? _self.platformAccountType : platformAccountType // ignore: cast_nullable_to_non_nullable
as PlatformAccountType,equity: freezed == equity ? _self.equity : equity // ignore: cast_nullable_to_non_nullable
as double?,profit: freezed == profit ? _self.profit : profit // ignore: cast_nullable_to_non_nullable
as double?,homeCurrency: null == homeCurrency ? _self.homeCurrency : homeCurrency // ignore: cast_nullable_to_non_nullable
as String,accountId: null == accountId ? _self.accountId : accountId // ignore: cast_nullable_to_non_nullable
as String,balance: freezed == balance ? _self.balance : balance // ignore: cast_nullable_to_non_nullable
as double?,credit: freezed == credit ? _self.credit : credit // ignore: cast_nullable_to_non_nullable
as double?,margin: freezed == margin ? _self.margin : margin // ignore: cast_nullable_to_non_nullable
as double?,marginLevel: freezed == marginLevel ? _self.marginLevel : marginLevel // ignore: cast_nullable_to_non_nullable
as double?,isSelected: null == isSelected ? _self.isSelected : isSelected // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
