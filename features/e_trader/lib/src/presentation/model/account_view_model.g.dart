// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'account_view_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_AccountViewModel _$AccountViewModelFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('_AccountViewModel', json, ($checkedConvert) {
  final val = _AccountViewModel(
    name: $checkedConvert('name', (v) => v as String?),
    nickName: $checkedConvert('nickName', (v) => v as String?),
    accountNumber: $checkedConvert('accountNumber', (v) => v as String?),
    platformType: $checkedConvert(
      'platformType',
      (v) =>
          $enumDecodeNullable(_$PlatformTypeEnumMap, v) ?? PlatformType.unknown,
    ),
    platformAccountType: $checkedConvert(
      'platformAccountType',
      (v) =>
          $enumDecodeNullable(_$PlatformAccountTypeEnumMap, v) ??
          PlatformAccountType.unknown,
    ),
    equity: $checkedConvert('equity', (v) => (v as num?)?.toDouble()),
    profit: $checkedConvert('profit', (v) => (v as num?)?.toDouble()),
    homeCurrency: $checkedConvert('homeCurrency', (v) => v as String),
    accountId: $checkedConvert('accountId', (v) => v as String),
    balance: $checkedConvert('balance', (v) => (v as num?)?.toDouble()),
    credit: $checkedConvert('credit', (v) => (v as num?)?.toDouble()),
    margin: $checkedConvert('margin', (v) => (v as num?)?.toDouble()),
    marginLevel: $checkedConvert('marginLevel', (v) => (v as num?)?.toDouble()),
    isSelected: $checkedConvert('isSelected', (v) => v as bool? ?? false),
  );
  return val;
});

Map<String, dynamic> _$AccountViewModelToJson(_AccountViewModel instance) =>
    <String, dynamic>{
      if (instance.name case final value?) 'name': value,
      if (instance.nickName case final value?) 'nickName': value,
      if (instance.accountNumber case final value?) 'accountNumber': value,
      'platformType': _$PlatformTypeEnumMap[instance.platformType]!,
      'platformAccountType':
          _$PlatformAccountTypeEnumMap[instance.platformAccountType]!,
      if (instance.equity case final value?) 'equity': value,
      if (instance.profit case final value?) 'profit': value,
      'homeCurrency': instance.homeCurrency,
      'accountId': instance.accountId,
      if (instance.balance case final value?) 'balance': value,
      if (instance.credit case final value?) 'credit': value,
      if (instance.margin case final value?) 'margin': value,
      if (instance.marginLevel case final value?) 'marginLevel': value,
      'isSelected': instance.isSelected,
    };

const _$PlatformTypeEnumMap = {
  PlatformType.dulcimer: 'Dulcimer',
  PlatformType.mt4: 'MT4',
  PlatformType.mt5: 'MT5',
  PlatformType.equiti: 'Equiti',
  PlatformType.unknown: 'unknown',
};

const _$PlatformAccountTypeEnumMap = {
  PlatformAccountType.premiere: 'Premiere',
  PlatformAccountType.standard: 'Standard',
  PlatformAccountType.unknown: 'unknown',
};
