import 'package:decimal/decimal.dart';
import 'package:e_trader/src/domain/model/method_type.dart';
import 'package:e_trader/src/domain/model/order_limit_calculation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:prelude/prelude.dart';

part 'order_limit_config.freezed.dart';

@freezed
abstract class OrderLimitConfig with _$OrderLimitConfig {
  const OrderLimitConfig._();

  const factory OrderLimitConfig({
    required List<MethodType> methods,
    required Decimal currentPrice,
    required Decimal initialPrice,
    required int digits,
  }) = _OrderLimitConfig;

  OrderLimitCalculation initialCalculation() {
    return methods.firstOrNull!.calculate(
      distance: Decimal.parse('3'),
      limitPrice: initialPrice,
      profitOrLoss: Decimal.parse('0'),
    );
  }
}
