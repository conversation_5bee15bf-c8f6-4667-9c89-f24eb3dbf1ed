// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'order_limit_config.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$OrderLimitConfig {

 List<MethodType> get methods; Decimal get currentPrice; Decimal get initialPrice; int get digits;
/// Create a copy of OrderLimitConfig
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$OrderLimitConfigCopyWith<OrderLimitConfig> get copyWith => _$OrderLimitConfigCopyWithImpl<OrderLimitConfig>(this as OrderLimitConfig, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is OrderLimitConfig&&const DeepCollectionEquality().equals(other.methods, methods)&&(identical(other.currentPrice, currentPrice) || other.currentPrice == currentPrice)&&(identical(other.initialPrice, initialPrice) || other.initialPrice == initialPrice)&&(identical(other.digits, digits) || other.digits == digits));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(methods),currentPrice,initialPrice,digits);

@override
String toString() {
  return 'OrderLimitConfig(methods: $methods, currentPrice: $currentPrice, initialPrice: $initialPrice, digits: $digits)';
}


}

/// @nodoc
abstract mixin class $OrderLimitConfigCopyWith<$Res>  {
  factory $OrderLimitConfigCopyWith(OrderLimitConfig value, $Res Function(OrderLimitConfig) _then) = _$OrderLimitConfigCopyWithImpl;
@useResult
$Res call({
 List<MethodType> methods, Decimal currentPrice, Decimal initialPrice, int digits
});




}
/// @nodoc
class _$OrderLimitConfigCopyWithImpl<$Res>
    implements $OrderLimitConfigCopyWith<$Res> {
  _$OrderLimitConfigCopyWithImpl(this._self, this._then);

  final OrderLimitConfig _self;
  final $Res Function(OrderLimitConfig) _then;

/// Create a copy of OrderLimitConfig
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? methods = null,Object? currentPrice = null,Object? initialPrice = null,Object? digits = null,}) {
  return _then(_self.copyWith(
methods: null == methods ? _self.methods : methods // ignore: cast_nullable_to_non_nullable
as List<MethodType>,currentPrice: null == currentPrice ? _self.currentPrice : currentPrice // ignore: cast_nullable_to_non_nullable
as Decimal,initialPrice: null == initialPrice ? _self.initialPrice : initialPrice // ignore: cast_nullable_to_non_nullable
as Decimal,digits: null == digits ? _self.digits : digits // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

}


/// @nodoc


class _OrderLimitConfig extends OrderLimitConfig {
  const _OrderLimitConfig({required final  List<MethodType> methods, required this.currentPrice, required this.initialPrice, required this.digits}): _methods = methods,super._();
  

 final  List<MethodType> _methods;
@override List<MethodType> get methods {
  if (_methods is EqualUnmodifiableListView) return _methods;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_methods);
}

@override final  Decimal currentPrice;
@override final  Decimal initialPrice;
@override final  int digits;

/// Create a copy of OrderLimitConfig
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OrderLimitConfigCopyWith<_OrderLimitConfig> get copyWith => __$OrderLimitConfigCopyWithImpl<_OrderLimitConfig>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OrderLimitConfig&&const DeepCollectionEquality().equals(other._methods, _methods)&&(identical(other.currentPrice, currentPrice) || other.currentPrice == currentPrice)&&(identical(other.initialPrice, initialPrice) || other.initialPrice == initialPrice)&&(identical(other.digits, digits) || other.digits == digits));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_methods),currentPrice,initialPrice,digits);

@override
String toString() {
  return 'OrderLimitConfig(methods: $methods, currentPrice: $currentPrice, initialPrice: $initialPrice, digits: $digits)';
}


}

/// @nodoc
abstract mixin class _$OrderLimitConfigCopyWith<$Res> implements $OrderLimitConfigCopyWith<$Res> {
  factory _$OrderLimitConfigCopyWith(_OrderLimitConfig value, $Res Function(_OrderLimitConfig) _then) = __$OrderLimitConfigCopyWithImpl;
@override @useResult
$Res call({
 List<MethodType> methods, Decimal currentPrice, Decimal initialPrice, int digits
});




}
/// @nodoc
class __$OrderLimitConfigCopyWithImpl<$Res>
    implements _$OrderLimitConfigCopyWith<$Res> {
  __$OrderLimitConfigCopyWithImpl(this._self, this._then);

  final _OrderLimitConfig _self;
  final $Res Function(_OrderLimitConfig) _then;

/// Create a copy of OrderLimitConfig
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? methods = null,Object? currentPrice = null,Object? initialPrice = null,Object? digits = null,}) {
  return _then(_OrderLimitConfig(
methods: null == methods ? _self._methods : methods // ignore: cast_nullable_to_non_nullable
as List<MethodType>,currentPrice: null == currentPrice ? _self.currentPrice : currentPrice // ignore: cast_nullable_to_non_nullable
as Decimal,initialPrice: null == initialPrice ? _self.initialPrice : initialPrice // ignore: cast_nullable_to_non_nullable
as Decimal,digits: null == digits ? _self.digits : digits // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}

// dart format on
