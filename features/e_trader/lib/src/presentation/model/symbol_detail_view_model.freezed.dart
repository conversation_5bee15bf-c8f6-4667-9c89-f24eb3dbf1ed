// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'symbol_detail_view_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SymbolDetailViewModel {

 String get symbolName; set symbolName(String value); String? get imageURL; set imageURL(String? value); String? get assetType; set assetType(String? value); double get minLot; set minLot(double value); double get maxLot; set maxLot(double value); int? get digit; set digit(int? value); bool get isForex; set isForex(bool value);
/// Create a copy of SymbolDetailViewModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SymbolDetailViewModelCopyWith<SymbolDetailViewModel> get copyWith => _$SymbolDetailViewModelCopyWithImpl<SymbolDetailViewModel>(this as SymbolDetailViewModel, _$identity);

  /// Serializes this SymbolDetailViewModel to a JSON map.
  Map<String, dynamic> toJson();




@override
String toString() {
  return 'SymbolDetailViewModel(symbolName: $symbolName, imageURL: $imageURL, assetType: $assetType, minLot: $minLot, maxLot: $maxLot, digit: $digit, isForex: $isForex)';
}


}

/// @nodoc
abstract mixin class $SymbolDetailViewModelCopyWith<$Res>  {
  factory $SymbolDetailViewModelCopyWith(SymbolDetailViewModel value, $Res Function(SymbolDetailViewModel) _then) = _$SymbolDetailViewModelCopyWithImpl;
@useResult
$Res call({
 String symbolName, String? imageURL, String? assetType, double minLot, double maxLot, int? digit, bool isForex
});




}
/// @nodoc
class _$SymbolDetailViewModelCopyWithImpl<$Res>
    implements $SymbolDetailViewModelCopyWith<$Res> {
  _$SymbolDetailViewModelCopyWithImpl(this._self, this._then);

  final SymbolDetailViewModel _self;
  final $Res Function(SymbolDetailViewModel) _then;

/// Create a copy of SymbolDetailViewModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? symbolName = null,Object? imageURL = freezed,Object? assetType = freezed,Object? minLot = null,Object? maxLot = null,Object? digit = freezed,Object? isForex = null,}) {
  return _then(_self.copyWith(
symbolName: null == symbolName ? _self.symbolName : symbolName // ignore: cast_nullable_to_non_nullable
as String,imageURL: freezed == imageURL ? _self.imageURL : imageURL // ignore: cast_nullable_to_non_nullable
as String?,assetType: freezed == assetType ? _self.assetType : assetType // ignore: cast_nullable_to_non_nullable
as String?,minLot: null == minLot ? _self.minLot : minLot // ignore: cast_nullable_to_non_nullable
as double,maxLot: null == maxLot ? _self.maxLot : maxLot // ignore: cast_nullable_to_non_nullable
as double,digit: freezed == digit ? _self.digit : digit // ignore: cast_nullable_to_non_nullable
as int?,isForex: null == isForex ? _self.isForex : isForex // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _SymbolDetailViewModel implements SymbolDetailViewModel {
   _SymbolDetailViewModel({required this.symbolName, this.imageURL, this.assetType, required this.minLot, required this.maxLot, this.digit, this.isForex = false});
  factory _SymbolDetailViewModel.fromJson(Map<String, dynamic> json) => _$SymbolDetailViewModelFromJson(json);

@override  String symbolName;
@override  String? imageURL;
@override  String? assetType;
@override  double minLot;
@override  double maxLot;
@override  int? digit;
@override@JsonKey()  bool isForex;

/// Create a copy of SymbolDetailViewModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SymbolDetailViewModelCopyWith<_SymbolDetailViewModel> get copyWith => __$SymbolDetailViewModelCopyWithImpl<_SymbolDetailViewModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SymbolDetailViewModelToJson(this, );
}



@override
String toString() {
  return 'SymbolDetailViewModel(symbolName: $symbolName, imageURL: $imageURL, assetType: $assetType, minLot: $minLot, maxLot: $maxLot, digit: $digit, isForex: $isForex)';
}


}

/// @nodoc
abstract mixin class _$SymbolDetailViewModelCopyWith<$Res> implements $SymbolDetailViewModelCopyWith<$Res> {
  factory _$SymbolDetailViewModelCopyWith(_SymbolDetailViewModel value, $Res Function(_SymbolDetailViewModel) _then) = __$SymbolDetailViewModelCopyWithImpl;
@override @useResult
$Res call({
 String symbolName, String? imageURL, String? assetType, double minLot, double maxLot, int? digit, bool isForex
});




}
/// @nodoc
class __$SymbolDetailViewModelCopyWithImpl<$Res>
    implements _$SymbolDetailViewModelCopyWith<$Res> {
  __$SymbolDetailViewModelCopyWithImpl(this._self, this._then);

  final _SymbolDetailViewModel _self;
  final $Res Function(_SymbolDetailViewModel) _then;

/// Create a copy of SymbolDetailViewModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? symbolName = null,Object? imageURL = freezed,Object? assetType = freezed,Object? minLot = null,Object? maxLot = null,Object? digit = freezed,Object? isForex = null,}) {
  return _then(_SymbolDetailViewModel(
symbolName: null == symbolName ? _self.symbolName : symbolName // ignore: cast_nullable_to_non_nullable
as String,imageURL: freezed == imageURL ? _self.imageURL : imageURL // ignore: cast_nullable_to_non_nullable
as String?,assetType: freezed == assetType ? _self.assetType : assetType // ignore: cast_nullable_to_non_nullable
as String?,minLot: null == minLot ? _self.minLot : minLot // ignore: cast_nullable_to_non_nullable
as double,maxLot: null == maxLot ? _self.maxLot : maxLot // ignore: cast_nullable_to_non_nullable
as double,digit: freezed == digit ? _self.digit : digit // ignore: cast_nullable_to_non_nullable
as int?,isForex: null == isForex ? _self.isForex : isForex // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
