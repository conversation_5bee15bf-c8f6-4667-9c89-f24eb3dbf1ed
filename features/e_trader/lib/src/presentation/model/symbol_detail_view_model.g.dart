// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'symbol_detail_view_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SymbolDetailViewModel _$SymbolDetailViewModelFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('_SymbolDetailViewModel', json, ($checkedConvert) {
  final val = _SymbolDetailViewModel(
    symbolName: $checkedConvert('symbolName', (v) => v as String),
    imageURL: $checkedConvert('imageURL', (v) => v as String?),
    assetType: $checkedConvert('assetType', (v) => v as String?),
    minLot: $checkedConvert('minLot', (v) => (v as num).toDouble()),
    maxLot: $checkedConvert('maxLot', (v) => (v as num).toDouble()),
    digit: $checkedConvert('digit', (v) => (v as num?)?.toInt()),
    isForex: $checkedConvert('isForex', (v) => v as bool? ?? false),
  );
  return val;
});

Map<String, dynamic> _$SymbolDetailViewModelToJson(
  _SymbolDetailViewModel instance,
) => <String, dynamic>{
  'symbolName': instance.symbolName,
  if (instance.imageURL case final value?) 'imageURL': value,
  if (instance.assetType case final value?) 'assetType': value,
  'minLot': instance.minLot,
  'maxLot': instance.maxLot,
  if (instance.digit case final value?) 'digit': value,
  'isForex': instance.isForex,
};
