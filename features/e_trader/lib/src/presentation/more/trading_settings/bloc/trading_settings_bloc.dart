import 'dart:async';

import 'package:e_trader/src/domain/usecase/get_selected_account_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_trading_preferences_use_case.dart';
import 'package:e_trader/src/domain/usecase/save_trading_preferences_use_case.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'trading_settings_bloc.freezed.dart';
part 'trading_settings_event.dart';
part 'trading_settings_state.dart';

class TradingSettingsBloc
    extends Bloc<TradingSettingsEvent, TradingSettingsState> {
  final GetTradingPreferencesUseCase _getTradingPreferencesUseCase;
  final SaveTradingPreferencesUseCase _saveTradingPreferencesUseCase;
  final GetSelectedAccountUseCase _getSelectedAccountUseCase;

  TradingSettingsBloc({
    required GetTradingPreferencesUseCase getTradingPreferencesUseCase,
    required SaveTradingPreferencesUseCase saveTradingPreferencesUseCase,
    required GetSelectedAccountUseCase getSelectedAccountUseCase,
  }) : _getTradingPreferencesUseCase = getTradingPreferencesUseCase,
       _saveTradingPreferencesUseCase = saveTradingPreferencesUseCase,
       _getSelectedAccountUseCase = getSelectedAccountUseCase,

       super(TradingSettingsInitial()) {
    on<_LeverageChanged>(_onLeverageChange);
    on<_FetchPrefrences>(_onFetchPrefrences);
  }

  FutureOr<void> _onFetchPrefrences(
    _FetchPrefrences event,
    Emitter<TradingSettingsState> emit,
  ) {
    final selectedAccount = _getSelectedAccountUseCase();

    emit(
      TradingSettingsSuccess(
        leverage: "1:${_getTradingPreferencesUseCase.getLeverage()}",
        accountNumber: selectedAccount?.accountNumber ?? "",
      ),
    );
  }

  FutureOr<void> _onLeverageChange(
    _LeverageChanged event,
    Emitter<TradingSettingsState> emit,
  ) {
    if (state case TradingSettingsSuccess()) {
      final selectedAccount = _getSelectedAccountUseCase();

      _saveTradingPreferencesUseCase.saveLeverage(event.leverage);
      emit(
        TradingSettingsSuccess(
          leverage: event.leverage,
          accountNumber: selectedAccount?.accountNumber ?? "",
        ),
      );
    }
  }
}
