part of '../navigation_bottom_bar.dart';

class _AccountSummaryContentWidget extends StatefulWidget {
  const _AccountSummaryContentWidget();

  @override
  State<_AccountSummaryContentWidget> createState() =>
      _AccountSummaryContentWidgetState();
}

class _AccountSummaryContentWidgetState
    extends State<_AccountSummaryContentWidget>
    with PerformanceObserverMixin {
  bool _isSubscribed = false;

  @override
  void initState() {
    super.initState();
    context.read<AccountBalanceBloc>().add(
      AccountBalanceEvent.connectToAccountBalance(),
    );
  }

  void _subscribe() {
    if (!_isSubscribed) {
      context.read<AccountBalanceBloc>().add(
        AccountBalanceEvent.updateAccountBalance(
          TradingSocketEvent.accountBalance.subscribe,
        ),
      );
      _isSubscribed = true;
    }
  }

  void _unsubscribe() {
    if (_isSubscribed) {
      try {
        diContainer<UpdateTradingAccountBalanceHubUseCase>().call(
          eventType: TradingSocketEvent.accountBalance.unsubscribe,
        );
      } catch (e) {
        diContainer<LoggerBase>().logError(e, stackTrace: StackTrace.current);
      }
      _isSubscribed = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<AccountBalanceBloc, AccountBalanceState>(
      listenWhen:
          (previous, current) =>
              previous.processState != current.processState &&
              current.processState is AccountBalanceProcessStateConnected,
      listener: (listenerContext, _) {
        listenerContext.read<AccountBalanceBloc>().add(
          AccountBalanceEvent.updateAccountBalance(
            TradingSocketEvent.accountBalance.subscribe,
          ),
        );
        _isSubscribed = true;
      },
      buildWhen:
          (previous, current) =>
              previous.tradingAccountModel != current.tradingAccountModel,
      builder: (blocSelectorContext, state) {
        final account = state.tradingAccountModel;

        return DuploTopChartNumbersWidget(
          title: EquitiLocalization.of(context).trader_totalEquity,
          amount: account.equity ?? 0,
          profitLoss: account.grossProfit ?? 0,
          currency: account.homeCurrency,
        );
      },
    );
  }

  @override
  void dispose() {
    _unsubscribe();
    super.dispose();
  }

  @override
  void onRoutePopped(Route<Object?> route) {
    _subscribe();
    super.onRoutePopped(route);
  }

  @override
  void onRoutePushed(Route<Object?> route) {
    _unsubscribe();
    super.onRoutePushed(route);
  }
}
