import 'package:duplo/duplo.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/presentation/performance_screen/bloc/performance_bloc.dart';
import 'package:e_trader/src/presentation/performance_screen/funding_tab/funding_tab.dart';
import 'package:e_trader/src/presentation/performance_screen/statements_tab/statements_tab.dart';
import 'package:e_trader/src/presentation/performance_screen/trading_tab/trading_tab.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class PerformanceScreen extends StatefulWidget {
  const PerformanceScreen({super.key, this.tabIndex = 0});
  final int tabIndex;

  @override
  State<PerformanceScreen> createState() => _PerformanceScreenState();
}

class _PerformanceScreenState extends State<PerformanceScreen>
    with SingleTickerProviderStateMixin {
  TabController? _tabController;

  @override
  void dispose() {
    _tabController?.dispose();
    super.dispose();
  }

  void _initTabController(int tabCount) {
    if (_tabController == null) {
      _tabController = TabController(
        length: tabCount,
        vsync: this,
        initialIndex: widget.tabIndex < tabCount ? widget.tabIndex : 0,
      );
    } else if (_tabController!.length != tabCount) {
      _tabController!.dispose();
      _tabController = TabController(
        length: tabCount,
        vsync: this,
        initialIndex: widget.tabIndex < tabCount ? widget.tabIndex : 0,
      );
    }
  }

  @override
  Widget build(BuildContext buildContext) {
    final theme = buildContext.duploTheme;
    final localization = EquitiLocalization.of(buildContext);
    final duploTextStyles = buildContext.duploTextStyles;

    return Scaffold(
      backgroundColor: theme.background.bgSecondary,
      body: BlocProvider(
        create:
            (_) =>
                diContainer<PerformanceBloc>()..add(PerformanceEvent.started()),
        child: BlocBuilder<PerformanceBloc, PerformanceState>(
          buildWhen:
              (previous, current) =>
                  previous.processState != current.processState,
          builder: (context, state) {
            return switch (state.processState) {
              PerformanceError() => Center(
                child: DuploText(
                  text: localization.trader_somethingWentWrong,
                  style: context.duploTextStyles.textSm,
                ),
              ),
              PerformanceLoading() => const Center(
                child: CircularProgressIndicator(),
              ),
              PerformanceSuccess() => () {
                final isDemo = state.accountModel?.isDemo ?? false;
                final tabCount = isDemo ? 2 : 3;
                _initTabController(tabCount);
                final tabTitles = [
                  localization.trader_trading,
                  localization.trader_funding,
                  if (!isDemo) localization.trader_statements,
                ];

                final tabViews = [
                  TradingTab(key: UniqueKey()),
                  FundingTab(key: UniqueKey()),
                  if (!isDemo) StatementsTab(key: UniqueKey()),
                ];

                return Column(
                  children: [
                    TabBar(
                      controller: _tabController,
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      dividerColor: theme.border.borderSecondary,
                      indicatorColor: theme.foreground.fgBrandPrimaryAlt,
                      labelColor: theme.text.textBrandSecondary,
                      labelStyle: TextStyle(
                        fontSize: duploTextStyles.textSm.fontSize,
                        fontWeight: DuploFontWeight.semiBold.value,
                      ),
                      indicatorSize: TabBarIndicatorSize.tab,
                      unselectedLabelColor: theme.text.textQuaternary,
                      splashFactory: null,
                      overlayColor: WidgetStateProperty.all(Colors.transparent),
                      tabAlignment: TabAlignment.fill,
                      unselectedLabelStyle: TextStyle(
                        fontSize: duploTextStyles.textSm.fontSize,
                        fontWeight: DuploFontWeight.medium.value,
                      ),
                      isScrollable: false,
                      tabs: tabTitles.map((e) => Tab(text: e)).toList(),
                    ),
                    Expanded(
                      child: TabBarView(
                        physics: const NeverScrollableScrollPhysics(),
                        controller: _tabController,
                        children: tabViews,
                      ),
                    ),
                    // DuploTabBar(
                    //   tabController: _tabController!,
                    //   initialIndex:
                    //       widget.tabIndex < tabCount ? widget.tabIndex : 0,
                    //   tabTitles: tabTitles,
                    //   isScrollable: false,
                    //   tabViews: tabViews,
                    //   isFlex: true,
                    // ),
                  ],
                );
              }(),
            };
          },
        ),
      ),
    );
  }
}
