import 'package:duplo/duplo.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;
import 'package:e_trader/src/data/api/trading_socket_event.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/domain/usecase/update_positions_use_case.dart';
import 'package:e_trader/src/presentation/portfolio/positions/bloc/position_bloc.dart';
import 'package:e_trader/src/presentation/portfolio_position/widgets/trades_loading.dart';
import 'package:e_trader/src/presentation/positions_and_trades/trade_tile.dart';
import 'package:e_trader/src/presentation/trade_options_view/trade_options_screen.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:monitoring/monitoring.dart';
import 'package:prelude/prelude.dart';
import 'package:visibility_detector/visibility_detector.dart';

class TradesTab extends StatefulWidget {
  const TradesTab({super.key, this.scrollController});
  final ScrollController? scrollController;

  @override
  State<TradesTab> createState() => _TradesTabState();
}

class _TradesTabState extends State<TradesTab> with PerformanceObserverMixin {
  bool _hasSubscribed = false;

  @override
  void onRoutePopped(Route<Object?> route) {
    _subscribe();
    super.onRoutePopped(route);
  }

  @override
  void onRoutePushed(Route<Object?> route) {
    _unsubscribe();
    super.onRoutePushed(route);
  }

  void _subscribe() {
    if (!_hasSubscribed) {
      context.read<PositionBloc>().add(
        PositionEvent.updatePositions(TradingSocketEvent.positions.subscribe),
      );
      _hasSubscribed = true;
    }
  }

  void _unsubscribe() {
    if (_hasSubscribed) {
      try {
        diContainer<UpdatePositionsUseCase>().call(
          eventType: TradingSocketEvent.positions.unsubscribe,
        );
      } catch (e) {
        diContainer<LoggerBase>().logError(e, stackTrace: StackTrace.current);
      }
      _hasSubscribed = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    final localization = EquitiLocalization.of(context);

    return VisibilityDetector(
      key: const Key('trades_tab'),
      onVisibilityChanged: (info) {
        if (info.visibleFraction > 0) {
          if (!_hasSubscribed) {
            context.read<PositionBloc>().add(PositionEvent.loadPositions());
            _hasSubscribed = true;
          }
        } else {
          _unsubscribe();
        }
      },
      child: SizedBox(
        child: CustomScrollView(
          slivers: [
            BlocConsumer<PositionBloc, PositionState>(
              listenWhen:
                  (previous, current) =>
                      previous.processState != current.processState &&
                      current.processState is PositionConnectedState,
              listener:
                  (listenerContext, state) =>
                      listenerContext.read<PositionBloc>().add(
                        PositionEvent.updatePositions(
                          TradingSocketEvent.positions.subscribe,
                        ),
                      ),
              buildWhen: (previous, current) => previous != current,
              builder:
                  (blocBuilderContext, state) => switch (state.processState) {
                    PositionLoadingState() => const TradesLoading(),
                    PositionEmptyState() => SliverFillRemaining(
                      hasScrollBody: false,
                      child: EmptyOrErrorStateComponent.empty(
                        svgImage:
                            trader.Assets.images.portfolioEmptyTradeList.svg(),
                        title: localization.trader_noOpenTrades,
                        description: localization.trader_noOpenTradesMessage,
                      ),
                    ),
                    PositionErrorState() => SliverFillRemaining(
                      hasScrollBody: false,
                      child: EmptyOrErrorStateComponent.error(
                        description:
                            localization.trader_insightsErrorDescription,
                        title: localization.trader_somethingWentWrong,
                        svgImage: trader.Assets.images.bug.svg(),
                        retryButtonText: localization.trader_reload,
                        onTapRetry: () {
                          context.read<PositionBloc>().add(
                            PositionEvent.loadPositions(),
                          );
                        },
                      ),
                    ),
                    PositionSuccessState() || PositionConnectedState() => () {
                      if (state.positions.isEmpty) {
                        return SliverFillRemaining(
                          hasScrollBody: false,
                          child: EmptyOrErrorStateComponent.empty(
                            svgImage:
                                trader.Assets.images.portfolioEmptyTradeList
                                    .svg(),
                            title: localization.trader_noOpenTrades,
                            description:
                                localization.trader_noOpenTradesMessage,
                          ),
                        );
                      }
                      return SliverMainAxisGroup(
                        slivers: [
                          SliverList.builder(
                            itemCount: state.positions.values.length,
                            itemBuilder: (listViewContext, index) {
                              final position =
                                  state.positions.values.elementAtOrNull(
                                    index,
                                  )!;
                              return InkWell(
                                onTap: () {
                                  showTradeOptionsSheet(
                                    context: listViewContext,
                                    positionId: position.positionId,
                                    currency: state.selectedAccountCurrency,
                                  );
                                },
                                child: TradeTile(
                                  digits: position.digits,
                                  lots: position.lotSize,
                                  tradeType: position.positionType,
                                  profit: position.profit!,
                                  priceChange:
                                      position.positionType == TradeType.buy
                                          ? position.buyPercentage
                                          : position.sellPercentage,
                                  currentPrice: position.currentPrice,
                                  tpValue: position.takeProfit,
                                  slValue: position.stopLoss,
                                  productName: position.symbol,
                                  productIcon: position.productLogoUrl,
                                  currency: state.selectedAccountCurrency,
                                ),
                              );
                            },
                          ),
                        ],
                      );
                    }(),
                  },
            ),
          ],
        ),
      ),
    );
  }
}
