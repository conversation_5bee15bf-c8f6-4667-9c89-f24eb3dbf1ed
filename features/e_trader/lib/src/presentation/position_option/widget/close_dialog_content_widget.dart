part of 'show_position_option_sheet.dart';

class _CloseDialogContentWidget extends StatefulWidget {
  const _CloseDialogContentWidget({
    required this.positionOptionBloc,
    required this.parentContext,
    required this.type,
    required this.currency,
  });

  final PositionOptionBloc positionOptionBloc;
  final BuildContext parentContext;
  final FilteredPositionType type;
  final String? currency;

  @override
  State<_CloseDialogContentWidget> createState() =>
      _CloseDialogContentWidgetState();
}

class _CloseDialogContentWidgetState extends State<_CloseDialogContentWidget> {
  late FilteredPositionType _currentType;

  final List<FilteredPositionType> _tabTypes = [
    FilteredPositionType.all,
    FilteredPositionType.buy,
    FilteredPositionType.sell,
    FilteredPositionType.winning,
    FilteredPositionType.losing,
  ];

  @override
  void initState() {
    super.initState();
    _currentType = widget.type;
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final localization = EquitiLocalization.of(context);
    return BlocProvider.value(
      value: widget.positionOptionBloc,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Tab Bar
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.background.bgSecondaryAlt,

              border: Border(
                top: BorderSide(color: theme.border.borderSecondary, width: 1),
              ),
            ),

            child: DuploHorizontalTabs<FilteredPositionType>.buttonBorder(
              options: _tabTypes,
              selectedValue: _currentType,
              itemPadding: EdgeInsets.symmetric(vertical: 8, horizontal: 4),
              showDividers: true,
              onChanged: (value) {
                setState(() {
                  _currentType = value;
                });
              },
              textBuilder:
                  (type) => _getLocalizedTabLabels(context)[type] ?? '',
            ),
          ),

          Expanded(
            child: BlocBuilder<PositionOptionBloc, PositionOptionState>(
              bloc: widget.positionOptionBloc,
              buildWhen:
                  (previous, current) =>
                      previous.groupedPositions != current.groupedPositions,
              builder: (blocBuilderContext, state) {
                final filteredPositions = _getFilteredPositions(state);

                if (filteredPositions.isEmpty) {
                  return Container(
                    color: theme.background.bgSecondaryAlt,

                    child: Center(
                      child: EmptyOrErrorStateComponent.empty(
                        svgImage:
                            trader.Assets.images.portfolioEmptyTradeList.svg(),
                        title: localization.trader_noOpenTrades,
                        description: localization.trader_noOpenTradesMessage,
                      ),
                    ),
                  );
                }

                return Container(
                  color: theme.background.bgSecondaryAlt,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Scrollable trade tiles
                      Expanded(
                        child: SingleChildScrollView(
                          physics: const ClampingScrollPhysics(),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              ...filteredPositions.map((position) {
                                return TradeTile(
                                  tpValue: position.takeProfit,
                                  slValue: position.stopLoss,
                                  lots: position.lotSize,
                                  tradeType: position.positionType,
                                  profit: position.profit!,
                                  priceChange:
                                      position.positionType == TradeType.buy
                                          ? position.buyPercentage
                                          : position.sellPercentage,
                                  currentPrice: position.currentPrice,
                                  productIcon: position.productLogoUrl,
                                  productName: position.symbol,
                                  digits: position.digits,
                                  currency: widget.currency,
                                );
                              }).toList(),
                            ],
                          ),
                        ),
                      ),

                      // Fixed bottom section
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Checkbox row
                          const SizedBox(height: 8),

                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              BlocSelector<
                                PositionOptionBloc,
                                PositionOptionState,
                                bool
                              >(
                                bloc: widget.positionOptionBloc,
                                selector:
                                    (selectorState) =>
                                        selectorState
                                            .shouldHideClosePositionsDialog,
                                builder:
                                    (builderContext, checkboxState) => Checkbox(
                                      activeColor:
                                          DuploTheme.of(
                                            builderContext,
                                          ).background.bgBrandSolid,
                                      value: checkboxState,
                                      onChanged:
                                          (
                                            value,
                                          ) => widget.positionOptionBloc.add(
                                            PositionOptionEvent.toggleClosePositionDialogVisibility(
                                              value!,
                                            ),
                                          ),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(4),
                                      ),
                                      materialTapTargetSize:
                                          MaterialTapTargetSize.shrinkWrap,
                                    ),
                              ),
                              Flexible(
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                    vertical: 8,
                                  ),
                                  child: DuploText(
                                    text:
                                        EquitiLocalization.of(
                                          context,
                                        ).trader_automaticallyCloseTradesMessage,
                                    style: DuploTextStyles.of(context).textSm,
                                    color:
                                        DuploTheme.of(
                                          context,
                                        ).text.textSecondary,
                                    textAlign:
                                        Directionality.of(context) ==
                                                TextDirection.rtl
                                            ? TextAlign.right
                                            : TextAlign.left,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          // Divider
                          Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Divider(
                              color: context.duploTheme.border.borderSecondary,
                              thickness: 1,
                            ),
                          ),

                          // Profit display
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                DuploText(
                                  text:
                                      "${EquitiLocalization.of(context).trader_profit}:",
                                  style: context.duploTextStyles.textXs,
                                  color: context.duploTheme.text.textTertiary,
                                ),
                                BlocBuilder<
                                  PositionOptionBloc,
                                  PositionOptionState
                                >(
                                  bloc: widget.positionOptionBloc,
                                  buildWhen:
                                      (previous, current) =>
                                          previous.groupedPositions !=
                                          current.groupedPositions,
                                  builder: (builderContext, profitState) {
                                    final profit = _getFilteredPositionsProfit(
                                      profitState,
                                      _currentType,
                                    );
                                    final profitColor =
                                        (profit != null && profit >= 0)
                                            ? builderContext
                                                .duploTheme
                                                .text
                                                .textSuccessPrimary
                                            : builderContext
                                                .duploTheme
                                                .text
                                                .textErrorPrimary;
                                    return Row(
                                      children: [
                                        DuploText(
                                          text:
                                              profit != null
                                                  ? "${profit > 0 ? "+" : ""}${profit.toStringAsFixed(2)}"
                                                  : "0",
                                          style:
                                              builderContext
                                                  .duploTextStyles
                                                  .textXs,
                                          color: profitColor,
                                          fontWeight: DuploFontWeight.semiBold,
                                        ),
                                        DuploText(
                                          text:
                                              " ${profitState.accountCurrency ?? "USD"}",
                                          style:
                                              builderContext
                                                  .duploTextStyles
                                                  .textXxs,
                                          color: profitColor,
                                        ),
                                      ],
                                    );
                                  },
                                ),
                              ],
                            ),
                          ),

                          const SizedBox(height: 8),
                        ],
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
          BlocBuilder<PositionOptionBloc, PositionOptionState>(
            bloc: widget.positionOptionBloc,
            buildWhen:
                (previous, current) =>
                    previous.groupedPositions != current.groupedPositions,
            builder: (blocBuilderContext, state) {
              final filteredPositions = _getFilteredPositions(state);
              return Container(
                color: blocBuilderContext.duploTheme.background.bgSecondaryAlt,

                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Padding(
                      padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
                      child: DuploButton.defaultPrimary(
                        isDisabled: filteredPositions.isEmpty,
                        title:
                            "${EquitiLocalization.of(blocBuilderContext).trader_close_title} ${_getSubTitle()}",
                        onTap: () {
                          _getCorrectOnPressed(
                            blocBuilderContext,
                            _currentType,
                            EquitiLocalization.of(blocBuilderContext),
                          ).call();

                          Navigator.of(blocBuilderContext).pop();
                        },
                      ),
                    ),
                    const SizedBox(height: 8.0),
                    Padding(
                      padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                      child: DuploButton.secondary(
                        title: EquitiLocalization.of(context).trader_cancel,
                        onTap: () {
                          blocBuilderContext.read<PositionOptionBloc>().add(
                            const PositionOptionEvent.toggleClosePositionDialogVisibility(
                              false,
                            ),
                          );
                          Navigator.of(blocBuilderContext).pop();
                        },
                      ),
                    ),
                    const SizedBox(height: 24),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  // Preserving the original method signature and using _currentType
  List<PositionModel> _getFilteredPositions(PositionOptionState state) {
    switch (_currentType) {
      case FilteredPositionType.buy:
        return state.buyTrades;
      case FilteredPositionType.sell:
        return state.sellTrades;
      case FilteredPositionType.losing:
        return state.losingTrades;
      case FilteredPositionType.winning:
        return state.winningTrades;
      case FilteredPositionType.all:
        return state.groupedPositions?.positions ?? [];
    }
  }

  String _getSubTitle() {
    switch (_currentType) {
      case FilteredPositionType.buy:
        return EquitiLocalization.of(context).trader_allBuyTrades;
      case FilteredPositionType.sell:
        return EquitiLocalization.of(context).trader_allSellTrades;
      case FilteredPositionType.losing:
        return EquitiLocalization.of(context).trader_allLosingTrades;
      case FilteredPositionType.winning:
        return EquitiLocalization.of(context).trader_allWinningTrades;
      case FilteredPositionType.all:
        return EquitiLocalization.of(context).trader_allTrades;
    }
  }

  double? _getFilteredPositionsProfit(
    PositionOptionState state,
    FilteredPositionType type,
  ) {
    switch (type) {
      case FilteredPositionType.buy:
        return GroupedPositions(
          url: state.buyTrades.firstOrNull?.productLogoUrl ?? '',
          symbol: state.buyTrades.firstOrNull?.symbol ?? "",
          positions: state.buyTrades,
        ).totalProfit;
      case FilteredPositionType.sell:
        return GroupedPositions(
          url: state.sellTrades.firstOrNull?.productLogoUrl ?? '',
          symbol: state.sellTrades.firstOrNull?.symbol ?? "",
          positions: state.sellTrades,
        ).totalProfit;
      case FilteredPositionType.losing:
        return GroupedPositions(
          url: state.losingTrades.firstOrNull?.productLogoUrl ?? '',
          symbol: state.losingTrades.firstOrNull?.symbol ?? "",
          positions: state.losingTrades,
        ).totalProfit;
      case FilteredPositionType.winning:
        return GroupedPositions(
          url: state.winningTrades.firstOrNull?.productLogoUrl ?? '',
          symbol: state.winningTrades.firstOrNull?.symbol ?? "",
          positions: state.winningTrades,
        ).totalProfit;
      case FilteredPositionType.all:
        return state.groupedPositions?.totalProfit;
    }
  }

  VoidCallback _getCorrectOnPressed(
    BuildContext onPressedContext,
    FilteredPositionType type,
    EquitiLocalization localization,
  ) {
    return () {
      switch (type) {
        case FilteredPositionType.buy:
          onPressedContext.read<PositionOptionBloc>().add(
            PositionOptionEvent.closeBuyTrades(
              localization.trader_closedAllBuyTrades,
            ),
          );
          break;
        case FilteredPositionType.sell:
          onPressedContext.read<PositionOptionBloc>().add(
            PositionOptionEvent.closeSellTrades(
              localization.trader_closedAllSellTrades,
            ),
          );
          break;
        case FilteredPositionType.losing:
          onPressedContext.read<PositionOptionBloc>().add(
            PositionOptionEvent.closeLosingTrades(
              localization.trader_closedAllLosingTrades,
            ),
          );
          break;
        case FilteredPositionType.winning:
          onPressedContext.read<PositionOptionBloc>().add(
            PositionOptionEvent.closeWinningTrades(
              localization.trader_closedAllWinningTrades,
            ),
          );
          break;
        case FilteredPositionType.all:
          onPressedContext.read<PositionOptionBloc>().add(
            PositionOptionEvent.closeAllTrades(
              localization.trader_closedAllTrades,
            ),
          );
          break;
      }
    };
  }

  Map<FilteredPositionType, String> _getLocalizedTabLabels(
    BuildContext context,
  ) {
    final localization = EquitiLocalization.of(context);

    return {
      FilteredPositionType.all: localization.trader_all,
      FilteredPositionType.buy: localization.trader_buy,
      FilteredPositionType.sell: localization.trader_sell,
      FilteredPositionType.winning: localization.trader_winners,
      FilteredPositionType.losing: localization.trader_losers,
    };
  }
}
