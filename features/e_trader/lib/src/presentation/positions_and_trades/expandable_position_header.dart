import 'package:e_trader/src/data/socket/position_model.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/presentation/positions_and_trades/position_header.dart';
import 'package:flutter/material.dart';

class ExpandablePositionHeader extends StatelessWidget {
  const ExpandablePositionHeader({
    super.key,
    required this.productName,
    required this.productIcon,
    required this.profit,
    required this.margin,
    required this.isHedging,
    required this.tradeType,
    required this.lots,
    required this.tradesData,
    this.onTap,
    this.currency,
  });
  final String productName;
  final String productIcon;
  final double profit;
  final double margin;
  final bool isHedging;
  final TradeType? tradeType;
  final double lots;
  final List<PositionModel> tradesData;
  final VoidCallback? onTap;
  final String? currency;

  @override
  Widget build(BuildContext context) {
    return PositionHeader(
      productName: productName,
      profit: profit,
      margin: margin,
      productIcon: productIcon,
      isHedging: isHedging,
      tradeType: tradeType,
      lots: lots,
      tradesDataModel: tradesData,
      onTap: onTap,
      currency: currency,
    );
  }
}
