import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:duplo/duplo.dart';
import 'package:e_trader/src/data/socket/position_model.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/presentation/positions_and_trades/widgets/buy_sell_lots.dart';
import 'package:e_trader/src/presentation/positions_and_trades/widgets/hedging_widget.dart';
import 'package:flutter/material.dart';

class PositionHeader extends StatefulWidget {
  final String productName;
  final String productIcon;
  final double profit;
  final double? margin;
  final bool isHedging;
  final TradeType? tradeType;
  final double lots;
  final List<PositionModel> tradesDataModel;
  final String? currency;
  final VoidCallback? onTap;

  const PositionHeader({
    super.key,
    required this.productName,
    required this.profit,
    this.margin,
    required this.productIcon,
    required this.isHedging,
    required this.tradeType,
    required this.lots,
    this.tradesDataModel = const [],
    this.currency,
    this.onTap,
  });

  @override
  State<PositionHeader> createState() => _PositionHeaderState();
}

class _PositionHeaderState extends State<PositionHeader> {
  final ValueNotifier<bool> _isExpanded = ValueNotifier(false);

  @override
  void dispose() {
    _isExpanded.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return _buildHeaderContent(context);
  }

  Widget _buildHeaderContent(BuildContext context) {
    final theme = DuploTheme.of(context);
    final profitColor =
        widget.profit >= 0
            ? theme.text.textSuccessPrimary
            : theme.text.textErrorPrimary;

    final String profitWhole =
        widget.profit.toStringAsFixed(2).split('.').firstOrNull!;
    final String profitFraction =
        widget.profit.toStringAsFixed(2).split('.').elementAtOrNull(1)!;

    String marginWhole = "";
    String marginFraction = "";
    if (widget.margin != null) {
      marginWhole = widget.margin!.toStringAsFixed(2).split('.').firstOrNull!;
      marginFraction =
          widget.margin!.toStringAsFixed(2).split('.').elementAtOrNull(1)!;
    }

    return InkWell(
      onTap: widget.onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 18),
        decoration: BoxDecoration(
          color: theme.background.bgPrimary,
          border: Border.all(color: theme.border.borderSecondary),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Platform.environment.containsKey('FLUTTER_TEST')
                    ? CircleAvatar(
                      radius: 16,
                      backgroundColor: theme.text.textErrorPrimary,
                    )
                    : CachedNetworkImage(
                      imageUrl: widget.productIcon,
                      height: 40,
                      width: 40,
                    ),
                const SizedBox(width: 8),
                Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    DuploText(
                      text: widget.productName,
                      style: context.duploTextStyles.textSm,
                      fontWeight: DuploFontWeight.medium,
                      color: theme.text.textPrimary,
                    ),
                    SizedBox(height: 2),
                    Row(
                      children: [
                        widget.isHedging
                            ? HedgingWidget()
                            : BuySellLots(
                              lots: widget.lots,
                              tradeType: widget.tradeType!,
                            ),
                        SizedBox(width: 10),
                      ],
                    ),
                  ],
                ),
              ],
            ),
            Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                DuploText.rich(
                  spans: [
                    DuploTextSpan(
                      text: "${widget.profit > 0 ? "+" : ""}${profitWhole}",
                      style: context.duploTextStyles.textSm,
                      color: profitColor,
                      fontWeight: DuploFontWeight.bold,
                    ),
                    DuploTextSpan(
                      text: "." + profitFraction,
                      style: context.duploTextStyles.textXs,
                      color: profitColor,
                      fontWeight: DuploFontWeight.bold,
                    ),
                    DuploTextSpan(
                      text:
                          widget.currency != null
                              ? " ${widget.currency!}"
                              : " USD",
                      style: context.duploTextStyles.textXs,
                      color: profitColor,
                      fontWeight: DuploFontWeight.regular,
                    ),
                  ],
                ),
                SizedBox(height: 2),
                if (widget.margin != null)
                  DuploText.rich(
                    spans: [
                      DuploTextSpan(
                        text: marginWhole,
                        style: context.duploTextStyles.textSm,
                        color: theme.text.textTertiary,
                        fontWeight: DuploFontWeight.regular,
                      ),
                      DuploTextSpan(
                        text: "." + marginFraction,
                        style: context.duploTextStyles.textXs,
                        color: theme.text.textTertiary,
                        fontWeight: DuploFontWeight.regular,
                      ),
                      DuploTextSpan(
                        text:
                            widget.currency != null
                                ? " ${widget.currency!}"
                                : " USD",
                        style: context.duploTextStyles.textXs,
                        color: theme.text.textTertiary,
                        fontWeight: DuploFontWeight.regular,
                      ),
                    ],
                  ),
                if (widget.margin == null) SizedBox(height: 24),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
