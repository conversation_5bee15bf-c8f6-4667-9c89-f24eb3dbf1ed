import 'package:duplo/duplo.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;
import 'package:e_trader/src/data/api/symbol_quote_model.dart';
import 'package:e_trader/src/data/api/trading_socket_event.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/presentation/buy_sell/buy_sell_buttons.dart';
import 'package:e_trader/src/presentation/create_trade/bloc/create_order/create_trade_bloc.dart';
import 'package:e_trader/src/presentation/create_trade/create_trade_widget.dart';
import 'package:e_trader/src/presentation/discover/events/bloc/events_bloc.dart';
import 'package:e_trader/src/presentation/discover/events/widgets/events_screen.dart';
import 'package:e_trader/src/presentation/discover/news/bloc/news_bloc.dart';
import 'package:e_trader/src/presentation/discover/news/news_tab.dart';
import 'package:e_trader/src/presentation/model/buy_sell_button_state.dart';
import 'package:e_trader/src/presentation/model/symbol_detail_view_model.dart';
import 'package:e_trader/src/presentation/price_alert/price_alert_widget.dart';
import 'package:e_trader/src/presentation/product_detail_overview/widget/product_detail_overview_screen.dart';
import 'package:e_trader/src/presentation/product_details/bloc/product_details_bloc.dart';
import 'package:e_trader/src/presentation/symbols/widgets/symbol_info_widget.dart';
import 'package:e_trader/src/presentation/trading_chart/advance_trading_view.dart';
import 'package:e_trader/src/presentation/watchlisted_symbol_indicator/watchlisted_symbol_indicator_screen.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:smooth_sheets/smooth_sheets.dart';

class ProductDetailsWidget extends StatefulWidget {
  final SymbolDetailViewModel symbolDetail;
  final String accountNumber;

  const ProductDetailsWidget({
    super.key,
    required this.symbolDetail,
    required this.accountNumber,
  });

  @override
  State<ProductDetailsWidget> createState() => _ProductDetailsWidgetState();
}

class _ProductDetailsWidgetState extends State<ProductDetailsWidget> {
  late final SheetController _sheetController;

  @override
  void initState() {
    super.initState();
    _sheetController = SheetController();
  }

  @override
  void dispose() {
    _sheetController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = EquitiLocalization.of(context);
    final theme = context.duploTheme;

    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create:
              (_) =>
                  diContainer<ProductDetailsBloc>()..add(
                    ProductDetailsEvent.fetchSymbolInfo(
                      widget.symbolDetail.symbolName,
                    ),
                  ),
        ),
        BlocProvider(
          create:
              (_) =>
                  diContainer<NewsBloc>(param1: widget.symbolDetail.symbolName)
                    ..add(NewsEvent.fetchNews()),
        ),
        BlocProvider(
          create:
              (_) => diContainer<EventsBloc>(
                param1: widget.symbolDetail.symbolName,
              )..add(EventsEvent.fetchEvents()),
        ),
        BlocProvider(
          create:
              (_) => diContainer<CreateTradeBloc>(
                param1: (
                  digits: widget.symbolDetail.digit ?? 2,
                  symbolCode: widget.symbolDetail.symbolName,
                  symbolImageUrl: widget.symbolDetail.imageURL ?? "",
                  minLot: widget.symbolDetail.minLot,
                  maxLot: widget.symbolDetail.maxLot,
                  isForex: widget.symbolDetail.isForex,
                ),
              )..add(
                CreateTradeEvent.subscribe(
                  orderSize: widget.symbolDetail.minLot,
                  eventType: TradingSocketEvent.marginRequirements.register,
                ),
              ),
        ),
      ],
      child: Scaffold(
        backgroundColor: theme.background.bgPrimary,
        appBar: DuploAppBar(
          title: '',
          titleWidget: SymbolInfoWidget(symbol: widget.symbolDetail),
          actions: [
            DuploIconButton.customColorMode(
              onTap: () {
                DuploSheet.showModalSheetV2<void>(
                  context,
                  appBar: DuploAppBar(
                    title: l10n.trader_priceAlert,
                    automaticallyImplyLeading: false,
                    duploAppBarTextAlign: DuploAppBarTextAlign.left,
                    actions: [
                      IconButton(
                        icon: Assets.images.closeIc.svg(),
                        onPressed: () => Navigator.pop(context),
                      ),
                    ],
                  ),
                  content: PriceAlertWidget(
                    symbol: widget.symbolDetail.symbolName,
                    symbolImageUrl: widget.symbolDetail.imageURL ?? "",
                  ),
                );
              },
              colorMode: ColorMode.dynamicMode,
              icon: trader.Assets.images.priceAlertClock.keyName,
            ),
            const SizedBox(width: 8),
            WatchlistedSymbolIndicatorScreen(
              symbolCode: widget.symbolDetail.symbolName,
              colorMode: ColorMode.dynamicMode,
            ),
            const SizedBox(width: 16),
          ],
        ),
        body: BlocBuilder<ProductDetailsBloc, ProductDetailsState>(
          buildWhen: (previous, current) => previous != current,
          builder: (blocBuilderContext, state) {
            final info = switch (state) {
              ProductDetailsSuccess(info: final successInfo) => successInfo,
              _ => null,
            };

            return LayoutBuilder(
              builder: (layoutBuilderContext, constraints) {
                final tabHeight = constraints.maxHeight * 0.8;

                return Stack(
                  children: [
                    Positioned(
                      top: 0,
                      left: 0,
                      right: 0,
                      height: tabHeight,
                      child: ColoredBox(
                        color: theme.background.bgSecondary,
                        child: DuploTabBar(
                          tabTitles: [
                            DuploTabBarTitle(
                              text: "Chart",
                              semanticsIdentifier: 'chart_tab',
                            ),
                            DuploTabBarTitle(
                              text: l10n.trader_news,
                              semanticsIdentifier: 'news_tab',
                            ),
                            DuploTabBarTitle(
                              text: l10n.trader_events,
                              semanticsIdentifier: 'events_tab',
                            ),
                            DuploTabBarTitle(
                              text: l10n.trader_details,
                              semanticsIdentifier: 'details_tab',
                            ),
                          ],
                          isScrollable: true,
                          tabViews: [
                            AdvanceTradingView(
                              symbol: widget.symbolDetail.symbolName,
                              digit: widget.symbolDetail.digit ?? 5,
                              sheetController: _sheetController,
                            ),
                            NewsTab(isInMarketDetails: true),
                            EventsScreen(isInMarketDetails: true),
                            ProductDetailOverviewScreen(
                              symbol: widget.symbolDetail.symbolName,
                              accountNumber: widget.accountNumber,
                              digit: widget.symbolDetail.digit ?? 5,
                            ),
                          ],
                          isFlex: false,
                        ),
                      ),
                    ),
                    Positioned.fill(
                      child: SheetViewport(
                        child: Sheet(
                          dragConfiguration: SheetDragConfiguration(
                            hitTestBehavior: HitTestBehavior.opaque,
                          ),
                          scrollConfiguration: SheetScrollConfiguration(),
                          controller: _sheetController,
                          decoration: MaterialSheetDecoration(
                            size: SheetSize.fit,
                            borderRadius: const BorderRadius.vertical(
                              top: Radius.circular(24),
                            ),
                            color: theme.background.bgPrimary,
                          ),
                          physics: BouncingSheetPhysics(
                            behavior: DirectionAwareBouncingBehavior(
                              upward: SheetOffset(0),
                            ),
                          ),
                          initialOffset: SheetOffset(0.2),
                          snapGrid: SheetSnapGrid(
                            snaps: [
                              SheetOffset(0.2), // Shows exactly BuySellButtons
                              SheetOffset(1.0), // Full height
                            ],
                          ),
                          child: Container(
                            decoration: BoxDecoration(
                              border: Border.all(
                                color: theme.border.borderSecondary,
                              ),
                              borderRadius: const BorderRadius.vertical(
                                top: Radius.circular(24),
                              ),
                            ),
                            child: Column(
                              children: [
                                DraggableHandle(),
                                const SizedBox(height: 12),
                                Expanded(
                                  child: Padding(
                                    padding: const EdgeInsets.only(
                                      left: 16.0,
                                      right: 16,
                                      bottom: 16,
                                    ),
                                    child: _PlaceTradeSheetWidget(
                                      sheetController: _sheetController,
                                      symbolQuoteModel: info,
                                      symbolDetail: widget.symbolDetail,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                );
              },
            );
          },
        ),
      ),
    );
  }
}

class _PlaceTradeSheetWidget extends StatefulWidget {
  const _PlaceTradeSheetWidget({
    required SheetController sheetController,
    required this.symbolQuoteModel,
    required this.symbolDetail,
  }) : _sheetController = sheetController;

  final SheetController _sheetController;
  final SymbolQuoteModel? symbolQuoteModel;
  final SymbolDetailViewModel symbolDetail;

  @override
  State<_PlaceTradeSheetWidget> createState() => _PlaceTradeSheetWidgetState();
}

class _PlaceTradeSheetWidgetState extends State<_PlaceTradeSheetWidget> {
  late final SheetOffsetDrivenAnimation _sheetAnimation;

  @override
  void initState() {
    super.initState();
    _sheetAnimation = SheetOffsetDrivenAnimation(
      controller: widget._sheetController,
      initialValue: 0.0, // Start hidden when collapsed
    );
    _sheetAnimation.addListener(_sheetAnimationListener);
  }

  void _sheetAnimationListener() {
    final currentOffset = widget._sheetController.metrics?.offset ?? 0.0;
    final minOffset = widget._sheetController.metrics?.minOffset ?? 0.0;
    final maxOffset = widget._sheetController.metrics?.maxOffset ?? 1.0;

    // Calculate the threshold as a percentage between min and max
    // Reset when sheet is 95% of the way down from fully expanded
    const resetThresholdPercentage = 0.95;
    final resetThreshold =
        maxOffset - (maxOffset - minOffset) * resetThresholdPercentage;

    // Reset trade selection when sheet crosses the threshold while collapsing
    if (currentOffset <= resetThreshold) {
      if (context.read<CreateTradeBloc>().state.tradeType != null) {
        context.read<CreateTradeBloc>().add(
          CreateTradeEvent.resetTradeSelection(),
        );
      }
    }
  }

  dispose() {
    _sheetAnimation.removeListener(_sheetAnimationListener);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;

    return BlocBuilder<CreateTradeBloc, CreateTradeState>(
      buildWhen: (previous, current) => previous != current,
      builder:
          (builderContext, createTradeState) => Container(
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(24),
              ),
            ),
            child: SheetContentScaffold(
              backgroundColor: theme.background.bgPrimary,
              topBar: AnimatedBuilder(
                animation: _sheetAnimation,
                builder: (animatedBuilderContext, child) {
                  return BuySellButtons(
                    onTap: (tradeType) {
                      widget._sheetController.animateTo(
                        SheetOffset.absolute(
                          widget._sheetController.metrics?.maxOffset ?? 1.0,
                        ),
                        curve: Curves.fastOutSlowIn,
                      );
                      if (tradeType != createTradeState.tradeType) {
                        animatedBuilderContext.read<CreateTradeBloc>().add(
                          CreateTradeEvent.updateTradeType(tradeType),
                        );
                      }
                    },
                    spread: widget.symbolQuoteModel?.spread ?? 0,
                    digits: widget.symbolQuoteModel?.digits ?? 2,
                    buyButtonState:
                        createTradeState.buyButtonState() ??
                        BuySellButtonState.active(
                          widget.symbolQuoteModel?.ask ?? 0.0,
                        ),
                    sellButtonState:
                        createTradeState.sellButtonState() ??
                        BuySellButtonState.active(
                          widget.symbolQuoteModel?.bid ?? 0.0,
                        ),
                  );
                },
              ),
              body: AnimatedBuilder(
                animation: _sheetAnimation,
                child: CreateTradeWidget(
                  key: ValueKey(createTradeState.tradeType),
                  args: (
                    digits: widget.symbolQuoteModel?.digits ?? 2,
                    symbolCode: widget.symbolDetail.symbolName,
                    symbolImageUrl: widget.symbolDetail.imageURL ?? "",
                    minLot: widget.symbolDetail.minLot,
                    maxLot: widget.symbolDetail.maxLot,
                    isForex: widget.symbolDetail.isForex,
                  ),
                ),
                builder: (animationContext, child) {
                  return FadeTransition(opacity: _sheetAnimation, child: child);
                },
              ),
            ),
          ),
    );
  }
}
