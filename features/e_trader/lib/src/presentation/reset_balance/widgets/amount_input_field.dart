import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/presentation/reset_balance/bloc/reset_balance_bloc.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:theme_manager/theme_manager.dart';

class AmountInputField extends StatelessWidget {
  const AmountInputField({super.key, this.controller});

  final TextEditingController? controller;

  @override
  Widget build(BuildContext context) {
    final state = context.watch<ResetBalanceBloc>().state;
    final localization = EquitiLocalization.of(context);

    return TextField(
      controller: controller,
      keyboardAppearance:
          diContainer<ThemeManager>().isDarkMode
              ? Brightness.dark
              : Brightness.light,
      decoration: InputDecoration(
        border: const OutlineInputBorder(),
        labelText: localization.trader_specifyAmount,
        hintText: localization.trader_amountShouldBeHint,
        errorText: switch (state.currentState) {
          AmountInvalidState() => localization.trader_invalidAmount,
          _ => null,
        },
      ),
      keyboardType: TextInputType.number,
    );
  }
}
