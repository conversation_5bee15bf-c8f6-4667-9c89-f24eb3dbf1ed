import 'dart:async';

import 'package:e_trader/src/data/api/platform_type.dart';
import 'package:e_trader/src/data/api/trading_account_model.dart';
import 'package:e_trader/src/data/api/trading_socket_event.dart';
import 'package:e_trader/src/data/socket/account_balance_hub_response.dart';
import 'package:e_trader/src/domain/usecase/get_selected_account_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_trading_accounts_use_case.dart';
import 'package:e_trader/src/domain/usecase/push_notification_token_use_case.dart';
import 'package:e_trader/src/domain/usecase/save_selected_account_use_case.dart';
import 'package:e_trader/src/domain/usecase/subscribe_to_trading_account_balance_use_case.dart';
import 'package:e_trader/src/domain/usecase/update_trading_account_balance_hub_use_case.dart';
import 'package:e_trader/src/navigation/equiti_trader_navigation.dart';
import 'package:e_trader/src/presentation/model/account_view_model.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:prelude/prelude.dart';

part 'accounts_bloc.freezed.dart';
part 'accounts_event.dart';
part 'accounts_state.dart';

class AccountsBloc extends Bloc<AccountsEvent, AccountsState>
    with DisposableMixin {
  AccountsBloc(
    this._getTradingAccountsUseCase,
    this._saveSelectedAccountUseCase,
    this._getSelectedAccountUseCase,
    this._subscribeToTradingAccountBalanceUseCase,
    this._updateTradingAccountBalanceHubUseCase,
    this._equitiTraderNavigation,
    this._pushNotificationTokenUseCase,
  ) : super(_AccountsState()) {
    on<AccountsEvent>(
      (event, emit) => switch (event) {
        _FetchAccounts() => _fetchAccounts(emit),
        _OnAccountSelected val => _onAccountSelected(val, emit),
        _GoToSymbols() => _goToSymbols(),
        _GoToDepositPaymentOptions() => _gotToDepositPaymentOptions(),
        _GoToWithdrawPaymentOptions() => _goToWithdrawPaymentOptions(),
        _GoToTransferOptions() => _goToTransferOptions(),
        _FetchWallets() => _fetchWallets(emit),
      },
    );
  }

  final GetTradingAccountsUseCase _getTradingAccountsUseCase;
  final SaveSelectedAccountUseCase _saveSelectedAccountUseCase;
  final GetSelectedAccountUseCase _getSelectedAccountUseCase;
  final PushNotificationTokenUseCase _pushNotificationTokenUseCase;

  final SubscribeToTradingAccountBalanceUseCase
  _subscribeToTradingAccountBalanceUseCase;
  final UpdateTradingAccountBalanceHubUseCase
  _updateTradingAccountBalanceHubUseCase;
  final EquitiTraderNavigation _equitiTraderNavigation;
  final Map<String, double> _accountEquities = {};
  final Map<String, double> _accountProfits = {};
  final Map<String, double> _accountCredits = {};
  final Map<String, double> _accountBalances = {};

  FutureOr<void> _fetchAccounts(Emitter<AccountsState> emit) async {
    final selectedAccount = _getSelectedAccountUseCase();
    final result =
        await TaskEither<
          Exception,
          (List<TradingAccountModel>, Stream<AccountBalanceHubResponse?>)
        >.Do(($) async {
          final accounts = await $(_getTradingAccountsUseCase());
          final accountBalanceStream = await $(
            _subscribeToTradingAccountBalanceUseCase(
              accounts
                  .where(
                    (account) =>
                        account.platformType != PlatformType.equiti &&
                        account.platformType != PlatformType.unknown,
                  )
                  .map((account) => account.accountNumber)
                  .toList(),
              subscriberId: '${AccountsBloc}_$hashCode',
              eventType: TradingSocketEvent.accountBalance.subscribe,
            ),
          );

          return (accounts, accountBalanceStream);
        }).run();
    await result.fold(
      (error) {
        addError(error);
        emit(state.copyWith(processState: AccountsProcessState.error()));
      },
      (value) async {
        final accounts = value.$1;
        final accountBalanceStream = value.$2;

        final wallets =
            accounts
                .where((account) => account.platformType == PlatformType.equiti)
                .map(
                  (accountModel) => AccountViewModel(
                    name: accountModel.name,
                    nickName: accountModel.nickName,
                    homeCurrency: accountModel.homeCurrency,
                    accountId: accountModel.recordId,
                    accountNumber: accountModel.accountNumber,
                    balance: accountModel.balance,
                    credit: accountModel.credit,
                    equity: accountModel.equity,
                    margin: accountModel.margin,
                    platformType: accountModel.platformType,
                    isSelected:
                        selectedAccount?.recordId == accountModel.recordId,
                    platformAccountType: accountModel.platformAccountType,
                    profit: accountModel.profit,
                    marginLevel: accountModel.marginLevel,
                  ),
                )
                .toList();

        final tradingAccounts =
            accounts
                .where(
                  (account) =>
                      account.platformType != PlatformType.equiti &&
                      account.platformType != PlatformType.unknown,
                )
                .map(
                  (accountModel) => AccountViewModel(
                    name: accountModel.name,
                    nickName: accountModel.nickName,
                    homeCurrency: accountModel.homeCurrency,
                    accountId: accountModel.recordId,
                    accountNumber: accountModel.accountNumber,
                    balance: accountModel.balance,
                    credit: accountModel.credit,
                    equity: accountModel.equity,
                    margin: accountModel.margin,
                    platformType: accountModel.platformType,
                    isSelected:
                        selectedAccount?.recordId == accountModel.recordId,
                    platformAccountType: accountModel.platformAccountType,
                    profit: accountModel.profit,
                    marginLevel: accountModel.marginLevel,
                  ),
                )
                .toList();

        emit(
          state.copyWith(
            processState: AccountsProcessState.success(),
            accounts: accounts,
            tradingAccounts: tradingAccounts,
            wallets: wallets,
            totalEquity: accounts.fold<double>(
              0,
              (sum, account) => sum + (account.equityAlternateCurrency ?? 0.0),
            ),
            totalProfit: accounts.fold<double>(
              0,
              (sum, account) => sum + (account.profitAlternateCurrency ?? 0.0),
            ),
            totalCredit: accounts.fold<double>(
              0,
              (sum, account) => sum + (account.creditAlternateCurrency ?? 0.0),
            ),
            totalBalance: accounts.fold<double>(
              0,
              (sum, account) => sum + (account.balanceAlternateCurrency ?? 0.0),
            ),
          ),
        );
        if (accounts.isEmpty) {
          return;
        }

        await emit.forEach(
          accountBalanceStream.distinct(),
          onData: (accountBalanceResponse) {
            final updatedTradingAccounts =
                tradingAccounts
                    .map(
                      (accountModel) => _updateAccountBalance(
                        accountModel,
                        accountBalanceResponse,
                      ),
                    )
                    .toList();

            if (accountBalanceResponse != null) {
              final accountNumber =
                  accountBalanceResponse.account.accountNumber;
              _accountEquities[accountNumber!] =
                  accountBalanceResponse.account.equityAlternateCurrency;
              _accountProfits[accountNumber] =
                  accountBalanceResponse.account.profitAlternateCurrency;
              _accountCredits[accountNumber] =
                  accountBalanceResponse.account.creditAlternateCurrency;
              _accountBalances[accountNumber] =
                  accountBalanceResponse.account.balanceAlternateCurrency;
            }

            final updatedTotalEquity = _accountEquities.values.fold<double>(
              0,
              (sum, equity) => sum + equity,
            );
            final updatedTotalProfit = _accountProfits.values.fold<double>(
              0,
              (sum, profit) => sum + profit,
            );
            final updatedTotalCredit = _accountCredits.values.fold<double>(
              0,
              (sum, credit) => sum + credit,
            );
            final updatedTotalBalance = _accountBalances.values.fold<double>(
              0,
              (sum, balance) => sum + balance,
            );

            return state.copyWith(
              tradingAccounts: updatedTradingAccounts,
              totalEquity: updatedTotalEquity,
              totalProfit: updatedTotalProfit,
              totalCredit: updatedTotalCredit,
              totalBalance: updatedTotalBalance,
            );
          },
          onError: (error, stackTrace) {
            return state.copyWith(processState: AccountsProcessState.error());
          },
        );
      },
    );
  }

  AccountViewModel _updateAccountBalance(
    AccountViewModel accountModel,
    AccountBalanceHubResponse? accountBalanceResponse,
  ) => accountModel.copyWith(
    balance:
        accountBalanceResponse?.account.accountNumber ==
                accountModel.accountNumber
            ? accountBalanceResponse?.account.balance
            : accountModel.balance,
    equity:
        accountBalanceResponse?.account.accountNumber ==
                accountModel.accountNumber
            ? accountBalanceResponse?.account.equity
            : accountModel.equity,
    margin:
        accountBalanceResponse?.account.accountNumber ==
                accountModel.accountNumber
            ? accountBalanceResponse?.account.margin
            : accountModel.margin,
    profit:
        accountBalanceResponse?.account.accountNumber ==
                accountModel.accountNumber
            ? accountBalanceResponse?.account.profit
            : accountModel.profit,
    credit:
        accountBalanceResponse?.account.accountNumber ==
                accountModel.accountNumber
            ? accountBalanceResponse?.account.credit
            : accountModel.credit,
    marginLevel:
        accountBalanceResponse?.account.accountNumber ==
                accountModel.accountNumber
            ? accountBalanceResponse?.account.marginLevel
            : accountModel.marginLevel,
  );

  FutureOr<void> _onAccountSelected(
    _OnAccountSelected val,
    Emitter<AccountsState> emit,
  ) async {
    final accountId = val.accountId;

    final updatedTradingAccounts =
        state.tradingAccounts
            .map(
              (tradingAccount) => tradingAccount.copyWith(
                isSelected: tradingAccount.accountId == accountId,
              ),
            )
            .toList();

    final selectedAccount = state.accounts.firstOrNullWhere(
      (account) => account.recordId == accountId,
    );

    if (selectedAccount != null) {
      _saveSelectedAccountUseCase(selectedAccount);
      await _pushNotificationTokenUseCase(
        accountNumber: selectedAccount.accountNumber,
      ).run();
    }

    emit(state.copyWith(tradingAccounts: updatedTradingAccounts));

    add(AccountsEvent.goToSymbols());
  }

  Future<void> _goToSymbols() async {
    await _updateTradingAccountBalanceHubUseCase(
      eventType: TradingSocketEvent.accountBalance.unsubscribe,
      accountNumbers:
          state.tradingAccounts
              .map((account) => account.accountNumber!)
              .toList(),
    );
    _equitiTraderNavigation.navigateToSymbols();
  }

  void _gotToDepositPaymentOptions() {
    _equitiTraderNavigation.navigateToDepositOptions();
  }

  void _goToWithdrawPaymentOptions() {
    _equitiTraderNavigation.navigateToWithdrawOptions();
  }

  void _goToTransferOptions() {
    _equitiTraderNavigation.goToTransferFundsScreen();
  }

  FutureOr<void> _fetchWallets(Emitter<AccountsState> emit) async {
    final result = await _getTradingAccountsUseCase().run();
    result.fold(
      (error) {
        addError(error);
      },
      (accounts) {
        final selectedAccount = _getSelectedAccountUseCase();
        final wallets =
            accounts
                .where((account) => account.platformType == PlatformType.equiti)
                .map(
                  (accountModel) => AccountViewModel(
                    name: accountModel.name,
                    nickName: accountModel.nickName,
                    homeCurrency: accountModel.homeCurrency,
                    accountId: accountModel.recordId,
                    accountNumber: accountModel.accountNumber,
                    balance: accountModel.balance,
                    credit: accountModel.credit,
                    equity: accountModel.equity,
                    margin: accountModel.margin,
                    platformType: accountModel.platformType,
                    isSelected:
                        selectedAccount?.recordId == accountModel.recordId,
                    platformAccountType: accountModel.platformAccountType,
                    profit: accountModel.profit,
                    marginLevel: accountModel.marginLevel,
                  ),
                )
                .toList();
        emit(state.copyWith(wallets: wallets));
      },
    );
  }
}
