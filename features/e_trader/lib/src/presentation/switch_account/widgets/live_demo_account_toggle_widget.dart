import 'dart:async';

import 'package:duplo/duplo.dart';
import 'package:e_trader/src/domain/model/trading_environment.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';

class LiveDemoAccountToggleWidget extends StatelessWidget {
  final FutureOr<void> Function(TradingEnvironment selected) onChanged;
  final TradingEnvironment initialValue;

  const LiveDemoAccountToggleWidget({
    super.key,
    required this.onChanged,
    this.initialValue = TradingEnvironment.demo,
  });

  @override
  Widget build(BuildContext context) {
    return DuploHorizontalTabs<TradingEnvironment>.buttonBorder(
      options: [TradingEnvironment.live, TradingEnvironment.demo],
      selectedValue: initialValue,
      onChanged: onChanged,
      textBuilder: (option) {
        return option.name == "Demo"
            ? EquitiLocalization.of(context).trader_demo
            : EquitiLocalization.of(context).trader_live;
      },
    );
  }
}
