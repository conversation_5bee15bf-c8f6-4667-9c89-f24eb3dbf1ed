// ignore_for_file: prefer-match-file-name

import 'dart:io';

import 'package:duplo/duplo.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/domain/usecase/symbol_local_data_use_case.dart';
import 'package:e_trader/src/presentation/symbols/bloc/symbols/symbols_bloc.dart';
import 'package:e_trader/src/presentation/symbols/widgets/search/empty_or_error_symbols_view.dart';
import 'package:e_trader/src/presentation/symbols/widgets/search/previous_searches_list.dart';
import 'package:e_trader/src/presentation/symbols/widgets/symbol_list_view.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:prelude/prelude.dart';

void showSearchView({
  required BuildContext parentContext,
  required TextEditingController textController,
  required String title,
}) {
  DuploSheet.showModalSheetV2<void>(
    parentContext,
    settings: RouteSettings(name: 'search_view'),
    appBar: DuploAppBar(
      duploAppBarTextAlign: DuploAppBarTextAlign.left,
      automaticallyImplyLeading: false,
      title: EquitiLocalization.of(parentContext).trader_searchMarkets,
      actions: [
        IconButton(
          icon: Assets.images.closeIc.svg(),
          onPressed: () => Navigator.pop(parentContext),
        ),
      ],
    ),
    content: BlocProvider(
      create: (ctx) => diContainer<SymbolsBloc>(),
      child: BlocConsumer<SymbolsBloc, SymbolsState>(
        buildWhen: (previous, current) => previous != current,
        listenWhen:
            (previous, current) =>
                previous.currentState != current.currentState,
        listener: (listenerContext, state) {
          if (state.currentState is SymbolsErrorState) {
            if (!Platform.environment.containsKey('FLUTTER_TEST')) {
              final toast = DuploToast();
              toast.showToastMessage(
                autoCloseDuration: Duration.zero,
                context: listenerContext,
                widget: DuploToastMessage(
                  titleMessage:
                      EquitiLocalization.of(
                        listenerContext,
                      ).trader_loadingError,
                  descriptionMessage:
                      EquitiLocalization.of(
                        listenerContext,
                      ).trader_loadingErrorDescription,
                  messageType: ToastMessageType.error,
                  onLeadingAction: () => toast.hidesToastMessage(),
                  actionButtonTitle:
                      EquitiLocalization.of(listenerContext).trader_reload,
                  onTap: () {
                    listenerContext.read<SymbolsBloc>().add(
                      SymbolsEvent.onSearchSymbols(textController.text),
                    );
                    toast.hidesToastMessage();
                  },
                ),
              );
            }
          }
        },
        builder:
            (builderContext, state) => _SearchViewContent(
              title: title,
              state: state,
              textController: textController,
            ),
      ),
    ),
  );
}

class _SearchViewContent extends StatefulWidget {
  const _SearchViewContent({
    required this.title,
    required this.state,
    required this.textController,
  });
  final String title;
  final SymbolsState state;
  final TextEditingController textController;

  @override
  State<_SearchViewContent> createState() => __SearchViewContentState();
}

class __SearchViewContentState extends State<_SearchViewContent> {
  late List<String> previousSearches;
  @override
  void initState() {
    super.initState();
    previousSearches =
        diContainer<SymbolLocalDataUseCase>().getPreviousSearches();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: context.duploTheme.background.bgPrimary,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(10),
          topRight: Radius.circular(10),
        ),
      ),
      height: MediaQuery.sizeOf(context).height * 0.9,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: DuploSearchInputField(
              key: const Key('search_box_bottom_sheet'),
              controller: widget.textController,
              hintText: widget.title,
              onChanged: (query) {
                context.read<SymbolsBloc>().add(
                  SymbolsEvent.onSearchSymbols(query),
                );
                if (query.isEmpty) {
                  previousSearches =
                      diContainer<SymbolLocalDataUseCase>()
                          .getPreviousSearches();
                }
              },
            ),
          ),
          Container(
            height: 1,
            color: context.duploTheme.border.borderSecondary,
            width: double.infinity,
          ),
          if (!(widget.textController.text.length < 2) &&
              widget.state.currentState is SymbolsSuccessState &&
              (widget.state.symbolsCount != 0))
            ColoredBox(
              color: context.duploTheme.background.bgSecondary,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: DuploText(
                      text:
                          EquitiFormatter.formatNumber(
                            value: widget.state.symbolsCount,
                            locale: Localizations.localeOf(context).toString(),
                          ) +
                          " " +
                          EquitiLocalization.of(context).trader_resultsFound,
                      color: context.duploTheme.text.textTertiary,
                      style: context.duploTextStyles.textSm,
                    ),
                  ),
                ],
              ),
            ),
          if (widget.textController.text.isEmpty)
            Expanded(
              child: PreviousSearchesList(
                listOfPreviousSearches: previousSearches,
                onTap: (text) {
                  widget.textController.text = text;
                  context.read<SymbolsBloc>().add(
                    SymbolsEvent.onSearchSymbols(text),
                  );
                },
              ),
            ),
          if (widget.textController.text.length > 1)
            Expanded(
              child: SymbolListView(
                isSearchView: true,
                query: widget.textController.text,
                loadingBuilder:
                    (ctx) => Center(
                      child:
                          widget.textController.text.length < 1
                              ? const SizedBox()
                              : DuploShimmerList(
                                hasLeading: true,
                                hasTrailing: true,
                              ),
                    ),
                errorBuilder:
                    (ctx) => EmptyOrErrorSymbolsView(
                      title:
                          EquitiLocalization.of(
                            context,
                          ).trader_somethingWentWrong,
                      message:
                          EquitiLocalization.of(context).trader_searchError,
                      image: trader.Assets.images.searchError.svg(
                        allowDrawingOutsideViewBox: true,
                      ),
                    ),
                emptyBuilder:
                    (ctx) => EmptyOrErrorSymbolsView(
                      title: EquitiLocalization.of(context).trader_nothingFound,
                      message:
                          EquitiLocalization.of(context).trader_noSearchResults,
                      image: trader.Assets.images.emptySearch.svg(
                        allowDrawingOutsideViewBox: true,
                      ),
                    ),
              ),
            ),
        ],
      ),
    );
  }
}
