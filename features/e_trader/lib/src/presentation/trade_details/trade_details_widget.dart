import 'dart:async';

import 'package:duplo/duplo.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/domain/usecase/get_trade_size_from_volume_use_case.dart';
import 'package:e_trader/src/presentation/positions_and_trades/trade_tile.dart';
import 'package:e_trader/src/presentation/trade_details/bloc/trade_details_bloc.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart' as intl;
import 'package:prelude/prelude.dart';

FutureOr<void> showTradeDetailsSheet({
  required BuildContext context,
  required int positionId,
  String? accountCurrency,
}) {
  final l10n = EquitiLocalization.of(context);
  final duploTextStyles = context.duploTextStyles;
  final theme = DuploTheme.of(context);
  final locale = Localizations.localeOf(context).toString();
  return DuploSheet.showModalSheet(
    context: context,
    title: l10n.trader_tradeDetails,
    isFullScreen: true,
    content:
        (_) => BlocProvider(
          create:
              (blocContext) =>
                  diContainer<TradeDetailsBloc>()..add(
                    TradeDetailsEvent.onGetPositions(positionId: positionId),
                  ),
          child: BlocBuilder<TradeDetailsBloc, TradeDetailsState>(
            buildWhen: (previous, current) => previous != current,
            builder: (ctx, state) {
              return switch (state) {
                TradeDetailsLoading() => DuploShimmerList(
                  hasLeading: false,
                  hasTrailing: true,
                ),
                TradeDetailsError() => Container(
                  color: ctx.duploTheme.background.bgSecondary,
                  child: Center(),
                ),
                TradeDetailsSuccess(
                  info: final position,
                  accountCurrency: final currency,
                ) =>
                  () {
                    final displayData = [
                      KeyValuePair(
                        label: l10n.trader_tradeIDNumber,
                        value: (position.positionId).toString(),
                      ),
                      KeyValuePair(
                        label: l10n.trader_product,
                        value: position.symbol,
                      ),
                      KeyValuePair(
                        label: l10n.trader_baseCurrency,
                        value: position.baseCurrency,
                      ),
                      KeyValuePair(
                        label: l10n.trader_marketClass,
                        value: position.productCategory,
                      ),
                      KeyValuePair(
                        label: l10n.trader_marketType,
                        value: position.assetType,
                      ),
                      KeyValuePair(
                        label: l10n.trader_account_currency_type,
                        value: accountCurrency ?? "",
                      ),
                    ];

                    final displayData2 = [
                      KeyValuePair(
                        label: l10n.trader_opened,
                        value: position.openedAt,
                      ),
                      KeyValuePair(
                        label: l10n.trader_openPrice,
                        value: EquitiFormatter.decimalPatternDigits(
                          value: position.openPrice,
                          digits: position.digits,
                          locale: locale,
                        ),
                      ),
                      KeyValuePair(
                        label: l10n.trader_direction,
                        value:
                            (position.positionType == TradeType.buy
                                ? l10n.trader_buy
                                : l10n.trader_sell),
                      ),
                      KeyValuePair(
                        label: l10n.trader_lotSize,
                        value: EquitiFormatter.formatNumberWithZeroDefault(
                          value: position.lotSize,
                          locale: locale,
                        ),
                      ),
                      KeyValuePair(
                        label: l10n.trader_notionalValue,
                        value:
                            EquitiFormatter.getCurrencySymbol(currency) +
                            EquitiFormatter.formatNumberWithZeroDefault(
                              value: position.notionalValue,
                              locale: locale,
                            ),
                      ),
                      KeyValuePair(
                        label: l10n.trader_pipValue,
                        value:
                            EquitiFormatter.getCurrencySymbol(currency) +
                            EquitiFormatter.formatNumberWithZeroDefault(
                              value: position.pipValue,
                              locale: locale,
                            ),
                      ),
                      KeyValuePair(
                        label: l10n.trader_leverage,
                        value:
                            "1:${EquitiFormatter.formatNumber(value: position.leverage, locale: locale)}",
                      ),
                    ];

                    final leftSideData = [
                      KeyValuePair(
                        label: l10n.trader_grossProfit,
                        value: EquitiFormatter.formatNumberWithZeroDefault(
                          locale: locale,
                          value: position.grossProfit,
                          digits: 2,
                        ),
                      ),
                      KeyValuePair(
                        label: l10n.trader_swaps,
                        value: EquitiFormatter.formatNumberWithZeroDefault(
                          value: position.swap,
                          locale: locale,
                        ),
                      ),
                      KeyValuePair(
                        label: l10n.trader_takeProfit,
                        value: EquitiFormatter.formatNumberWithZeroDefault(
                          value: position.takeProfit,
                          locale: locale,
                        ),
                      ),
                    ];

                    final rightSideData = [
                      KeyValuePair(
                        label: l10n.trader_netProfit,
                        value: EquitiFormatter.formatNumberWithZeroDefault(
                          locale: locale,
                          value: position.netProfit,
                          digits: 2,
                        ),
                      ),
                      KeyValuePair(
                        label: l10n.trader_commision,
                        value: EquitiFormatter.formatNumberWithZeroDefault(
                          value: position.commission,
                          locale: locale,
                        ),
                      ),
                      KeyValuePair(
                        label: l10n.trader_stopLoss,
                        value: EquitiFormatter.formatNumberWithZeroDefault(
                          value: position.stopLoss,
                          locale: locale,
                        ),
                      ),
                    ];

                    return Container(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          TradeTile(
                            digits: position.digits,
                            productName: position.symbol,
                            productIcon: position.productLogoUrl,
                            profit: position.profit ?? 0,
                            priceChange:
                                position.positionType == TradeType.buy
                                    ? position.buyPercentage
                                    : position.sellPercentage,
                            currentPrice: position.currentPrice,

                            tpValue: position.takeProfit,
                            slValue: position.stopLoss,
                            lots: diContainer<GetTradeSizeFromVolumeUseCase>()
                                .call(position.volume),
                            tradeType: position.positionType,
                            currency: accountCurrency,
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 14,
                            ),
                            decoration: BoxDecoration(
                              border: Border(
                                top: BorderSide(
                                  color: theme.border.borderSecondary,
                                  width: 1,
                                ),
                                bottom: BorderSide(
                                  color: theme.border.borderSecondary,
                                  width: 1,
                                ),
                                left:
                                    intl.Bidi.isRtlLanguage(
                                          Localizations.localeOf(
                                            context,
                                          ).languageCode,
                                        )
                                        ? BorderSide.none
                                        : BorderSide(
                                          color:
                                              position.positionType ==
                                                      TradeType.buy
                                                  ? theme
                                                      .foreground
                                                      .fgSuccessPrimary
                                                  : theme
                                                      .foreground
                                                      .fgErrorPrimary,
                                          width: 4,
                                        ),
                                right:
                                    intl.Bidi.isRtlLanguage(
                                          Localizations.localeOf(
                                            context,
                                          ).languageCode,
                                        )
                                        ? BorderSide(
                                          color:
                                              position.positionType ==
                                                      TradeType.buy
                                                  ? theme
                                                      .foreground
                                                      .fgSuccessPrimary
                                                  : theme
                                                      .foreground
                                                      .fgErrorPrimary,
                                          width: 4,
                                        )
                                        : BorderSide.none,
                              ),
                            ),
                            child: DuploStatsWidget(
                              leftColumnData: leftSideData,
                              rightColumnData: rightSideData,
                              labelColor: theme.text.textTertiary,
                              verticalPadding: 4,
                              backgroundColor: theme.background.bgPrimary,
                            ),
                          ),
                          Container(
                            color: theme.background.bgSecondary,
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                            child: Column(
                              children: [
                                SizedBox(height: 20),
                                DuploKeyValueDisplay(
                                  title: l10n.trader_general,
                                  keyValuePairs: displayData,
                                  keyTextStyle: duploTextStyles.textSm,
                                  valueTextStyle: duploTextStyles.textSm,
                                  addBorder: true,
                                ),
                                SizedBox(height: 24),
                                DuploKeyValueDisplay(
                                  title: l10n.trader_execution,
                                  keyValuePairs: displayData2,
                                  keyTextStyle: duploTextStyles.textSm,
                                  addBorder: true,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    );
                  }(),
              };
            },
          ),
        ),
  );
}
