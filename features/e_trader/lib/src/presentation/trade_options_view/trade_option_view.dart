import 'package:duplo/duplo.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/presentation/create_trade/modify_trade_bottom_sheet.dart';
import 'package:e_trader/src/presentation/partial_close/partial_close.dart';
import 'package:e_trader/src/presentation/partial_close/partial_close_toast.dart';
import 'package:e_trader/src/presentation/positions_and_trades/trade_tile.dart';
import 'package:e_trader/src/presentation/trade_details/trade_details_widget.dart';
import 'package:e_trader/src/presentation/trade_options_view/bloc/trade_options_bloc.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:prelude/prelude.dart';

class TradeOptionView extends StatelessWidget {
  const TradeOptionView({super.key, required this.positionId, this.currency});
  final String positionId;
  final String? currency;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final textStyles = context.duploTextStyles;
    final localization = EquitiLocalization.of(context);
    DuploToast? toast;

    return Scaffold(
      backgroundColor: theme.background.bgPrimary,
      body: BlocProvider(
        create:
            (createContext) =>
                diContainer<TradeOptionsBloc>(param1: positionId)
                  ..add(const TradeOptionsEvent.connectToPositionSocket()),
        child: BlocConsumer<TradeOptionsBloc, TradeOptionsState>(
          listenWhen:
              (previous, current) =>
                  current.quickCloseProcessState !=
                  previous.quickCloseProcessState,
          listener: (listenerContext, state) {
            switch (state.quickCloseProcessState) {
              case QuickCloseSuccessState():
                {
                  toast?.hidesToastMessage();
                  toast = DuploToast();
                  showCloseTradeToast(
                    toast: toast!,
                    context: context,
                    lots: state.lotSize,
                    productIconUrl: state.position!.productLogoUrl,
                    productName: state.position!.productName,
                    profit: state.position!.profit ?? 0,
                    tradeType: state.position!.positionType,
                    titleMessage: localization.trader_tradeClosed,
                    currency: currency,
                  );
                  Navigator.pop(context);
                }
              case QuickCloseMarketClosedState():
                {
                  toast?.hidesToastMessage();
                  toast = DuploToast();
                  toast?.showToastMessage(
                    context: context,
                    widget: DuploToastMessage(
                      titleMessage: localization.trader_marketIsClosed,
                      descriptionMessage:
                          localization
                              .trader_closeTrade_marketIsClosedDescription,
                      messageType: ToastMessageType.error,
                      onLeadingAction: () => toast?.hidesToastMessage(),
                    ),
                  );
                }
              case QuickCloseLoadingState():
                {
                  toast?.hidesToastMessage();
                  toast = DuploToast();
                  toast!.showToastMessage(
                    context: context,
                    widget: DuploToastMessage(
                      titleMessage: localization.trader_quickClose,
                      descriptionMessage: localization.trader_quickCloseLoading,
                      messageType: ToastMessageType.success,
                      onLeadingAction: () {
                        toast?.hidesToastMessage();
                      },
                    ),
                  );
                }
              case QuickCloseErrorState(:final counter):
                {
                  toast?.hidesToastMessage();
                  toast = DuploToast();
                  toast!.showToastMessage(
                    context: context,
                    autoCloseDuration: Duration.zero,
                    widget: DuploToastMessage(
                      titleMessage:
                          counter == 1
                              ? localization.trader_tradeNotClosed
                              : localization.trader_contactSupportTeam,
                      descriptionMessage:
                          counter == 1
                              ? localization.trader_placeholderText
                              : localization.trader_contactSupportDescription,
                      messageType: ToastMessageType.error,
                      onLeadingAction: () {
                        toast?.hidesToastMessage();
                      },
                      primaryButtonTitle:
                          counter == 1
                              ? null
                              : localization.trader_raiseSupportTicket,
                      onTap: () {
                        toast
                            ?.hidesToastMessage(); // TODO: Raise support ticket
                      },
                    ),
                  );
                }
              case QuickCloseInitialState():
                {
                  debugPrint('init state');
                }
            }
          },
          buildWhen: (previous, current) => previous != current,
          builder: (blocBuilderContext, state) {
            return switch (state.processState) {
              TradeOptionsLoading() => Center(
                child: CircularProgressIndicator(),
              ),
              TradeOptionsError() => Center(
                child: DuploText(
                  text: localization.trader_somethingWentWrong,
                  style: textStyles.textSm,
                  color: theme.text.textPrimary,
                ),
              ),
              TradeOptionsSuccess() => () {
                final position = state.position!;

                return CustomScrollView(
                  slivers: [
                    SliverToBoxAdapter(
                      child: TradeTile(
                        digits: position.digits,
                        lots: state.lotSize,
                        profit: position.profit!,
                        tradeType: position.positionType,
                        tpValue: position.takeProfit,
                        slValue: position.stopLoss,
                        currentPrice: position.currentPrice,
                        priceChange:
                            position.positionType == TradeType.buy
                                ? position.buyPercentage
                                : position.sellPercentage,
                        productIcon: position.productLogoUrl,
                        productName: position.productName,
                        currency: currency,
                      ),
                    ),
                    SliverToBoxAdapter(
                      child: Divider(
                        color: theme.border.borderSecondary,
                        height: 0,
                      ),
                    ),
                    SliverToBoxAdapter(
                      child: Container(
                        decoration: BoxDecoration(
                          border: Border(
                            left: BorderSide(
                              color:
                                  position.positionType == TradeType.buy
                                      ? theme.foreground.fgSuccessPrimary
                                      : theme.border.borderError,
                              width: 4.0,
                            ),
                          ),
                        ),
                        child: Padding(
                          padding: EdgeInsets.all(16.0),
                          child: DuploStatsWidget(
                            leftColumnData: [
                              KeyValuePair(
                                label: localization.trader_grossProfit,
                                value:
                                    EquitiFormatter.formatNumberWithZeroDefault(
                                      value: position.grossProfit,
                                      locale:
                                          Localizations.localeOf(
                                            context,
                                          ).toString(),
                                    ),
                              ),
                              KeyValuePair(
                                label: localization.trader_swaps,
                                value:
                                    EquitiFormatter.formatNumberWithZeroDefault(
                                      value: position.swap,
                                      locale:
                                          Localizations.localeOf(
                                            context,
                                          ).toString(),
                                    ),
                              ),
                              KeyValuePair(
                                label: localization.trader_takeProfit,
                                value:
                                    EquitiFormatter.formatNumberWithZeroDefault(
                                      value: position.takeProfit,
                                      locale:
                                          Localizations.localeOf(
                                            context,
                                          ).toString(),
                                    ),
                              ),
                            ],
                            rightColumnData: [
                              KeyValuePair(
                                label: localization.trader_netProfit,
                                value:
                                    EquitiFormatter.formatNumberWithZeroDefault(
                                      value:
                                          (position.commission) +
                                          position.swap +
                                          position.grossProfit,
                                      locale:
                                          Localizations.localeOf(
                                            context,
                                          ).toString(),
                                    ),
                              ),
                              KeyValuePair(
                                label: localization.trader_commision,
                                value:
                                    EquitiFormatter.formatNumberWithZeroDefault(
                                      value: position.commission,
                                      locale:
                                          Localizations.localeOf(
                                            context,
                                          ).toString(),
                                    ),
                              ),
                              KeyValuePair(
                                label: localization.trader_stopLoss,
                                value:
                                    EquitiFormatter.formatNumberWithZeroDefault(
                                      value: position.stopLoss,
                                      locale:
                                          Localizations.localeOf(
                                            context,
                                          ).toString(),
                                    ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    SliverFillRemaining(
                      hasScrollBody: false,
                      child: Column(
                        children: [
                          Container(
                            color: theme.background.bgSecondary,
                            padding: EdgeInsets.symmetric(horizontal: 2),
                            child: Column(
                              children: [
                                const SizedBox(height: 12),
                                TextChevronWidget(
                                  title: localization.trader_tradeDetails,
                                  onPressed: () {
                                    showTradeDetailsSheet(
                                      context: context,
                                      positionId: int.parse(positionId),
                                      accountCurrency: currency,
                                    );
                                  },
                                ),
                                Divider(
                                  indent: 12,
                                  endIndent: 12,
                                  color: theme.border.borderSecondary,
                                  height: 0,
                                ),
                                TextChevronWidget(
                                  title: localization.trader_viewChart,
                                  onPressed: () {
                                    // TODO(Danya): open View Chart
                                  },
                                ),
                                Divider(
                                  indent: 12,
                                  endIndent: 12,
                                  color: theme.border.borderSecondary,
                                  height: 0,
                                ),
                                TextChevronWidget(
                                  title: localization.trader_modifiyTrade,
                                  onPressed: () {
                                    modifyTradeBottomSheet(
                                      context,
                                      state.position!,
                                    );
                                  },
                                ),
                                Divider(
                                  indent: 12,
                                  endIndent: 12,
                                  color: theme.border.borderSecondary,
                                  height: 0,
                                ),
                                TextChevronWidget(
                                  title: localization.trader_partialClose,
                                  onPressed: () {
                                    showPartialCloseSheet(
                                      context: context,
                                      positionId: int.parse(
                                        position.positionId,
                                      ),
                                      onSuccess: ({
                                        required double lots,
                                        required String productIconUrl,
                                        required String productName,
                                        required double profit,
                                        required TradeType? tradeType,
                                        String? titleMessage,
                                      }) {
                                        toast?.hidesToastMessage();
                                        toast = DuploToast();
                                        showCloseTradeToast(
                                          toast: toast!,
                                          context: context,
                                          lots: lots,
                                          productIconUrl: productIconUrl,
                                          productName: productName,
                                          profit: profit,
                                          tradeType: tradeType,
                                          titleMessage: titleMessage,
                                          currency: currency,
                                        );
                                      },
                                    );
                                  },
                                ),
                                Divider(
                                  indent: 12,
                                  endIndent: 12,
                                  color: theme.border.borderSecondary,
                                  height: 0,
                                ),
                                TextChevronWidget(
                                  title: localization.trader_quickClose,
                                  onPressed: () {
                                    if (state.quickCloseProcessState !=
                                        QuickCloseProcessState.loading())
                                      blocBuilderContext
                                          .read<TradeOptionsBloc>()
                                          .add(
                                            const TradeOptionsEvent.quickCloseTrade(),
                                          );
                                  },
                                ),
                                Divider(
                                  indent: 12,
                                  endIndent: 12,
                                  color: theme.border.borderSecondary,
                                  height: 0,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                );
              }(),
            };
          },
        ),
      ),
    );
  }
}
