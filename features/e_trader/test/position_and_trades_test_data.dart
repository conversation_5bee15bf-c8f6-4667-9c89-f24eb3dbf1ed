import 'package:e_trader/src/data/socket/position_model.dart';
import 'package:e_trader/src/domain/model/price_direction.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/presentation/positions_and_trades/expandable_position_header.dart';
import 'package:e_trader/src/presentation/positions_and_trades/position_header.dart';
import 'package:e_trader/src/presentation/positions_and_trades/trade_tile.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class PositionAndTradesTestData extends StatelessWidget {
  const PositionAndTradesTestData({super.key});

  @override
  Widget build(BuildContext context) {
    return ExpandablePositionHeader(
      lots: 0.4,
      productIcon:
          'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQiauMVXJTGHbVWoUu6w62vuYIAxYTSGA1sBg&s',
      productName: 'EURUSD',
      isHedging: true,
      profit: 15000,
      margin: 12000,
      tradeType: TradeType.buy,
      tradesData: [
        PositionModel(
          volume: 40,
          profit: 15000,
          percentageChange: 12000,
          currentPrice: 1.23456,
          openPrice: 1.23456,
          takeProfit: 1.23456,
          stopLoss: 1.23456,
          positionType: TradeType.buy,
          productCategoryId: '',
          positionId: '1',
          symbol: '',
          marginRate: 0.0,
          tradingAccountNumber: '',
          profitRate: 0.0,
          contractSize: 1.0,
          margin: 0.0,
          marginAllocation: 0.0,
          productCategory: '',
          commission: 0.0,
          leverage: 1,
          digits: 5,
          productName: 'EURUSD',
          productLogoUrl: '',
          notionalValue: 0.0,
          pipValue: 0.0,
          assetType: '',
          sector: '',
          baseCurrency: '',
          grossProfit: 15000,
          minLot: 0.01,
          maxLot: 100.0,
          marginLevel: 0.0,
          messageType: 1,
          openedAt: DateFormat().format(DateTime.now()),
          updatedAt: DateFormat().format(DateTime.now()),
          priceDirection: PriceDirection.up,
          swap: 0.0,
          multiply: 10000,
        ),
        PositionModel(
          volume: 40,
          profit: -134,
          percentageChange: 12000,
          currentPrice: 1.23456,
          openPrice: 1.23456,
          takeProfit: 1.23456,
          stopLoss: 1.23456,
          positionType: TradeType.sell,
          productCategoryId: '',
          positionId: '2',
          symbol: '',
          marginRate: 0.0,
          tradingAccountNumber: '',
          profitRate: 0.0,
          contractSize: 1.0,
          margin: 0.0,
          marginAllocation: 0.0,
          productCategory: '',
          commission: 0.0,
          leverage: 1,
          digits: 5,
          productName: 'EURUSD',
          productLogoUrl: '',
          notionalValue: 0.0,
          pipValue: 0.0,
          assetType: '',
          sector: '',
          baseCurrency: '',
          grossProfit: -134,
          minLot: 0.01,
          maxLot: 100.0,
          marginLevel: 0.0,
          messageType: 1,
          openedAt: DateFormat().format(DateTime.now()),
          updatedAt: DateFormat().format(DateTime.now()),
          priceDirection: PriceDirection.down,
          swap: 0.0,
          multiply: 10000,
        ),
        PositionModel(
          volume: 40,
          profit: 15000,
          percentageChange: 12000,
          currentPrice: 1.23456,
          openPrice: 1.23456,
          takeProfit: 1.23456,
          stopLoss: 1.23456,
          positionType: TradeType.buy,
          productCategoryId: '',
          positionId: '3',
          symbol: '',
          marginRate: 0.0,
          tradingAccountNumber: '',
          profitRate: 0.0,
          contractSize: 1.0,
          margin: 0.0,
          marginAllocation: 0.0,
          productCategory: '',
          commission: 0.0,
          leverage: 1,
          digits: 5,
          productName: 'EURUSD',
          productLogoUrl: '',
          notionalValue: 0.0,
          pipValue: 0.0,
          assetType: '',
          sector: '',
          baseCurrency: '',
          grossProfit: 15000,
          minLot: 0.01,
          maxLot: 100.0,
          marginLevel: 0.0,
          messageType: 1,
          openedAt: DateFormat().format(DateTime.now()),
          updatedAt: DateFormat().format(DateTime.now()),
          priceDirection: PriceDirection.up,
          swap: 0.0,
          multiply: 10000,
        ),
        PositionModel(
          volume: 40,
          profit: -134,
          percentageChange: 12000,
          currentPrice: 1.23456,
          openPrice: 1.23456,
          takeProfit: 1.23456,
          stopLoss: 1.23456,
          positionType: TradeType.sell,
          productCategoryId: '',
          positionId: '4',
          symbol: '',
          marginRate: 0.0,
          tradingAccountNumber: '',
          profitRate: 0.0,
          contractSize: 1.0,
          margin: 0.0,
          marginAllocation: 0.0,
          productCategory: '',
          commission: 0.0,
          leverage: 1,
          digits: 5,
          productName: 'EURUSD',
          productLogoUrl: '',
          notionalValue: 0.0,
          pipValue: 0.0,
          assetType: '',
          sector: '',
          baseCurrency: '',
          grossProfit: -134,
          minLot: 0.01,
          maxLot: 100.0,
          marginLevel: 0.0,
          messageType: 1,
          openedAt: DateFormat().format(DateTime.now()),
          updatedAt: DateFormat().format(DateTime.now()),
          priceDirection: PriceDirection.down,
          swap: 0.0,
          multiply: 10000,
        ),
      ],
    );
  }
}

class PositionHeaderWidget extends StatelessWidget {
  const PositionHeaderWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return const PositionHeader(
      lots: 0.4,
      productIcon:
          'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQiauMVXJTGHbVWoUu6w62vuYIAxYTSGA1sBg&s',
      productName: 'EURUSD',
      isHedging: false,
      profit: 15000,
      margin: 12000,
      tradeType: TradeType.buy,
    );
  }
}

class TradeTileWidget extends StatelessWidget {
  const TradeTileWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        TradeTile(
          digits: 3,
          lots: 0.4,
          profit: 15000,
          tradeType: TradeType.buy,
          tpValue: 1.23456,
          slValue: 1.23456,
          currentPrice: 1.5432,
          priceChange: 12000,
          productName: "EURUSD",
          productIcon: "",
        ),
      ],
    );
  }
}
