import 'package:freezed_annotation/freezed_annotation.dart';

part 'account_activity_model.freezed.dart';
part 'account_activity_model.g.dart';

@freezed
abstract class AccountActivityModel with _$AccountActivityModel {
  const factory AccountActivityModel({
    List<ActivityItem>? list,
    @Default(0) int totalCount,
  }) = _AccountActivityModel;

  factory AccountActivityModel.fromJson(Map<String, dynamic> json) =>
      _$AccountActivityModelFromJson(json);
}

@freezed
abstract class ActivityItem with _$ActivityItem {
  const factory ActivityItem({
    int? id,
    String? activityType,
    String? activityTypeName,
    DateTime? dateTime,
    ActivityDetail? activityDetail,
    String? operationId,
    String? activityText,
  }) = _ActivityItem;

  factory ActivityItem.fromJson(Map<String, dynamic> json) =>
      _$ActivityItemFromJson(json);
}

@freezed
abstract class ActivityDetail with _$ActivityDetail {
  const factory ActivityDetail({
    String? currency,
    double? amount,
    String? accountNumber,
    String? status,
    String? statusName,
    String? paymentMethod,
    String? destinationAccount,
    String? comment,
    String? accountType,
    String? sourceAccount,
    String? campaign,
    int? oldLeverage,
    int? newLeverage,
    String? sourceClientName,
    String? destinationClientName,
  }) = _ActivityDetail;

  factory ActivityDetail.fromJson(Map<String, dynamic> json) =>
      _$ActivityDetailFromJson(json);
}
