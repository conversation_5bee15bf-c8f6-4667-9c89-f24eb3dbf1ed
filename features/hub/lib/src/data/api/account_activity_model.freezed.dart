// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'account_activity_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$AccountActivityModel {

 List<ActivityItem>? get list; int get totalCount;
/// Create a copy of AccountActivityModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AccountActivityModelCopyWith<AccountActivityModel> get copyWith => _$AccountActivityModelCopyWithImpl<AccountActivityModel>(this as AccountActivityModel, _$identity);

  /// Serializes this AccountActivityModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AccountActivityModel&&const DeepCollectionEquality().equals(other.list, list)&&(identical(other.totalCount, totalCount) || other.totalCount == totalCount));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(list),totalCount);

@override
String toString() {
  return 'AccountActivityModel(list: $list, totalCount: $totalCount)';
}


}

/// @nodoc
abstract mixin class $AccountActivityModelCopyWith<$Res>  {
  factory $AccountActivityModelCopyWith(AccountActivityModel value, $Res Function(AccountActivityModel) _then) = _$AccountActivityModelCopyWithImpl;
@useResult
$Res call({
 List<ActivityItem>? list, int totalCount
});




}
/// @nodoc
class _$AccountActivityModelCopyWithImpl<$Res>
    implements $AccountActivityModelCopyWith<$Res> {
  _$AccountActivityModelCopyWithImpl(this._self, this._then);

  final AccountActivityModel _self;
  final $Res Function(AccountActivityModel) _then;

/// Create a copy of AccountActivityModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? list = freezed,Object? totalCount = null,}) {
  return _then(_self.copyWith(
list: freezed == list ? _self.list : list // ignore: cast_nullable_to_non_nullable
as List<ActivityItem>?,totalCount: null == totalCount ? _self.totalCount : totalCount // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _AccountActivityModel implements AccountActivityModel {
  const _AccountActivityModel({final  List<ActivityItem>? list, this.totalCount = 0}): _list = list;
  factory _AccountActivityModel.fromJson(Map<String, dynamic> json) => _$AccountActivityModelFromJson(json);

 final  List<ActivityItem>? _list;
@override List<ActivityItem>? get list {
  final value = _list;
  if (value == null) return null;
  if (_list is EqualUnmodifiableListView) return _list;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

@override@JsonKey() final  int totalCount;

/// Create a copy of AccountActivityModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AccountActivityModelCopyWith<_AccountActivityModel> get copyWith => __$AccountActivityModelCopyWithImpl<_AccountActivityModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$AccountActivityModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AccountActivityModel&&const DeepCollectionEquality().equals(other._list, _list)&&(identical(other.totalCount, totalCount) || other.totalCount == totalCount));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_list),totalCount);

@override
String toString() {
  return 'AccountActivityModel(list: $list, totalCount: $totalCount)';
}


}

/// @nodoc
abstract mixin class _$AccountActivityModelCopyWith<$Res> implements $AccountActivityModelCopyWith<$Res> {
  factory _$AccountActivityModelCopyWith(_AccountActivityModel value, $Res Function(_AccountActivityModel) _then) = __$AccountActivityModelCopyWithImpl;
@override @useResult
$Res call({
 List<ActivityItem>? list, int totalCount
});




}
/// @nodoc
class __$AccountActivityModelCopyWithImpl<$Res>
    implements _$AccountActivityModelCopyWith<$Res> {
  __$AccountActivityModelCopyWithImpl(this._self, this._then);

  final _AccountActivityModel _self;
  final $Res Function(_AccountActivityModel) _then;

/// Create a copy of AccountActivityModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? list = freezed,Object? totalCount = null,}) {
  return _then(_AccountActivityModel(
list: freezed == list ? _self._list : list // ignore: cast_nullable_to_non_nullable
as List<ActivityItem>?,totalCount: null == totalCount ? _self.totalCount : totalCount // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}


/// @nodoc
mixin _$ActivityItem {

 int? get id; String? get activityType; String? get activityTypeName; DateTime? get dateTime; ActivityDetail? get activityDetail; String? get operationId; String? get activityText;
/// Create a copy of ActivityItem
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ActivityItemCopyWith<ActivityItem> get copyWith => _$ActivityItemCopyWithImpl<ActivityItem>(this as ActivityItem, _$identity);

  /// Serializes this ActivityItem to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ActivityItem&&(identical(other.id, id) || other.id == id)&&(identical(other.activityType, activityType) || other.activityType == activityType)&&(identical(other.activityTypeName, activityTypeName) || other.activityTypeName == activityTypeName)&&(identical(other.dateTime, dateTime) || other.dateTime == dateTime)&&(identical(other.activityDetail, activityDetail) || other.activityDetail == activityDetail)&&(identical(other.operationId, operationId) || other.operationId == operationId)&&(identical(other.activityText, activityText) || other.activityText == activityText));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,activityType,activityTypeName,dateTime,activityDetail,operationId,activityText);

@override
String toString() {
  return 'ActivityItem(id: $id, activityType: $activityType, activityTypeName: $activityTypeName, dateTime: $dateTime, activityDetail: $activityDetail, operationId: $operationId, activityText: $activityText)';
}


}

/// @nodoc
abstract mixin class $ActivityItemCopyWith<$Res>  {
  factory $ActivityItemCopyWith(ActivityItem value, $Res Function(ActivityItem) _then) = _$ActivityItemCopyWithImpl;
@useResult
$Res call({
 int? id, String? activityType, String? activityTypeName, DateTime? dateTime, ActivityDetail? activityDetail, String? operationId, String? activityText
});


$ActivityDetailCopyWith<$Res>? get activityDetail;

}
/// @nodoc
class _$ActivityItemCopyWithImpl<$Res>
    implements $ActivityItemCopyWith<$Res> {
  _$ActivityItemCopyWithImpl(this._self, this._then);

  final ActivityItem _self;
  final $Res Function(ActivityItem) _then;

/// Create a copy of ActivityItem
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? activityType = freezed,Object? activityTypeName = freezed,Object? dateTime = freezed,Object? activityDetail = freezed,Object? operationId = freezed,Object? activityText = freezed,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,activityType: freezed == activityType ? _self.activityType : activityType // ignore: cast_nullable_to_non_nullable
as String?,activityTypeName: freezed == activityTypeName ? _self.activityTypeName : activityTypeName // ignore: cast_nullable_to_non_nullable
as String?,dateTime: freezed == dateTime ? _self.dateTime : dateTime // ignore: cast_nullable_to_non_nullable
as DateTime?,activityDetail: freezed == activityDetail ? _self.activityDetail : activityDetail // ignore: cast_nullable_to_non_nullable
as ActivityDetail?,operationId: freezed == operationId ? _self.operationId : operationId // ignore: cast_nullable_to_non_nullable
as String?,activityText: freezed == activityText ? _self.activityText : activityText // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}
/// Create a copy of ActivityItem
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ActivityDetailCopyWith<$Res>? get activityDetail {
    if (_self.activityDetail == null) {
    return null;
  }

  return $ActivityDetailCopyWith<$Res>(_self.activityDetail!, (value) {
    return _then(_self.copyWith(activityDetail: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _ActivityItem implements ActivityItem {
  const _ActivityItem({this.id, this.activityType, this.activityTypeName, this.dateTime, this.activityDetail, this.operationId, this.activityText});
  factory _ActivityItem.fromJson(Map<String, dynamic> json) => _$ActivityItemFromJson(json);

@override final  int? id;
@override final  String? activityType;
@override final  String? activityTypeName;
@override final  DateTime? dateTime;
@override final  ActivityDetail? activityDetail;
@override final  String? operationId;
@override final  String? activityText;

/// Create a copy of ActivityItem
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ActivityItemCopyWith<_ActivityItem> get copyWith => __$ActivityItemCopyWithImpl<_ActivityItem>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ActivityItemToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ActivityItem&&(identical(other.id, id) || other.id == id)&&(identical(other.activityType, activityType) || other.activityType == activityType)&&(identical(other.activityTypeName, activityTypeName) || other.activityTypeName == activityTypeName)&&(identical(other.dateTime, dateTime) || other.dateTime == dateTime)&&(identical(other.activityDetail, activityDetail) || other.activityDetail == activityDetail)&&(identical(other.operationId, operationId) || other.operationId == operationId)&&(identical(other.activityText, activityText) || other.activityText == activityText));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,activityType,activityTypeName,dateTime,activityDetail,operationId,activityText);

@override
String toString() {
  return 'ActivityItem(id: $id, activityType: $activityType, activityTypeName: $activityTypeName, dateTime: $dateTime, activityDetail: $activityDetail, operationId: $operationId, activityText: $activityText)';
}


}

/// @nodoc
abstract mixin class _$ActivityItemCopyWith<$Res> implements $ActivityItemCopyWith<$Res> {
  factory _$ActivityItemCopyWith(_ActivityItem value, $Res Function(_ActivityItem) _then) = __$ActivityItemCopyWithImpl;
@override @useResult
$Res call({
 int? id, String? activityType, String? activityTypeName, DateTime? dateTime, ActivityDetail? activityDetail, String? operationId, String? activityText
});


@override $ActivityDetailCopyWith<$Res>? get activityDetail;

}
/// @nodoc
class __$ActivityItemCopyWithImpl<$Res>
    implements _$ActivityItemCopyWith<$Res> {
  __$ActivityItemCopyWithImpl(this._self, this._then);

  final _ActivityItem _self;
  final $Res Function(_ActivityItem) _then;

/// Create a copy of ActivityItem
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? activityType = freezed,Object? activityTypeName = freezed,Object? dateTime = freezed,Object? activityDetail = freezed,Object? operationId = freezed,Object? activityText = freezed,}) {
  return _then(_ActivityItem(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,activityType: freezed == activityType ? _self.activityType : activityType // ignore: cast_nullable_to_non_nullable
as String?,activityTypeName: freezed == activityTypeName ? _self.activityTypeName : activityTypeName // ignore: cast_nullable_to_non_nullable
as String?,dateTime: freezed == dateTime ? _self.dateTime : dateTime // ignore: cast_nullable_to_non_nullable
as DateTime?,activityDetail: freezed == activityDetail ? _self.activityDetail : activityDetail // ignore: cast_nullable_to_non_nullable
as ActivityDetail?,operationId: freezed == operationId ? _self.operationId : operationId // ignore: cast_nullable_to_non_nullable
as String?,activityText: freezed == activityText ? _self.activityText : activityText // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

/// Create a copy of ActivityItem
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ActivityDetailCopyWith<$Res>? get activityDetail {
    if (_self.activityDetail == null) {
    return null;
  }

  return $ActivityDetailCopyWith<$Res>(_self.activityDetail!, (value) {
    return _then(_self.copyWith(activityDetail: value));
  });
}
}


/// @nodoc
mixin _$ActivityDetail {

 String? get currency; double? get amount; String? get accountNumber; String? get status; String? get statusName; String? get paymentMethod; String? get destinationAccount; String? get comment; String? get accountType; String? get sourceAccount; String? get campaign; int? get oldLeverage; int? get newLeverage; String? get sourceClientName; String? get destinationClientName;
/// Create a copy of ActivityDetail
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ActivityDetailCopyWith<ActivityDetail> get copyWith => _$ActivityDetailCopyWithImpl<ActivityDetail>(this as ActivityDetail, _$identity);

  /// Serializes this ActivityDetail to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ActivityDetail&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.accountNumber, accountNumber) || other.accountNumber == accountNumber)&&(identical(other.status, status) || other.status == status)&&(identical(other.statusName, statusName) || other.statusName == statusName)&&(identical(other.paymentMethod, paymentMethod) || other.paymentMethod == paymentMethod)&&(identical(other.destinationAccount, destinationAccount) || other.destinationAccount == destinationAccount)&&(identical(other.comment, comment) || other.comment == comment)&&(identical(other.accountType, accountType) || other.accountType == accountType)&&(identical(other.sourceAccount, sourceAccount) || other.sourceAccount == sourceAccount)&&(identical(other.campaign, campaign) || other.campaign == campaign)&&(identical(other.oldLeverage, oldLeverage) || other.oldLeverage == oldLeverage)&&(identical(other.newLeverage, newLeverage) || other.newLeverage == newLeverage)&&(identical(other.sourceClientName, sourceClientName) || other.sourceClientName == sourceClientName)&&(identical(other.destinationClientName, destinationClientName) || other.destinationClientName == destinationClientName));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,currency,amount,accountNumber,status,statusName,paymentMethod,destinationAccount,comment,accountType,sourceAccount,campaign,oldLeverage,newLeverage,sourceClientName,destinationClientName);

@override
String toString() {
  return 'ActivityDetail(currency: $currency, amount: $amount, accountNumber: $accountNumber, status: $status, statusName: $statusName, paymentMethod: $paymentMethod, destinationAccount: $destinationAccount, comment: $comment, accountType: $accountType, sourceAccount: $sourceAccount, campaign: $campaign, oldLeverage: $oldLeverage, newLeverage: $newLeverage, sourceClientName: $sourceClientName, destinationClientName: $destinationClientName)';
}


}

/// @nodoc
abstract mixin class $ActivityDetailCopyWith<$Res>  {
  factory $ActivityDetailCopyWith(ActivityDetail value, $Res Function(ActivityDetail) _then) = _$ActivityDetailCopyWithImpl;
@useResult
$Res call({
 String? currency, double? amount, String? accountNumber, String? status, String? statusName, String? paymentMethod, String? destinationAccount, String? comment, String? accountType, String? sourceAccount, String? campaign, int? oldLeverage, int? newLeverage, String? sourceClientName, String? destinationClientName
});




}
/// @nodoc
class _$ActivityDetailCopyWithImpl<$Res>
    implements $ActivityDetailCopyWith<$Res> {
  _$ActivityDetailCopyWithImpl(this._self, this._then);

  final ActivityDetail _self;
  final $Res Function(ActivityDetail) _then;

/// Create a copy of ActivityDetail
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? currency = freezed,Object? amount = freezed,Object? accountNumber = freezed,Object? status = freezed,Object? statusName = freezed,Object? paymentMethod = freezed,Object? destinationAccount = freezed,Object? comment = freezed,Object? accountType = freezed,Object? sourceAccount = freezed,Object? campaign = freezed,Object? oldLeverage = freezed,Object? newLeverage = freezed,Object? sourceClientName = freezed,Object? destinationClientName = freezed,}) {
  return _then(_self.copyWith(
currency: freezed == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String?,amount: freezed == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double?,accountNumber: freezed == accountNumber ? _self.accountNumber : accountNumber // ignore: cast_nullable_to_non_nullable
as String?,status: freezed == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String?,statusName: freezed == statusName ? _self.statusName : statusName // ignore: cast_nullable_to_non_nullable
as String?,paymentMethod: freezed == paymentMethod ? _self.paymentMethod : paymentMethod // ignore: cast_nullable_to_non_nullable
as String?,destinationAccount: freezed == destinationAccount ? _self.destinationAccount : destinationAccount // ignore: cast_nullable_to_non_nullable
as String?,comment: freezed == comment ? _self.comment : comment // ignore: cast_nullable_to_non_nullable
as String?,accountType: freezed == accountType ? _self.accountType : accountType // ignore: cast_nullable_to_non_nullable
as String?,sourceAccount: freezed == sourceAccount ? _self.sourceAccount : sourceAccount // ignore: cast_nullable_to_non_nullable
as String?,campaign: freezed == campaign ? _self.campaign : campaign // ignore: cast_nullable_to_non_nullable
as String?,oldLeverage: freezed == oldLeverage ? _self.oldLeverage : oldLeverage // ignore: cast_nullable_to_non_nullable
as int?,newLeverage: freezed == newLeverage ? _self.newLeverage : newLeverage // ignore: cast_nullable_to_non_nullable
as int?,sourceClientName: freezed == sourceClientName ? _self.sourceClientName : sourceClientName // ignore: cast_nullable_to_non_nullable
as String?,destinationClientName: freezed == destinationClientName ? _self.destinationClientName : destinationClientName // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _ActivityDetail implements ActivityDetail {
  const _ActivityDetail({this.currency, this.amount, this.accountNumber, this.status, this.statusName, this.paymentMethod, this.destinationAccount, this.comment, this.accountType, this.sourceAccount, this.campaign, this.oldLeverage, this.newLeverage, this.sourceClientName, this.destinationClientName});
  factory _ActivityDetail.fromJson(Map<String, dynamic> json) => _$ActivityDetailFromJson(json);

@override final  String? currency;
@override final  double? amount;
@override final  String? accountNumber;
@override final  String? status;
@override final  String? statusName;
@override final  String? paymentMethod;
@override final  String? destinationAccount;
@override final  String? comment;
@override final  String? accountType;
@override final  String? sourceAccount;
@override final  String? campaign;
@override final  int? oldLeverage;
@override final  int? newLeverage;
@override final  String? sourceClientName;
@override final  String? destinationClientName;

/// Create a copy of ActivityDetail
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ActivityDetailCopyWith<_ActivityDetail> get copyWith => __$ActivityDetailCopyWithImpl<_ActivityDetail>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ActivityDetailToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ActivityDetail&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.accountNumber, accountNumber) || other.accountNumber == accountNumber)&&(identical(other.status, status) || other.status == status)&&(identical(other.statusName, statusName) || other.statusName == statusName)&&(identical(other.paymentMethod, paymentMethod) || other.paymentMethod == paymentMethod)&&(identical(other.destinationAccount, destinationAccount) || other.destinationAccount == destinationAccount)&&(identical(other.comment, comment) || other.comment == comment)&&(identical(other.accountType, accountType) || other.accountType == accountType)&&(identical(other.sourceAccount, sourceAccount) || other.sourceAccount == sourceAccount)&&(identical(other.campaign, campaign) || other.campaign == campaign)&&(identical(other.oldLeverage, oldLeverage) || other.oldLeverage == oldLeverage)&&(identical(other.newLeverage, newLeverage) || other.newLeverage == newLeverage)&&(identical(other.sourceClientName, sourceClientName) || other.sourceClientName == sourceClientName)&&(identical(other.destinationClientName, destinationClientName) || other.destinationClientName == destinationClientName));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,currency,amount,accountNumber,status,statusName,paymentMethod,destinationAccount,comment,accountType,sourceAccount,campaign,oldLeverage,newLeverage,sourceClientName,destinationClientName);

@override
String toString() {
  return 'ActivityDetail(currency: $currency, amount: $amount, accountNumber: $accountNumber, status: $status, statusName: $statusName, paymentMethod: $paymentMethod, destinationAccount: $destinationAccount, comment: $comment, accountType: $accountType, sourceAccount: $sourceAccount, campaign: $campaign, oldLeverage: $oldLeverage, newLeverage: $newLeverage, sourceClientName: $sourceClientName, destinationClientName: $destinationClientName)';
}


}

/// @nodoc
abstract mixin class _$ActivityDetailCopyWith<$Res> implements $ActivityDetailCopyWith<$Res> {
  factory _$ActivityDetailCopyWith(_ActivityDetail value, $Res Function(_ActivityDetail) _then) = __$ActivityDetailCopyWithImpl;
@override @useResult
$Res call({
 String? currency, double? amount, String? accountNumber, String? status, String? statusName, String? paymentMethod, String? destinationAccount, String? comment, String? accountType, String? sourceAccount, String? campaign, int? oldLeverage, int? newLeverage, String? sourceClientName, String? destinationClientName
});




}
/// @nodoc
class __$ActivityDetailCopyWithImpl<$Res>
    implements _$ActivityDetailCopyWith<$Res> {
  __$ActivityDetailCopyWithImpl(this._self, this._then);

  final _ActivityDetail _self;
  final $Res Function(_ActivityDetail) _then;

/// Create a copy of ActivityDetail
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? currency = freezed,Object? amount = freezed,Object? accountNumber = freezed,Object? status = freezed,Object? statusName = freezed,Object? paymentMethod = freezed,Object? destinationAccount = freezed,Object? comment = freezed,Object? accountType = freezed,Object? sourceAccount = freezed,Object? campaign = freezed,Object? oldLeverage = freezed,Object? newLeverage = freezed,Object? sourceClientName = freezed,Object? destinationClientName = freezed,}) {
  return _then(_ActivityDetail(
currency: freezed == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String?,amount: freezed == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double?,accountNumber: freezed == accountNumber ? _self.accountNumber : accountNumber // ignore: cast_nullable_to_non_nullable
as String?,status: freezed == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String?,statusName: freezed == statusName ? _self.statusName : statusName // ignore: cast_nullable_to_non_nullable
as String?,paymentMethod: freezed == paymentMethod ? _self.paymentMethod : paymentMethod // ignore: cast_nullable_to_non_nullable
as String?,destinationAccount: freezed == destinationAccount ? _self.destinationAccount : destinationAccount // ignore: cast_nullable_to_non_nullable
as String?,comment: freezed == comment ? _self.comment : comment // ignore: cast_nullable_to_non_nullable
as String?,accountType: freezed == accountType ? _self.accountType : accountType // ignore: cast_nullable_to_non_nullable
as String?,sourceAccount: freezed == sourceAccount ? _self.sourceAccount : sourceAccount // ignore: cast_nullable_to_non_nullable
as String?,campaign: freezed == campaign ? _self.campaign : campaign // ignore: cast_nullable_to_non_nullable
as String?,oldLeverage: freezed == oldLeverage ? _self.oldLeverage : oldLeverage // ignore: cast_nullable_to_non_nullable
as int?,newLeverage: freezed == newLeverage ? _self.newLeverage : newLeverage // ignore: cast_nullable_to_non_nullable
as int?,sourceClientName: freezed == sourceClientName ? _self.sourceClientName : sourceClientName // ignore: cast_nullable_to_non_nullable
as String?,destinationClientName: freezed == destinationClientName ? _self.destinationClientName : destinationClientName // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
