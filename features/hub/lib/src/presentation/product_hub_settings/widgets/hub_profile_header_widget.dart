import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:prelude/prelude.dart';

class HubProfileHeaderWidget extends StatelessWidget {
  const HubProfileHeaderWidget({
    super.key,
    this.avatarUrl,
    required this.firstName,
    required this.lastName,
    required this.email,
    required this.onTap,
  });

  final String? avatarUrl;
  final String firstName;
  final String lastName;
  final String email;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final textStyles = context.duploTextStyles;
    final localization = EquitiLocalization.of(context);

    return DuploTap(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: theme.background.bgSecondary,
          borderRadius: BorderRadius.circular(6),
        ),
        child: Row(
          children: [
            DuploCircularAvatarWidget(
              imageUrl: avatarUrl,
              firstName: firstName,
              lastName: lastName,
              imageWidth: 64,
              imageHeight: 64,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  DuploText(
                    text: "${firstName.capitalize()} ${lastName.capitalize()}",
                    style: textStyles.textSm,
                    fontWeight: DuploFontWeight.semiBold,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    color: theme.text.textSecondary,
                  ),
                  DuploText(
                    text: email,
                    style: textStyles.textXs,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    color: theme.text.textTertiary,
                  ),
                ],
              ),
            ),
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                DuploText(
                  text: localization.hub_profile_edit,
                  style: textStyles.textMd,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  color: theme.button.buttonTertiaryFg,
                ),
                Assets.images.chevronRightDirectional(context).svg(),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
