import 'package:freezed_annotation/freezed_annotation.dart';

part 'account_model.freezed.dart';
part 'account_model.g.dart';

@freezed
sealed class AccountModel with _$AccountModel {
  const factory AccountModel({
    required bool success,
    required AccountData data,
  }) = _AccountModel;

  factory AccountModel.fromJson(Map<String, dynamic> json) =>
      _$AccountModelFromJson(json);
}

@freezed
sealed class AccountData with _$AccountData {
  const factory AccountData({required List<Account> accounts}) = _AccountData;

  factory AccountData.fromJson(Map<String, dynamic> json) =>
      _$AccountDataFromJson(json);
}

@freezed
sealed class Account with _$Account {
  const factory Account({
    required String accountId,
    required String accountStatus,
    required String accountType,
    required String accountCurrency,
    required String platformAccountNumber,
    required String clientId,
    required String accountGroup,
    required String brokerId,
    required String platformAccountType,
    required String name,
    required String nickName,
    required String primaryEmail,
    required int leverage,
    required String serverCode,
    required String platformType,
    required String leadSource,
    required num balance,
    required num margin,
    required num equity,
    required num profit,
    required num grossProfit,
    required DateTime dateCreated,
    required bool isDemo,
    required String classification,
    required int accountIdLong,
    required num credit,
    required String accountCurrencyUsdPair,
  }) = _Account;

  factory Account.fromJson(Map<String, dynamic> json) =>
      _$AccountFromJson(json);
}
