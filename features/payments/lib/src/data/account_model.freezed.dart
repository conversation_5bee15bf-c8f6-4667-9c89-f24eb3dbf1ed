// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'account_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$AccountModel {

 bool get success; AccountData get data;
/// Create a copy of AccountModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AccountModelCopyWith<AccountModel> get copyWith => _$AccountModelCopyWithImpl<AccountModel>(this as AccountModel, _$identity);

  /// Serializes this AccountModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AccountModel&&(identical(other.success, success) || other.success == success)&&(identical(other.data, data) || other.data == data));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,success,data);

@override
String toString() {
  return 'AccountModel(success: $success, data: $data)';
}


}

/// @nodoc
abstract mixin class $AccountModelCopyWith<$Res>  {
  factory $AccountModelCopyWith(AccountModel value, $Res Function(AccountModel) _then) = _$AccountModelCopyWithImpl;
@useResult
$Res call({
 bool success, AccountData data
});


$AccountDataCopyWith<$Res> get data;

}
/// @nodoc
class _$AccountModelCopyWithImpl<$Res>
    implements $AccountModelCopyWith<$Res> {
  _$AccountModelCopyWithImpl(this._self, this._then);

  final AccountModel _self;
  final $Res Function(AccountModel) _then;

/// Create a copy of AccountModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? success = null,Object? data = null,}) {
  return _then(_self.copyWith(
success: null == success ? _self.success : success // ignore: cast_nullable_to_non_nullable
as bool,data: null == data ? _self.data : data // ignore: cast_nullable_to_non_nullable
as AccountData,
  ));
}
/// Create a copy of AccountModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AccountDataCopyWith<$Res> get data {
  
  return $AccountDataCopyWith<$Res>(_self.data, (value) {
    return _then(_self.copyWith(data: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _AccountModel implements AccountModel {
  const _AccountModel({required this.success, required this.data});
  factory _AccountModel.fromJson(Map<String, dynamic> json) => _$AccountModelFromJson(json);

@override final  bool success;
@override final  AccountData data;

/// Create a copy of AccountModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AccountModelCopyWith<_AccountModel> get copyWith => __$AccountModelCopyWithImpl<_AccountModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$AccountModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AccountModel&&(identical(other.success, success) || other.success == success)&&(identical(other.data, data) || other.data == data));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,success,data);

@override
String toString() {
  return 'AccountModel(success: $success, data: $data)';
}


}

/// @nodoc
abstract mixin class _$AccountModelCopyWith<$Res> implements $AccountModelCopyWith<$Res> {
  factory _$AccountModelCopyWith(_AccountModel value, $Res Function(_AccountModel) _then) = __$AccountModelCopyWithImpl;
@override @useResult
$Res call({
 bool success, AccountData data
});


@override $AccountDataCopyWith<$Res> get data;

}
/// @nodoc
class __$AccountModelCopyWithImpl<$Res>
    implements _$AccountModelCopyWith<$Res> {
  __$AccountModelCopyWithImpl(this._self, this._then);

  final _AccountModel _self;
  final $Res Function(_AccountModel) _then;

/// Create a copy of AccountModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? success = null,Object? data = null,}) {
  return _then(_AccountModel(
success: null == success ? _self.success : success // ignore: cast_nullable_to_non_nullable
as bool,data: null == data ? _self.data : data // ignore: cast_nullable_to_non_nullable
as AccountData,
  ));
}

/// Create a copy of AccountModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AccountDataCopyWith<$Res> get data {
  
  return $AccountDataCopyWith<$Res>(_self.data, (value) {
    return _then(_self.copyWith(data: value));
  });
}
}


/// @nodoc
mixin _$AccountData {

 List<Account> get accounts;
/// Create a copy of AccountData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AccountDataCopyWith<AccountData> get copyWith => _$AccountDataCopyWithImpl<AccountData>(this as AccountData, _$identity);

  /// Serializes this AccountData to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AccountData&&const DeepCollectionEquality().equals(other.accounts, accounts));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(accounts));

@override
String toString() {
  return 'AccountData(accounts: $accounts)';
}


}

/// @nodoc
abstract mixin class $AccountDataCopyWith<$Res>  {
  factory $AccountDataCopyWith(AccountData value, $Res Function(AccountData) _then) = _$AccountDataCopyWithImpl;
@useResult
$Res call({
 List<Account> accounts
});




}
/// @nodoc
class _$AccountDataCopyWithImpl<$Res>
    implements $AccountDataCopyWith<$Res> {
  _$AccountDataCopyWithImpl(this._self, this._then);

  final AccountData _self;
  final $Res Function(AccountData) _then;

/// Create a copy of AccountData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? accounts = null,}) {
  return _then(_self.copyWith(
accounts: null == accounts ? _self.accounts : accounts // ignore: cast_nullable_to_non_nullable
as List<Account>,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _AccountData implements AccountData {
  const _AccountData({required final  List<Account> accounts}): _accounts = accounts;
  factory _AccountData.fromJson(Map<String, dynamic> json) => _$AccountDataFromJson(json);

 final  List<Account> _accounts;
@override List<Account> get accounts {
  if (_accounts is EqualUnmodifiableListView) return _accounts;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_accounts);
}


/// Create a copy of AccountData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AccountDataCopyWith<_AccountData> get copyWith => __$AccountDataCopyWithImpl<_AccountData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$AccountDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AccountData&&const DeepCollectionEquality().equals(other._accounts, _accounts));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_accounts));

@override
String toString() {
  return 'AccountData(accounts: $accounts)';
}


}

/// @nodoc
abstract mixin class _$AccountDataCopyWith<$Res> implements $AccountDataCopyWith<$Res> {
  factory _$AccountDataCopyWith(_AccountData value, $Res Function(_AccountData) _then) = __$AccountDataCopyWithImpl;
@override @useResult
$Res call({
 List<Account> accounts
});




}
/// @nodoc
class __$AccountDataCopyWithImpl<$Res>
    implements _$AccountDataCopyWith<$Res> {
  __$AccountDataCopyWithImpl(this._self, this._then);

  final _AccountData _self;
  final $Res Function(_AccountData) _then;

/// Create a copy of AccountData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? accounts = null,}) {
  return _then(_AccountData(
accounts: null == accounts ? _self._accounts : accounts // ignore: cast_nullable_to_non_nullable
as List<Account>,
  ));
}


}


/// @nodoc
mixin _$Account {

 String get accountId; String get accountStatus; String get accountType; String get accountCurrency; String get platformAccountNumber; String get clientId; String get accountGroup; String get brokerId; String get platformAccountType; String get name; String get nickName; String get primaryEmail; int get leverage; String get serverCode; String get platformType; String get leadSource; num get balance; num get margin; num get equity; num get profit; num get grossProfit; DateTime get dateCreated; bool get isDemo; String get classification; int get accountIdLong; num get credit; String get accountCurrencyUsdPair;
/// Create a copy of Account
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AccountCopyWith<Account> get copyWith => _$AccountCopyWithImpl<Account>(this as Account, _$identity);

  /// Serializes this Account to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Account&&(identical(other.accountId, accountId) || other.accountId == accountId)&&(identical(other.accountStatus, accountStatus) || other.accountStatus == accountStatus)&&(identical(other.accountType, accountType) || other.accountType == accountType)&&(identical(other.accountCurrency, accountCurrency) || other.accountCurrency == accountCurrency)&&(identical(other.platformAccountNumber, platformAccountNumber) || other.platformAccountNumber == platformAccountNumber)&&(identical(other.clientId, clientId) || other.clientId == clientId)&&(identical(other.accountGroup, accountGroup) || other.accountGroup == accountGroup)&&(identical(other.brokerId, brokerId) || other.brokerId == brokerId)&&(identical(other.platformAccountType, platformAccountType) || other.platformAccountType == platformAccountType)&&(identical(other.name, name) || other.name == name)&&(identical(other.nickName, nickName) || other.nickName == nickName)&&(identical(other.primaryEmail, primaryEmail) || other.primaryEmail == primaryEmail)&&(identical(other.leverage, leverage) || other.leverage == leverage)&&(identical(other.serverCode, serverCode) || other.serverCode == serverCode)&&(identical(other.platformType, platformType) || other.platformType == platformType)&&(identical(other.leadSource, leadSource) || other.leadSource == leadSource)&&(identical(other.balance, balance) || other.balance == balance)&&(identical(other.margin, margin) || other.margin == margin)&&(identical(other.equity, equity) || other.equity == equity)&&(identical(other.profit, profit) || other.profit == profit)&&(identical(other.grossProfit, grossProfit) || other.grossProfit == grossProfit)&&(identical(other.dateCreated, dateCreated) || other.dateCreated == dateCreated)&&(identical(other.isDemo, isDemo) || other.isDemo == isDemo)&&(identical(other.classification, classification) || other.classification == classification)&&(identical(other.accountIdLong, accountIdLong) || other.accountIdLong == accountIdLong)&&(identical(other.credit, credit) || other.credit == credit)&&(identical(other.accountCurrencyUsdPair, accountCurrencyUsdPair) || other.accountCurrencyUsdPair == accountCurrencyUsdPair));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,accountId,accountStatus,accountType,accountCurrency,platformAccountNumber,clientId,accountGroup,brokerId,platformAccountType,name,nickName,primaryEmail,leverage,serverCode,platformType,leadSource,balance,margin,equity,profit,grossProfit,dateCreated,isDemo,classification,accountIdLong,credit,accountCurrencyUsdPair]);

@override
String toString() {
  return 'Account(accountId: $accountId, accountStatus: $accountStatus, accountType: $accountType, accountCurrency: $accountCurrency, platformAccountNumber: $platformAccountNumber, clientId: $clientId, accountGroup: $accountGroup, brokerId: $brokerId, platformAccountType: $platformAccountType, name: $name, nickName: $nickName, primaryEmail: $primaryEmail, leverage: $leverage, serverCode: $serverCode, platformType: $platformType, leadSource: $leadSource, balance: $balance, margin: $margin, equity: $equity, profit: $profit, grossProfit: $grossProfit, dateCreated: $dateCreated, isDemo: $isDemo, classification: $classification, accountIdLong: $accountIdLong, credit: $credit, accountCurrencyUsdPair: $accountCurrencyUsdPair)';
}


}

/// @nodoc
abstract mixin class $AccountCopyWith<$Res>  {
  factory $AccountCopyWith(Account value, $Res Function(Account) _then) = _$AccountCopyWithImpl;
@useResult
$Res call({
 String accountId, String accountStatus, String accountType, String accountCurrency, String platformAccountNumber, String clientId, String accountGroup, String brokerId, String platformAccountType, String name, String nickName, String primaryEmail, int leverage, String serverCode, String platformType, String leadSource, num balance, num margin, num equity, num profit, num grossProfit, DateTime dateCreated, bool isDemo, String classification, int accountIdLong, num credit, String accountCurrencyUsdPair
});




}
/// @nodoc
class _$AccountCopyWithImpl<$Res>
    implements $AccountCopyWith<$Res> {
  _$AccountCopyWithImpl(this._self, this._then);

  final Account _self;
  final $Res Function(Account) _then;

/// Create a copy of Account
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? accountId = null,Object? accountStatus = null,Object? accountType = null,Object? accountCurrency = null,Object? platformAccountNumber = null,Object? clientId = null,Object? accountGroup = null,Object? brokerId = null,Object? platformAccountType = null,Object? name = null,Object? nickName = null,Object? primaryEmail = null,Object? leverage = null,Object? serverCode = null,Object? platformType = null,Object? leadSource = null,Object? balance = null,Object? margin = null,Object? equity = null,Object? profit = null,Object? grossProfit = null,Object? dateCreated = null,Object? isDemo = null,Object? classification = null,Object? accountIdLong = null,Object? credit = null,Object? accountCurrencyUsdPair = null,}) {
  return _then(_self.copyWith(
accountId: null == accountId ? _self.accountId : accountId // ignore: cast_nullable_to_non_nullable
as String,accountStatus: null == accountStatus ? _self.accountStatus : accountStatus // ignore: cast_nullable_to_non_nullable
as String,accountType: null == accountType ? _self.accountType : accountType // ignore: cast_nullable_to_non_nullable
as String,accountCurrency: null == accountCurrency ? _self.accountCurrency : accountCurrency // ignore: cast_nullable_to_non_nullable
as String,platformAccountNumber: null == platformAccountNumber ? _self.platformAccountNumber : platformAccountNumber // ignore: cast_nullable_to_non_nullable
as String,clientId: null == clientId ? _self.clientId : clientId // ignore: cast_nullable_to_non_nullable
as String,accountGroup: null == accountGroup ? _self.accountGroup : accountGroup // ignore: cast_nullable_to_non_nullable
as String,brokerId: null == brokerId ? _self.brokerId : brokerId // ignore: cast_nullable_to_non_nullable
as String,platformAccountType: null == platformAccountType ? _self.platformAccountType : platformAccountType // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,nickName: null == nickName ? _self.nickName : nickName // ignore: cast_nullable_to_non_nullable
as String,primaryEmail: null == primaryEmail ? _self.primaryEmail : primaryEmail // ignore: cast_nullable_to_non_nullable
as String,leverage: null == leverage ? _self.leverage : leverage // ignore: cast_nullable_to_non_nullable
as int,serverCode: null == serverCode ? _self.serverCode : serverCode // ignore: cast_nullable_to_non_nullable
as String,platformType: null == platformType ? _self.platformType : platformType // ignore: cast_nullable_to_non_nullable
as String,leadSource: null == leadSource ? _self.leadSource : leadSource // ignore: cast_nullable_to_non_nullable
as String,balance: null == balance ? _self.balance : balance // ignore: cast_nullable_to_non_nullable
as num,margin: null == margin ? _self.margin : margin // ignore: cast_nullable_to_non_nullable
as num,equity: null == equity ? _self.equity : equity // ignore: cast_nullable_to_non_nullable
as num,profit: null == profit ? _self.profit : profit // ignore: cast_nullable_to_non_nullable
as num,grossProfit: null == grossProfit ? _self.grossProfit : grossProfit // ignore: cast_nullable_to_non_nullable
as num,dateCreated: null == dateCreated ? _self.dateCreated : dateCreated // ignore: cast_nullable_to_non_nullable
as DateTime,isDemo: null == isDemo ? _self.isDemo : isDemo // ignore: cast_nullable_to_non_nullable
as bool,classification: null == classification ? _self.classification : classification // ignore: cast_nullable_to_non_nullable
as String,accountIdLong: null == accountIdLong ? _self.accountIdLong : accountIdLong // ignore: cast_nullable_to_non_nullable
as int,credit: null == credit ? _self.credit : credit // ignore: cast_nullable_to_non_nullable
as num,accountCurrencyUsdPair: null == accountCurrencyUsdPair ? _self.accountCurrencyUsdPair : accountCurrencyUsdPair // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _Account implements Account {
  const _Account({required this.accountId, required this.accountStatus, required this.accountType, required this.accountCurrency, required this.platformAccountNumber, required this.clientId, required this.accountGroup, required this.brokerId, required this.platformAccountType, required this.name, required this.nickName, required this.primaryEmail, required this.leverage, required this.serverCode, required this.platformType, required this.leadSource, required this.balance, required this.margin, required this.equity, required this.profit, required this.grossProfit, required this.dateCreated, required this.isDemo, required this.classification, required this.accountIdLong, required this.credit, required this.accountCurrencyUsdPair});
  factory _Account.fromJson(Map<String, dynamic> json) => _$AccountFromJson(json);

@override final  String accountId;
@override final  String accountStatus;
@override final  String accountType;
@override final  String accountCurrency;
@override final  String platformAccountNumber;
@override final  String clientId;
@override final  String accountGroup;
@override final  String brokerId;
@override final  String platformAccountType;
@override final  String name;
@override final  String nickName;
@override final  String primaryEmail;
@override final  int leverage;
@override final  String serverCode;
@override final  String platformType;
@override final  String leadSource;
@override final  num balance;
@override final  num margin;
@override final  num equity;
@override final  num profit;
@override final  num grossProfit;
@override final  DateTime dateCreated;
@override final  bool isDemo;
@override final  String classification;
@override final  int accountIdLong;
@override final  num credit;
@override final  String accountCurrencyUsdPair;

/// Create a copy of Account
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AccountCopyWith<_Account> get copyWith => __$AccountCopyWithImpl<_Account>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$AccountToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Account&&(identical(other.accountId, accountId) || other.accountId == accountId)&&(identical(other.accountStatus, accountStatus) || other.accountStatus == accountStatus)&&(identical(other.accountType, accountType) || other.accountType == accountType)&&(identical(other.accountCurrency, accountCurrency) || other.accountCurrency == accountCurrency)&&(identical(other.platformAccountNumber, platformAccountNumber) || other.platformAccountNumber == platformAccountNumber)&&(identical(other.clientId, clientId) || other.clientId == clientId)&&(identical(other.accountGroup, accountGroup) || other.accountGroup == accountGroup)&&(identical(other.brokerId, brokerId) || other.brokerId == brokerId)&&(identical(other.platformAccountType, platformAccountType) || other.platformAccountType == platformAccountType)&&(identical(other.name, name) || other.name == name)&&(identical(other.nickName, nickName) || other.nickName == nickName)&&(identical(other.primaryEmail, primaryEmail) || other.primaryEmail == primaryEmail)&&(identical(other.leverage, leverage) || other.leverage == leverage)&&(identical(other.serverCode, serverCode) || other.serverCode == serverCode)&&(identical(other.platformType, platformType) || other.platformType == platformType)&&(identical(other.leadSource, leadSource) || other.leadSource == leadSource)&&(identical(other.balance, balance) || other.balance == balance)&&(identical(other.margin, margin) || other.margin == margin)&&(identical(other.equity, equity) || other.equity == equity)&&(identical(other.profit, profit) || other.profit == profit)&&(identical(other.grossProfit, grossProfit) || other.grossProfit == grossProfit)&&(identical(other.dateCreated, dateCreated) || other.dateCreated == dateCreated)&&(identical(other.isDemo, isDemo) || other.isDemo == isDemo)&&(identical(other.classification, classification) || other.classification == classification)&&(identical(other.accountIdLong, accountIdLong) || other.accountIdLong == accountIdLong)&&(identical(other.credit, credit) || other.credit == credit)&&(identical(other.accountCurrencyUsdPair, accountCurrencyUsdPair) || other.accountCurrencyUsdPair == accountCurrencyUsdPair));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,accountId,accountStatus,accountType,accountCurrency,platformAccountNumber,clientId,accountGroup,brokerId,platformAccountType,name,nickName,primaryEmail,leverage,serverCode,platformType,leadSource,balance,margin,equity,profit,grossProfit,dateCreated,isDemo,classification,accountIdLong,credit,accountCurrencyUsdPair]);

@override
String toString() {
  return 'Account(accountId: $accountId, accountStatus: $accountStatus, accountType: $accountType, accountCurrency: $accountCurrency, platformAccountNumber: $platformAccountNumber, clientId: $clientId, accountGroup: $accountGroup, brokerId: $brokerId, platformAccountType: $platformAccountType, name: $name, nickName: $nickName, primaryEmail: $primaryEmail, leverage: $leverage, serverCode: $serverCode, platformType: $platformType, leadSource: $leadSource, balance: $balance, margin: $margin, equity: $equity, profit: $profit, grossProfit: $grossProfit, dateCreated: $dateCreated, isDemo: $isDemo, classification: $classification, accountIdLong: $accountIdLong, credit: $credit, accountCurrencyUsdPair: $accountCurrencyUsdPair)';
}


}

/// @nodoc
abstract mixin class _$AccountCopyWith<$Res> implements $AccountCopyWith<$Res> {
  factory _$AccountCopyWith(_Account value, $Res Function(_Account) _then) = __$AccountCopyWithImpl;
@override @useResult
$Res call({
 String accountId, String accountStatus, String accountType, String accountCurrency, String platformAccountNumber, String clientId, String accountGroup, String brokerId, String platformAccountType, String name, String nickName, String primaryEmail, int leverage, String serverCode, String platformType, String leadSource, num balance, num margin, num equity, num profit, num grossProfit, DateTime dateCreated, bool isDemo, String classification, int accountIdLong, num credit, String accountCurrencyUsdPair
});




}
/// @nodoc
class __$AccountCopyWithImpl<$Res>
    implements _$AccountCopyWith<$Res> {
  __$AccountCopyWithImpl(this._self, this._then);

  final _Account _self;
  final $Res Function(_Account) _then;

/// Create a copy of Account
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? accountId = null,Object? accountStatus = null,Object? accountType = null,Object? accountCurrency = null,Object? platformAccountNumber = null,Object? clientId = null,Object? accountGroup = null,Object? brokerId = null,Object? platformAccountType = null,Object? name = null,Object? nickName = null,Object? primaryEmail = null,Object? leverage = null,Object? serverCode = null,Object? platformType = null,Object? leadSource = null,Object? balance = null,Object? margin = null,Object? equity = null,Object? profit = null,Object? grossProfit = null,Object? dateCreated = null,Object? isDemo = null,Object? classification = null,Object? accountIdLong = null,Object? credit = null,Object? accountCurrencyUsdPair = null,}) {
  return _then(_Account(
accountId: null == accountId ? _self.accountId : accountId // ignore: cast_nullable_to_non_nullable
as String,accountStatus: null == accountStatus ? _self.accountStatus : accountStatus // ignore: cast_nullable_to_non_nullable
as String,accountType: null == accountType ? _self.accountType : accountType // ignore: cast_nullable_to_non_nullable
as String,accountCurrency: null == accountCurrency ? _self.accountCurrency : accountCurrency // ignore: cast_nullable_to_non_nullable
as String,platformAccountNumber: null == platformAccountNumber ? _self.platformAccountNumber : platformAccountNumber // ignore: cast_nullable_to_non_nullable
as String,clientId: null == clientId ? _self.clientId : clientId // ignore: cast_nullable_to_non_nullable
as String,accountGroup: null == accountGroup ? _self.accountGroup : accountGroup // ignore: cast_nullable_to_non_nullable
as String,brokerId: null == brokerId ? _self.brokerId : brokerId // ignore: cast_nullable_to_non_nullable
as String,platformAccountType: null == platformAccountType ? _self.platformAccountType : platformAccountType // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,nickName: null == nickName ? _self.nickName : nickName // ignore: cast_nullable_to_non_nullable
as String,primaryEmail: null == primaryEmail ? _self.primaryEmail : primaryEmail // ignore: cast_nullable_to_non_nullable
as String,leverage: null == leverage ? _self.leverage : leverage // ignore: cast_nullable_to_non_nullable
as int,serverCode: null == serverCode ? _self.serverCode : serverCode // ignore: cast_nullable_to_non_nullable
as String,platformType: null == platformType ? _self.platformType : platformType // ignore: cast_nullable_to_non_nullable
as String,leadSource: null == leadSource ? _self.leadSource : leadSource // ignore: cast_nullable_to_non_nullable
as String,balance: null == balance ? _self.balance : balance // ignore: cast_nullable_to_non_nullable
as num,margin: null == margin ? _self.margin : margin // ignore: cast_nullable_to_non_nullable
as num,equity: null == equity ? _self.equity : equity // ignore: cast_nullable_to_non_nullable
as num,profit: null == profit ? _self.profit : profit // ignore: cast_nullable_to_non_nullable
as num,grossProfit: null == grossProfit ? _self.grossProfit : grossProfit // ignore: cast_nullable_to_non_nullable
as num,dateCreated: null == dateCreated ? _self.dateCreated : dateCreated // ignore: cast_nullable_to_non_nullable
as DateTime,isDemo: null == isDemo ? _self.isDemo : isDemo // ignore: cast_nullable_to_non_nullable
as bool,classification: null == classification ? _self.classification : classification // ignore: cast_nullable_to_non_nullable
as String,accountIdLong: null == accountIdLong ? _self.accountIdLong : accountIdLong // ignore: cast_nullable_to_non_nullable
as int,credit: null == credit ? _self.credit : credit // ignore: cast_nullable_to_non_nullable
as num,accountCurrencyUsdPair: null == accountCurrencyUsdPair ? _self.accountCurrencyUsdPair : accountCurrencyUsdPair // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
