// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'withdraw_request_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_WithdrawRequestModel _$WithdrawRequestModelFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('_WithdrawRequestModel', json, ($checkedConvert) {
  final val = _WithdrawRequestModel(
    paymentType: $checkedConvert('paymentType', (v) => v as String),
    accountInfo: $checkedConvert(
      'accountInfo',
      (v) => AccountInfo.fromJson(v as Map<String, dynamic>),
    ),
    amount: $checkedConvert(
      'amount',
      (v) => Amount.fromJson(v as Map<String, dynamic>),
    ),
    metadata: $checkedConvert(
      'metadata',
      (v) => Metadata.fromJson(v as Map<String, dynamic>),
    ),
  );
  return val;
});

Map<String, dynamic> _$WithdrawRequestModelToJson(
  _WithdrawRequestModel instance,
) => <String, dynamic>{
  'paymentType': instance.paymentType,
  'accountInfo': instance.accountInfo.toJson(),
  'amount': instance.amount.toJson(),
  'metadata': instance.metadata.toJson(),
};

_AccountInfo _$AccountInfoFromJson(Map<String, dynamic> json) => $checkedCreate(
  '_AccountInfo',
  json,
  ($checkedConvert) {
    final val = _AccountInfo(
      tradingAccountId: $checkedConvert('tradingAccountId', (v) => v as String),
      accountCurrency: $checkedConvert('accountCurrency', (v) => v as String),
    );
    return val;
  },
);

Map<String, dynamic> _$AccountInfoToJson(_AccountInfo instance) =>
    <String, dynamic>{
      'tradingAccountId': instance.tradingAccountId,
      'accountCurrency': instance.accountCurrency,
    };

_Amount _$AmountFromJson(Map<String, dynamic> json) =>
    $checkedCreate('_Amount', json, ($checkedConvert) {
      final val = _Amount(
        currency: $checkedConvert('currency', (v) => v as String),
        conversionRateString: $checkedConvert(
          'conversionRateString',
          (v) => v as String,
        ),
        amount: $checkedConvert('amount', (v) => v as num),
        convertedAmount: $checkedConvert('convertedAmount', (v) => v as num),
        conversionRate: $checkedConvert('conversionRate', (v) => v as num),
      );
      return val;
    });

Map<String, dynamic> _$AmountToJson(_Amount instance) => <String, dynamic>{
  'currency': instance.currency,
  'conversionRateString': instance.conversionRateString,
  'amount': instance.amount,
  'convertedAmount': instance.convertedAmount,
  'conversionRate': instance.conversionRate,
};

CardMetadata _$CardMetadataFromJson(Map<String, dynamic> json) =>
    $checkedCreate('CardMetadata', json, ($checkedConvert) {
      final val = CardMetadata(
        card: $checkedConvert(
          'card',
          (v) => Card.fromJson(v as Map<String, dynamic>),
        ),
        $type: $checkedConvert('runtimeType', (v) => v as String?),
      );
      return val;
    }, fieldKeyMap: const {r'$type': 'runtimeType'});

Map<String, dynamic> _$CardMetadataToJson(CardMetadata instance) =>
    <String, dynamic>{
      'card': instance.card.toJson(),
      'runtimeType': instance.$type,
    };

SkrillMetadata _$SkrillMetadataFromJson(Map<String, dynamic> json) =>
    $checkedCreate('SkrillMetadata', json, ($checkedConvert) {
      final val = SkrillMetadata(
        skrill: $checkedConvert(
          'skrill',
          (v) => Skrill.fromJson(v as Map<String, dynamic>),
        ),
        $type: $checkedConvert('runtimeType', (v) => v as String?),
      );
      return val;
    }, fieldKeyMap: const {r'$type': 'runtimeType'});

Map<String, dynamic> _$SkrillMetadataToJson(SkrillMetadata instance) =>
    <String, dynamic>{
      'skrill': instance.skrill.toJson(),
      'runtimeType': instance.$type,
    };

NetellerMetadata _$NetellerMetadataFromJson(Map<String, dynamic> json) =>
    $checkedCreate('NetellerMetadata', json, ($checkedConvert) {
      final val = NetellerMetadata(
        neteller: $checkedConvert(
          'neteller',
          (v) => Neteller.fromJson(v as Map<String, dynamic>),
        ),
        $type: $checkedConvert('runtimeType', (v) => v as String?),
      );
      return val;
    }, fieldKeyMap: const {r'$type': 'runtimeType'});

Map<String, dynamic> _$NetellerMetadataToJson(NetellerMetadata instance) =>
    <String, dynamic>{
      'neteller': instance.neteller.toJson(),
      'runtimeType': instance.$type,
    };

BankMetadata _$BankMetadataFromJson(Map<String, dynamic> json) =>
    $checkedCreate('BankMetadata', json, ($checkedConvert) {
      final val = BankMetadata(
        transferType: $checkedConvert('transferType', (v) => v as String),
        bankAccount: $checkedConvert(
          'bankAccount',
          (v) => BankAccount.fromJson(v as Map<String, dynamic>),
        ),
        $type: $checkedConvert('runtimeType', (v) => v as String?),
      );
      return val;
    }, fieldKeyMap: const {r'$type': 'runtimeType'});

Map<String, dynamic> _$BankMetadataToJson(BankMetadata instance) =>
    <String, dynamic>{
      'transferType': instance.transferType,
      'bankAccount': instance.bankAccount.toJson(),
      'runtimeType': instance.$type,
    };

_BankAccount _$BankAccountFromJson(Map<String, dynamic> json) =>
    $checkedCreate('_BankAccount', json, ($checkedConvert) {
      final val = _BankAccount(
        country: $checkedConvert('country', (v) => v as String),
        accountHolder: $checkedConvert('accountHolder', (v) => v as String),
        bankName: $checkedConvert('bankName', (v) => v as String),
        accountNickname: $checkedConvert('accountNickname', (v) => v as String),
        branchName: $checkedConvert('branchName', (v) => v as String),
        swiftBic: $checkedConvert('swiftBic', (v) => v as String),
        iban: $checkedConvert('iban', (v) => v as String),
      );
      return val;
    });

Map<String, dynamic> _$BankAccountToJson(_BankAccount instance) =>
    <String, dynamic>{
      'country': instance.country,
      'accountHolder': instance.accountHolder,
      'bankName': instance.bankName,
      'accountNickname': instance.accountNickname,
      'branchName': instance.branchName,
      'swiftBic': instance.swiftBic,
      'iban': instance.iban,
    };

_Card _$CardFromJson(Map<String, dynamic> json) =>
    $checkedCreate('_Card', json, ($checkedConvert) {
      final val = _Card(id: $checkedConvert('id', (v) => v as String));
      return val;
    });

Map<String, dynamic> _$CardToJson(_Card instance) => <String, dynamic>{
  'id': instance.id,
};

_Skrill _$SkrillFromJson(Map<String, dynamic> json) =>
    $checkedCreate('_Skrill', json, ($checkedConvert) {
      final val = _Skrill(email: $checkedConvert('email', (v) => v as String));
      return val;
    });

Map<String, dynamic> _$SkrillToJson(_Skrill instance) => <String, dynamic>{
  'email': instance.email,
};

_Neteller _$NetellerFromJson(Map<String, dynamic> json) => $checkedCreate(
  '_Neteller',
  json,
  ($checkedConvert) {
    final val = _Neteller(email: $checkedConvert('email', (v) => v as String));
    return val;
  },
);

Map<String, dynamic> _$NetellerToJson(_Neteller instance) => <String, dynamic>{
  'email': instance.email,
};
