/// Enum to define the type of conversion
enum ConversionType {
  /// Convert from account currency to target currency (isReversedConversion = false)
  accountToTargetCurrency,

  /// Convert from target currency to account currency (isReversedConversion = true)
  targetToAccountCurrency,
}

/// Extension to provide helper methods for ConversionType
extension ConversionTypeExtension on ConversionType {
  /// Returns true if this is a reversed conversion (target to account currency)
  bool get isReversedConversion {
    return this == ConversionType.targetToAccountCurrency;
  }
}
