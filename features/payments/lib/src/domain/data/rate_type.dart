import 'payment_type.dart';

enum RateType { Sell, Buy }

extension RateTypeExtension on RateType {
  /// Get rate type based on payment type
  /// - deposit and transfer return Sell
  /// - withdrawal returns Buy
  static RateType fromPaymentType(PaymentType paymentType) {
    switch (paymentType) {
      case PaymentType.deposit:
      case PaymentType.transfer:
        return RateType.Sell;
      case PaymentType.withdrawal:
        return RateType.Buy;
    }
  }
}
