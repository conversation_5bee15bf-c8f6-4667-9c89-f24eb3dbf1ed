import 'dart:io';

import 'package:payment/src/data/conversion_rate/conversion_rate_model.dart';
import 'package:payment/src/domain/data/payment_type.dart';
import 'package:payment/src/domain/data/rate_type.dart';
import 'package:payment/src/domain/repository/payment_options_repository.dart';
import 'package:prelude/prelude.dart';
import 'package:user_account/user_account.dart';

class ConversionRateUsecase {
  final PaymentOptionsRepository repository;
  final GetBrokerIdUseCase getBrokerIdUseCase;

  const ConversionRateUsecase({
    required this.repository,
    required this.getBrokerIdUseCase,
  });
  TaskEither<Exception, ConversionRateModel> call({
    required String sourceCurrency,
    required String targetCurrency,
    required PaymentType paymentType,
  }) {
    String? brokerId = getBrokerIdUseCase();
    if (Platform.environment.containsKey('FLUTTER_TEST')) {
      brokerId = "SCA";
    }
    if (brokerId == null) {
      return TaskEither.left(Exception('Broker ID not found'));
    }
    // Determine rate type based on payment type
    final rateType = RateTypeExtension.fromPaymentType(paymentType);

    return repository.getConversionRate(
      sourceCurrency: sourceCurrency,
      targetCurrency: targetCurrency,
      rateType: rateType.name,
      brokerId: brokerId,
    );
  }
}
