import 'dart:developer';

import 'package:payment/src/data/deposit_request_model.dart';
import 'package:payment/src/data/deposit_response.dart';
import 'package:payment/src/domain/data/deposit_mop.dart';
import 'package:payment/src/domain/repository/deposit_repository.dart';
import 'package:prelude/prelude.dart';

class GetDepositDetailsUsecase {
  final DepositRepository repository;
  const GetDepositDetailsUsecase({required this.repository});
  TaskEither<Exception, DepositResponse> call({
    required String tradingAccountId,
    required String clientId,
    required String accountCurrency,
    required String depositCurrency,
    required double amount,
    required double convertedAmount,
    required double conversionRate,
    required DepositMop mop,
  }) {
    final data = DepositRequestModel(
      accountInfo: AccountInfo(
        tradingAccountId: tradingAccountId,
        clientId: clientId,
        accountCurrency: accountCurrency,
      ),
      amountDetails: AmountDetails(
        currency: depositCurrency,
        conversionRateString: conversionRate.toString(),
        amount: amount,
        convertedAmount: convertedAmount,
        conversionRate: conversionRate,
      ),
      campaigns: Campaigns(id: "", optedIn: false),
      mop: mop,
      language: "en",
      view: "trader",
    );

    return repository.getDepositDetails(data: data);
  }
}
