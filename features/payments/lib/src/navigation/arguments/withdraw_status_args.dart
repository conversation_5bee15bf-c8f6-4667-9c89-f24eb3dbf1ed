import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:payment/src/domain/data/withdraw_status_type.dart';

part 'withdraw_status_args.freezed.dart';
part 'withdraw_status_args.g.dart';

@freezed
abstract class WithdrawStatusArgs with _$WithdrawStatusArgs {
  const factory WithdrawStatusArgs({
    WithdrawStatusType? statusType,
    @JsonKey(includeFromJson: false, includeToJson: false)
    VoidCallback? onButtonPressed,
    String? popUntilRoute,
  }) = _WithdrawStatusArgs;

  factory WithdrawStatusArgs.fromJson(Map<String, dynamic> json) =>
      _$WithdrawStatusArgsFromJson(json);
}
