// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'withdraw_status_args.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$WithdrawStatusArgs {

 WithdrawStatusType? get statusType;@JsonKey(includeFromJson: false, includeToJson: false) VoidCallback? get onButtonPressed; String? get popUntilRoute;
/// Create a copy of WithdrawStatusArgs
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$WithdrawStatusArgsCopyWith<WithdrawStatusArgs> get copyWith => _$WithdrawStatusArgsCopyWithImpl<WithdrawStatusArgs>(this as WithdrawStatusArgs, _$identity);

  /// Serializes this WithdrawStatusArgs to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WithdrawStatusArgs&&(identical(other.statusType, statusType) || other.statusType == statusType)&&(identical(other.onButtonPressed, onButtonPressed) || other.onButtonPressed == onButtonPressed)&&(identical(other.popUntilRoute, popUntilRoute) || other.popUntilRoute == popUntilRoute));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,statusType,onButtonPressed,popUntilRoute);

@override
String toString() {
  return 'WithdrawStatusArgs(statusType: $statusType, onButtonPressed: $onButtonPressed, popUntilRoute: $popUntilRoute)';
}


}

/// @nodoc
abstract mixin class $WithdrawStatusArgsCopyWith<$Res>  {
  factory $WithdrawStatusArgsCopyWith(WithdrawStatusArgs value, $Res Function(WithdrawStatusArgs) _then) = _$WithdrawStatusArgsCopyWithImpl;
@useResult
$Res call({
 WithdrawStatusType? statusType,@JsonKey(includeFromJson: false, includeToJson: false) VoidCallback? onButtonPressed, String? popUntilRoute
});




}
/// @nodoc
class _$WithdrawStatusArgsCopyWithImpl<$Res>
    implements $WithdrawStatusArgsCopyWith<$Res> {
  _$WithdrawStatusArgsCopyWithImpl(this._self, this._then);

  final WithdrawStatusArgs _self;
  final $Res Function(WithdrawStatusArgs) _then;

/// Create a copy of WithdrawStatusArgs
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? statusType = freezed,Object? onButtonPressed = freezed,Object? popUntilRoute = freezed,}) {
  return _then(_self.copyWith(
statusType: freezed == statusType ? _self.statusType : statusType // ignore: cast_nullable_to_non_nullable
as WithdrawStatusType?,onButtonPressed: freezed == onButtonPressed ? _self.onButtonPressed : onButtonPressed // ignore: cast_nullable_to_non_nullable
as VoidCallback?,popUntilRoute: freezed == popUntilRoute ? _self.popUntilRoute : popUntilRoute // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _WithdrawStatusArgs implements WithdrawStatusArgs {
  const _WithdrawStatusArgs({this.statusType, @JsonKey(includeFromJson: false, includeToJson: false) this.onButtonPressed, this.popUntilRoute});
  factory _WithdrawStatusArgs.fromJson(Map<String, dynamic> json) => _$WithdrawStatusArgsFromJson(json);

@override final  WithdrawStatusType? statusType;
@override@JsonKey(includeFromJson: false, includeToJson: false) final  VoidCallback? onButtonPressed;
@override final  String? popUntilRoute;

/// Create a copy of WithdrawStatusArgs
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$WithdrawStatusArgsCopyWith<_WithdrawStatusArgs> get copyWith => __$WithdrawStatusArgsCopyWithImpl<_WithdrawStatusArgs>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$WithdrawStatusArgsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _WithdrawStatusArgs&&(identical(other.statusType, statusType) || other.statusType == statusType)&&(identical(other.onButtonPressed, onButtonPressed) || other.onButtonPressed == onButtonPressed)&&(identical(other.popUntilRoute, popUntilRoute) || other.popUntilRoute == popUntilRoute));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,statusType,onButtonPressed,popUntilRoute);

@override
String toString() {
  return 'WithdrawStatusArgs(statusType: $statusType, onButtonPressed: $onButtonPressed, popUntilRoute: $popUntilRoute)';
}


}

/// @nodoc
abstract mixin class _$WithdrawStatusArgsCopyWith<$Res> implements $WithdrawStatusArgsCopyWith<$Res> {
  factory _$WithdrawStatusArgsCopyWith(_WithdrawStatusArgs value, $Res Function(_WithdrawStatusArgs) _then) = __$WithdrawStatusArgsCopyWithImpl;
@override @useResult
$Res call({
 WithdrawStatusType? statusType,@JsonKey(includeFromJson: false, includeToJson: false) VoidCallback? onButtonPressed, String? popUntilRoute
});




}
/// @nodoc
class __$WithdrawStatusArgsCopyWithImpl<$Res>
    implements _$WithdrawStatusArgsCopyWith<$Res> {
  __$WithdrawStatusArgsCopyWithImpl(this._self, this._then);

  final _WithdrawStatusArgs _self;
  final $Res Function(_WithdrawStatusArgs) _then;

/// Create a copy of WithdrawStatusArgs
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? statusType = freezed,Object? onButtonPressed = freezed,Object? popUntilRoute = freezed,}) {
  return _then(_WithdrawStatusArgs(
statusType: freezed == statusType ? _self.statusType : statusType // ignore: cast_nullable_to_non_nullable
as WithdrawStatusType?,onButtonPressed: freezed == onButtonPressed ? _self.onButtonPressed : onButtonPressed // ignore: cast_nullable_to_non_nullable
as VoidCallback?,popUntilRoute: freezed == popUntilRoute ? _self.popUntilRoute : popUntilRoute // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
