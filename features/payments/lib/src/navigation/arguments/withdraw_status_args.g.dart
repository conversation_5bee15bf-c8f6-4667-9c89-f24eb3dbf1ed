// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'withdraw_status_args.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_WithdrawStatusArgs _$WithdrawStatusArgsFromJson(Map<String, dynamic> json) =>
    $checkedCreate('_WithdrawStatusArgs', json, ($checkedConvert) {
      final val = _WithdrawStatusArgs(
        statusType: $checkedConvert(
          'statusType',
          (v) => $enumDecodeNullable(_$WithdrawStatusTypeEnumMap, v),
        ),
        popUntilRoute: $checkedConvert('popUntilRoute', (v) => v as String?),
      );
      return val;
    });

Map<String, dynamic> _$WithdrawStatusArgsToJson(_WithdrawStatusArgs instance) =>
    <String, dynamic>{
      if (_$WithdrawStatusTypeEnumMap[instance.statusType] case final value?)
        'statusType': value,
      if (instance.popUntilRoute case final value?) 'popUntilRoute': value,
    };

const _$WithdrawStatusTypeEnumMap = {
  WithdrawStatusType.newStatus: 'New',
  WithdrawStatusType.pendingApproval: 'PendingApproval',
  WithdrawStatusType.successful: 'Successful',
  WithdrawStatusType.rejected: 'Rejected',
  WithdrawStatusType.error: 'Error',
  WithdrawStatusType.awaitingDocuments: 'AwaitingDocuments',
};
