import 'package:equiti_router/equiti_router.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:payment/src/navigation/arguments/withdraw_status_args.dart';
import 'package:payment/src/navigation/payment_route_schema.dart';
import 'package:payment/src/presentation/widgets/withdraw_status_screen.dart';

class WithdrawStatusPage extends EquitiPage {
  const WithdrawStatusPage();

  @override
  Widget builder(
    BuildContext context,
    EquitiRoute routeData,
    Map<String, dynamic> globalData,
  ) {
    final arguments = routeData.arguments as WithdrawStatusArgs;
    return WithdrawStatusScreen.fromWithDrawStatus(
      statusType: arguments.statusType,
      onButtonPressed: arguments.onButtonPressed,
      popUntilRoute: arguments.popUntilRoute,
    );
  }

  @override
  String get label => PaymentRouteSchema.withdrawStatusRoute.label;

  @override
  String get url => PaymentRouteSchema.withdrawStatusRoute.url;
}
