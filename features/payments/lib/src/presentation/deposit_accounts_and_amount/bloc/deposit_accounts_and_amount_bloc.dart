import 'dart:async';
import 'dart:developer';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:payment/src/data/account_model.dart';
import 'package:payment/src/data/conversion_rate/conversion_rate_model.dart';
import 'package:payment/src/data/deposit_request_model.dart';
import 'package:payment/src/data/payment_method/deposit_payment_methods_model/deposit_payment_methods_model.dart';
import 'package:payment/src/data/deposit_response.dart';
import 'package:payment/src/domain/data/deposit_mop.dart';
import 'package:payment/src/domain/data/withdrawal_mop_types.dart';
import 'package:payment/src/domain/usecase/get_deposit_details_usecase.dart';
import 'package:payment/src/navigation/payment_navigation.dart';
import 'package:payment/src/utils/payment/apple_pay_utils.dart';
import 'package:payment/src/utils/payment/google_pay_utils.dart';

part 'deposit_accounts_and_amount_bloc.freezed.dart';
part 'deposit_accounts_and_amount_event.dart';
part 'deposit_accounts_and_amount_state.dart';

class DepositAccountsAndAmountBloc
    extends Bloc<DepositAccountsAndAmountEvent, DepositAccountsAndAmountState> {
  final GetDepositDetailsUsecase _getDepositDetailsUsecase;
  final PaymentNavigation _paymentNavigation;
  final GooglePayUtils _googlePayUtils;
  final ApplePayUtils _applePayUtils;

  final num? _maxPollingAttempts;
  final num? _pollingFrequencySeconds;

  DepositAccountsAndAmountBloc({
    required GetDepositDetailsUsecase getDepositDetailsUsecase,
    required PaymentNavigation paymentNavigation,
    required GooglePayUtils googlePayUtils,
    required ApplePayUtils applePayUtils,
    num? maxPollingAttempts,
    num? pollingFrequencySeconds,
  }) : _paymentNavigation = paymentNavigation,
       _getDepositDetailsUsecase = getDepositDetailsUsecase,
       _googlePayUtils = googlePayUtils,
       _applePayUtils = applePayUtils,
       _maxPollingAttempts = maxPollingAttempts,
       _pollingFrequencySeconds = pollingFrequencySeconds,
       super(DepositAccountsAndAmountState()) {
    on<_OnAccountChange>(_onAccountChange);
    on<_NaviagteToSelectedPaymentMethod>(_navigateToSelectedPaymentMethod);
    on<_ChangeButtonState>(_changeButtonState);
    on<_OnAmountChange>(_onAmountChange);
    on<_GetDepositDetails>(_getDepositDetails);
    on<_OnApplePayResult>(_onApplePayResult);
    on<_OnGooglePayResult>(_onGooglePayResult);
  }

  FutureOr<void> _onAccountChange(
    _OnAccountChange event,
    Emitter<DepositAccountsAndAmountState> emit,
  ) {
    emit(state.copyWith(selectedAccount: event.account));
  }

  FutureOr<void> _navigateToSelectedPaymentMethod(
    _NaviagteToSelectedPaymentMethod event,
    Emitter<DepositAccountsAndAmountState> emit,
  ) {
    switch (event.mop) {
      default:
        break;
    }
  }

  FutureOr<void> _changeButtonState(
    _ChangeButtonState event,
    Emitter<DepositAccountsAndAmountState> emit,
  ) {
    emit(state.copyWith(isButtonEnabled: event.isValid));
  }

  FutureOr<void> _onAmountChange(
    _OnAmountChange event,
    Emitter<DepositAccountsAndAmountState> emit,
  ) {
    emit(
      state.copyWith(
        amount: event.amount,
        convertedAmount: event.convertedAmount,
        ratesModel: event.ratesModel,
        conversionRateString:
            event.conversionRateString ??
            '1 ${state.selectedAccount?.accountCurrency ?? ''} = 1 ${state.selectedAccount?.accountCurrency ?? ''}',
        targetCurrency:
            event.targetCurrency ??
            state.selectedAccount?.accountCurrency ??
            '',
      ),
    );
  }

  FutureOr<void> _getDepositDetails(
    _GetDepositDetails event,
    Emitter<DepositAccountsAndAmountState> emit,
  ) async {
    try {
      if (!isClosed) {
        emit(state.copyWith(isButtonLoading: true));
      }
      if (event.paymentMethod.mop == null) {
        emit(
          state.copyWith(isButtonLoading: false, processState: ErrorState()),
        );
        return;
      }
      final result =
          await _getDepositDetailsUsecase(
            tradingAccountId: state.selectedAccount!.accountId,
            clientId: state.selectedAccount!.clientId,
            accountCurrency: state.selectedAccount!.accountCurrency,
            depositCurrency: state.targetCurrency,
            amount: double.parse(state.amount),
            convertedAmount: double.parse(state.convertedAmount),
            conversionRate: state.ratesModel?.rate ?? 1,
            mop: event.paymentMethod.mop!,
          ).run();

      result.fold(
        (l) {
          log('Error: ${l.toString()}');
          if (!isClosed) {
            emit(state.copyWith(isButtonLoading: false));
          }
          addError(l);
        },
        (r) {
          log(
            "paymentMethod: ${event.paymentMethod.mop ?? "NA"} and here is result ${r.data.toJson()}",
          );
          switch (event.paymentMethod.mop) {
            case DepositMop.card:
              _handleCardPayment(r.data, emit);
              break;
            case DepositMop.apple_pay:
              _handleApplePayPayment(r.data);
              break;
            case DepositMop.google_pay:
              _handleGooglePayPayment(r.data, emit);
              break;
            case DepositMop.bridgerpay:
              _handleBridgerPayPayment(r.data);
              break;
            default:
              break;
          }
        },
      );
    } on Exception catch (_) {
      // TODO (Aakash): Handle error
      if (!isClosed) {
        emit(state.copyWith(isButtonLoading: false));
      }
    }
  }

  void _handleCardPayment(
    DepositResponseData data,
    Emitter<DepositAccountsAndAmountState> emit,
  ) {
    if (!isClosed) {
      emit(state.copyWith(isButtonLoading: false));
    }
    _paymentNavigation.goToEquitiPayCardsScreen(
      data.redirectUrl,
      "Deposit with Card",
      data.transactionId,
      state.selectedAccount!.platformAccountNumber,
      maxPollingAttempts: _maxPollingAttempts,
      pollingFrequencySeconds: _pollingFrequencySeconds,
    );
  }

  void _handleApplePayPayment(DepositResponseData data) {
    log('Apple Pay Payment Initiated ✅');
    _applePayUtils.setOnPaymentCompleteCallback(({
      String? gatewayCode,
      required ApplePayStatus paymentStatus,
    }) {
      if (!isClosed) {
        add(
          _OnApplePayResult(
            paymentStatus: paymentStatus,
            gatewayCode: gatewayCode,
            applePay: _applePayUtils,
          ),
        );
      }
    });
    log('Apple Pay Payment session started ✅');
    _applePayUtils.initiateSession(data.paymentProviderId);
  }

  FutureOr<void> _onApplePayResult(
    _OnApplePayResult event,
    Emitter<DepositAccountsAndAmountState> emit,
  ) {
    log(
      'paymentStatus: ${event.paymentStatus}, gatewayCode: ${event.gatewayCode ?? "null"}',
    );
    if (event.paymentStatus == ApplePayStatus.success) {
      emit(
        state.copyWith(
          processState: PaymentSuccessState(),
          isButtonLoading: false,
        ),
      );
    } else {
      emit(
        state.copyWith(
          processState: PaymentFailedState(),
          isButtonLoading: false,
        ),
      );
    }
    event.applePay.dispose();
  }

  void _handleGooglePayPayment(
    DepositResponseData data,
    Emitter<DepositAccountsAndAmountState> emit,
  ) {
    log('Google Pay Payment Initiated ✅');
    _googlePayUtils.setOnPaymentCompleteCallback(({
      String? gatewayCode,
      required GooglePayStatus paymentStatus,
    }) {
      if (!isClosed) {
        add(
          _OnGooglePayResult(
            paymentStatus: paymentStatus,
            gatewayCode: gatewayCode,
            googlePay: _googlePayUtils,
          ),
        );
      }
    });
    log('Google Pay Payment session started ✅');
    _googlePayUtils.initiateSession(data.paymentProviderId);
    emit(state.copyWith(isButtonLoading: false));
  }

  FutureOr<void> _onGooglePayResult(
    _OnGooglePayResult event,
    Emitter<DepositAccountsAndAmountState> emit,
  ) {
    log(
      'paymentStatus: ${event.paymentStatus}, gatewayCode: ${event.gatewayCode ?? "null"}',
    );
    if (event.paymentStatus == GooglePayStatus.success) {
      emit(
        state.copyWith(
          processState: PaymentSuccessState(),
          isButtonLoading: false,
        ),
      );
    } else {
      emit(
        state.copyWith(
          processState: PaymentFailedState(),
          isButtonLoading: false,
        ),
      );
    }
    event.googlePay.dispose();
  }

  void _handleBridgerPayPayment(DepositResponseData data) {
    _paymentNavigation.goToAdditionalPaymentScreen(
      data.redirectUrl,
      "Deposit with eWallets",
      data.transactionId,
      state.selectedAccount!.platformAccountNumber,
      maxPollingAttempts: _maxPollingAttempts,
      pollingFrequencySeconds: _pollingFrequencySeconds,
    );
  }
}
