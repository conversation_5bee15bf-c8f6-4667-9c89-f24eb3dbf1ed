part of 'deposit_accounts_and_amount_bloc.dart';

@freezed
sealed class DepositAccountsAndAmountState
    with _$DepositAccountsAndAmountState {
  const factory DepositAccountsAndAmountState({
    Account? selectedAccount,
    @Default(false) bool isButtonEnabled,
    @Default('') String amount,
    @Default('') String convertedAmount,
    @Default(null) RatesModel? ratesModel,
    @Default(false) bool isButtonLoading,
    @Default('') String conversionRateString,
    @Default('') String targetCurrency,
    @Default(LoadedState()) DepositAccountsAndAmountProcessState processState,
  }) = _DepositAccountsAndAmountState;
}

@freezed
sealed class DepositAccountsAndAmountProcessState
    with _$DepositAccountsAndAmountProcessState {
  const factory DepositAccountsAndAmountProcessState.loaded() = LoadedState;
  const factory DepositAccountsAndAmountProcessState.error() = ErrorState;
  const factory DepositAccountsAndAmountProcessState.paymentFailed() =
      PaymentFailedState;
  const factory DepositAccountsAndAmountProcessState.paymentSuccess() =
      PaymentSuccessState;
}
