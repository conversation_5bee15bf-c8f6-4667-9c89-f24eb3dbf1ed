import 'dart:developer';

import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:equiti_localization/l10n/gen/equiti_localization.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:payment/src/data/account_model.dart';
import 'package:payment/src/data/conversion_rate/conversion_rate_model.dart';
import 'package:payment/src/data/payment_method/deposit_payment_methods_model/deposit_payment_methods_model.dart';
import 'package:payment/src/domain/data/conversion_type.dart';
import 'package:payment/src/domain/data/deposit_mop.dart';
import 'package:payment/src/domain/data/payment_type.dart';
import 'package:payment/src/presentation/deposit_accounts_and_amount/bloc/deposit_accounts_and_amount_bloc.dart';
import 'package:payment/src/presentation/widgets/account_list_widget/account_list_widget.dart';
import 'package:payment/src/presentation/widgets/amount_conversion_widget/amount_conversion_widget.dart';

class DepositAccountsAmountLoadedScreen extends StatelessWidget {
  const DepositAccountsAmountLoadedScreen({
    super.key,
    required this.paymentMethod,
  });
  final DepositPaymentMethod paymentMethod;

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final EquitiLocalization localization = EquitiLocalization.of(context);
    return Scaffold(
      backgroundColor: theme.background.bgPrimary,
      appBar: DuploAppBar(
        title: "${localization.payments_deposit_with} ${paymentMethod.name}",
      ),
      body: GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();
        },
        child: SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                DuploText(
                  text: localization.payments_deposit_to_account,
                  style: DuploTextStyles.of(context).textLg,
                  fontWeight: DuploFontWeight.semiBold,
                  color: theme.text.textPrimary,
                ),
                const SizedBox(height: 5),
                DuploText(
                  text: localization.payments_deposit_select_trading_account,
                  style: DuploTextStyles.of(context).textSm,
                  fontWeight: DuploFontWeight.regular,
                  color: theme.text.textSecondary,
                ),
                const SizedBox(height: 15),
                BlocBuilder<
                  DepositAccountsAndAmountBloc,
                  DepositAccountsAndAmountState
                >(
                  buildWhen:
                      (previous, current) => current.selectedAccount != null,
                  builder: (builderContext, state) {
                    return AccountListWidget(
                      args: (
                        selectByHighestBalance: false,
                        excludeAccountNumber: null,
                        onEmptyStateChanged: null,
                      ),
                      onAccountSelected: (Account account) {
                        log("Account selected: ${account.toJson()}");
                        builderContext.read<DepositAccountsAndAmountBloc>().add(
                          DepositAccountsAndAmountEvent.onAccountChange(
                            account,
                          ),
                        );
                      },
                    );
                  },
                ),
                const SizedBox(height: 15),

                BlocBuilder<
                  DepositAccountsAndAmountBloc,
                  DepositAccountsAndAmountState
                >(
                  buildWhen:
                      (previous, current) =>
                          previous.selectedAccount != current.selectedAccount,
                  builder: (builderContext, state) {
                    return state.selectedAccount != null
                        ? AmountConversionWidget(
                          args: (
                            transferCurrency:
                                state.selectedAccount!.accountCurrency,
                            transferCurrencyImage: '',
                            currencyAmountDetails:
                                paymentMethod.currencyAmountDetails ?? [],
                            currencies: paymentMethod.currencies ?? [],
                            showSuggestedAmounts: true,
                            isStartWithConversionRate: false,
                            targetCurrency: null,
                            externalErrorMessage: null,
                            paymentType: PaymentType.deposit,
                            conversionType:
                                ConversionType.targetToAccountCurrency,
                          ),
                          onAmountChange: ({
                            required String amount,
                            required bool isAmountValid,
                            required String convertedAmount,
                            required RatesModel? ratesModel,
                            String? conversionRateString,
                            required ConversionRateModel? conversionRateData,
                            String? targetCurrency,
                          }) {
                            builderContext
                                .read<DepositAccountsAndAmountBloc>()
                                .add(
                                  DepositAccountsAndAmountEvent.changeButtonState(
                                    isAmountValid,
                                  ),
                                );
                            if (isAmountValid) {
                              builderContext
                                  .read<DepositAccountsAndAmountBloc>()
                                  .add(
                                    DepositAccountsAndAmountEvent.onAmountChange(
                                      amount,
                                      convertedAmount,
                                      ratesModel,
                                      conversionRateString,
                                      targetCurrency,
                                    ),
                                  );
                            }
                          },
                        )
                        : Container();
                  },
                ),
                const SizedBox(height: DuploSpacing.spacing_3xl_24),
                BlocBuilder<
                  DepositAccountsAndAmountBloc,
                  DepositAccountsAndAmountState
                >(
                  buildWhen:
                      (previous, current) =>
                          previous.isButtonEnabled != current.isButtonEnabled ||
                          previous.isButtonLoading != current.isButtonLoading,
                  builder: (builderContext, state) {
                    return switch (paymentMethod.mop) {
                      DepositMop.apple_pay => DuploButton.applePay(
                        isApplePayDarkMode: true,
                        onTap: () {
                          _getDepositDetails(builderContext);
                        },
                        isLoading: state.isButtonLoading,
                        isDisabled: !state.isButtonEnabled,
                      ),
                      DepositMop.google_pay => DuploButton.googlePay(
                        isGooglePayDarkMode: true,
                        onTap: () {
                          _getDepositDetails(builderContext);
                        },
                        isLoading: state.isButtonLoading,
                        isDisabled: !state.isButtonEnabled,
                      ),
                      DepositMop.bank ||
                      DepositMop.card ||
                      DepositMop.bridgerpay ||
                      null => DuploButton.defaultPrimary(
                        title: "Continue",
                        onTap: () {
                          _getDepositDetails(builderContext);
                        },
                        isDisabled: !state.isButtonEnabled,
                        isLoading: state.isButtonLoading,
                        trailingIcon:
                            Assets.images
                                .chevronRightDirectional(context)
                                .keyName,

                        useFullWidth: true,
                      ),
                    };
                  },
                ),
                const SizedBox(height: 20), // Extra space at the bottom
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _getDepositDetails(BuildContext ctx) {
    ctx.read<DepositAccountsAndAmountBloc>().add(
      DepositAccountsAndAmountEvent.getDepositDetails(
        paymentMethod: paymentMethod,
      ),
    );
  }
}
