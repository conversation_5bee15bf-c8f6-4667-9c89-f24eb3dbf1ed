import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:payment/src/data/payment_method/deposit_payment_methods_model/deposit_payment_methods_model.dart';
import 'package:payment/src/di/di_container.dart';

import 'bloc/deposit_payment_options_bloc.dart';
import 'widgets/payment_method_tile.dart';

class DepositPaymentOptions extends StatelessWidget {
  const DepositPaymentOptions({super.key});

  @override
  Widget build(BuildContext context) {
    final localization = EquitiLocalization.of(context);
    final theme = DuploTheme.of(context);
    return BlocProvider(
      create:
          (providerContext) =>
              diContainer<DepositPaymentOptionsBloc>()
                ..add(const DepositPaymentOptionsEvent.getPaymentOptions()),
      child: <PERSON><PERSON><PERSON>er<DepositPaymentOptionsBloc, DepositPaymentOptionsState>(
        buildWhen:
            (previous, current) =>
                previous.paymentMethodsProccessState !=
                current.paymentMethodsProccessState,
        builder: (builderContext, state) {
          return Scaffold(
            appBar: DuploAppBar(title: localization.payments_deposit),
            backgroundColor: theme.background.bgPrimary,
            body: switch (state.paymentMethodsProccessState) {
              PaymentMethodsProccessStateLoading() => Center(
                child: DuploLoadingIndicator(
                  color: theme.button.buttonPrimaryBg,
                ),
              ),
              PaymentMethodsProccessStateSuccess() => ListView.builder(
                itemCount:
                    state.paymentMethodsData?.data.paymentsMethods.length ?? 0,
                itemBuilder: (itemBuilderContext, mainIndex) {
                  final DepositPaymentMethodGroup? paymentMethodGroup = state
                      .paymentMethodsData
                      ?.data
                      .paymentsMethods
                      .elementAtOrNull(mainIndex);

                  return Padding(
                    padding: const EdgeInsets.symmetric(
                      vertical: 10,
                      horizontal: 15,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            SizedBox(width: 15),
                            DuploText(
                              text: paymentMethodGroup?.label ?? "",
                              color: theme.text.textPrimary,
                              style: DuploTextStyles.of(context).textMd,
                              fontWeight: DuploFontWeight.semiBold,
                            ),
                          ],
                        ),
                        for (DepositPaymentMethod paymentMethod
                            in paymentMethodGroup?.methods ?? [])
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: 8),
                            child: DuploTap(
                              radius: DuploRadius.radius_md_8,
                              onTap: () {
                                builderContext
                                    .read<DepositPaymentOptionsBloc>()
                                    .add(
                                      DepositPaymentOptionsEvent.navigateToSelectAccountAndAmount(
                                        paymentMethodGroup: paymentMethodGroup!,
                                        paymentMethod: paymentMethod,
                                      ),
                                    );
                              },
                              child: PaymentMethodTile(
                                currencies: paymentMethod.currencies ?? [],
                                imageUrl: paymentMethod.imageUrl ?? "",
                                tags: paymentMethod.tag ?? [],
                                name: paymentMethod.name,
                                fee: paymentMethod.fee,
                                mop: paymentMethod.mop,
                              ),
                            ),
                          ),
                      ],
                    ),
                  );
                },
              ),
              PaymentMethodsProccessStateError() => const Center(
                child: Text('Error'),
              ),
            },
          );
        },
      ),
    );
  }
}
