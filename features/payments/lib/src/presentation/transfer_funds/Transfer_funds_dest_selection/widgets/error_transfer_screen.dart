import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';
import 'package:payment/src/assets/assets.gen.dart' as assets;

class ErrorTransferScreen extends StatelessWidget {
  const ErrorTransferScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final style = context.duploTextStyles;
    return Scaffold(
      backgroundColor: theme.background.bgPrimary,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Center(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const SizedBox(height: 60),
                assets.Assets.images.transferFundsFailure.svg(),
                const SizedBox(height: 60),
                DuploText(
                  text: "Something went wrong",
                  style: style.textXl,
                  color: theme.text.textPrimary,
                  fontWeight: DuploFontWeight.semiBold,
                ),
                const Sized<PERSON><PERSON>(height: 8),
                Duplo<PERSON>ext(
                  text: "We couldn’t complete the transaction.",
                  style: style.textSm,
                  color: theme.text.textSecondary,
                ),
                Spacer(),
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 24.0),
                  child: DuploButton.secondary(
                    title: "Go back",
                    useFullWidth: true,
                    onTap: () {
                      Navigator.pop(context);
                    },
                  ),
                ),
                const SizedBox(height: 8),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
