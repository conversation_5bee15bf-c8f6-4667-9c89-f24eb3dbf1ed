import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';
import 'package:payment/src/assets/assets.gen.dart' as assets;
import 'package:payment/src/data/account_model.dart';
import 'package:payment/src/di/di_container.dart';
import 'package:payment/src/navigation/payment_navigation.dart';
import 'package:payment/src/presentation/transfer_funds/Transfer_funds_dest_selection/widgets/transfer_funds_success_info_card.dart';

class SuccessTransferScreen extends StatelessWidget {
  final Account sourceAccount;
  final Account destinationAccount;
  final String amount;
  const SuccessTransferScreen({
    super.key,
    required this.sourceAccount,
    required this.destinationAccount,
    required this.amount,
  });

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final style = context.duploTextStyles;
    return Scaffold(
      backgroundColor: theme.background.bgPrimary,
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const SizedBox(height: 20),
                  assets.Assets.images.trasnferFundsSuccess.svg(),
                  const SizedBox(height: 60),
                  DuploText(
                    text: "Transfer successful!",
                    style: style.textXl,
                    color: theme.text.textPrimary,
                    fontWeight: DuploFontWeight.semiBold,
                  ),
                  const SizedBox(height: 24),
                  Container(
                    decoration: BoxDecoration(
                      color: theme.background.bgSecondary,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Column(
                      children: [
                        TransferFundsSuccessInfoCard(
                          title: "Transfer from account",
                          value:
                              "${sourceAccount.platformAccountNumber} - ${_handleAccountType(sourceAccount.accountType)}",
                        ),
                        TransferFundsSuccessInfoCard(
                          title: "Transfer to account",
                          value:
                              "${destinationAccount.platformAccountNumber} - ${_handleAccountType(destinationAccount.accountType)}",
                        ),
                        TransferFundsSuccessInfoCard(
                          title: "Transfer Amount",
                          value: amount,
                        ),
                        TransferFundsSuccessInfoCard(
                          title: "Currency",
                          value: destinationAccount.accountCurrency,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 40),
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 24.0),
                    child: DuploButton.defaultPrimary(
                      title: "Continue",
                      useFullWidth: true,
                      trailingIcon:
                          Assets.images
                              .chevronRightDirectional(context)
                              .keyName,
                      onTap: () {
                        diContainer<PaymentNavigation>().popUntilRoute(
                          "/navBar",
                        );
                      },
                    ),
                  ),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  String _handleAccountType(String accountType) {
    if (accountType == "Trading") {
      return "Account";
    }
    return accountType;
  }
}
