import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';
import 'package:payment/src/data/account_model.dart';

/// Individual account tile component
class AccountTile extends StatelessWidget {
  const AccountTile({
    required this.account,
    required this.isWallet,
    required this.isSelected,
    required this.onTap,
    super.key,
  });

  final Account account;
  final bool isWallet;
  final bool isSelected;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return DuploTap(
      radius: 12,
      key: Key(account.accountCurrency),
      onTap: onTap,
      child: DuploPaymentAccountTile(
        accountInfo: AccountInfo(
          nickName: account.nickName,
          balance: account.balance.toDouble(),
          currency: account.accountCurrency,
          accountNumber: account.platformAccountNumber,
          platform: account.platformType,
          type: account.platformAccountType,
          currencyImage: FlagProvider.getFlagFromCurrencyCode(
            account.accountCurrency,
            width: 16,
            height: 16,
          ),
          isWallet: isWallet,
        ),
        isSelected: isSelected,
      ),
    );
  }
}
