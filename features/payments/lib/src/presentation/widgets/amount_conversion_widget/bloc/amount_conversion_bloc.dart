import 'dart:async';
import 'dart:developer';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:payment/src/data/conversion_rate/conversion_rate_model.dart';
import 'package:payment/src/data/payment_method/payment_method_mix_model.dart';
import 'package:payment/src/domain/data/conversion_type.dart';
import 'package:payment/src/domain/data/payment_type.dart';
import 'package:payment/src/domain/usecase/conversion_rate_usecase.dart';
import 'package:payment/src/presentation/widgets/amount_conversion_widget/services/amount_conversion_service.dart';
import 'package:payment/src/presentation/widgets/amount_conversion_widget/services/amount_validation_service.dart';
import 'package:payment/src/presentation/widgets/amount_conversion_widget/services/currency_service.dart';

part 'amount_conversion_bloc.freezed.dart';
part 'amount_conversion_event.dart';
part 'amount_conversion_state.dart';

/// Bloc responsible for managing amount conversion state
/// Refactored to follow SOLID principles with dependency injection
class AmountConversionBloc
    extends Bloc<AmountConversionEvent, AmountConversionState> {
  AmountConversionBloc({
    required ConversionRateUsecase conversionRateUsecase,
    CurrencyService? currencyService,
    AmountValidationService? validationService,
    AmountConversionService? conversionService,
  }) : _conversionRateUsecase = conversionRateUsecase,
       _currencyService = currencyService ?? const CurrencyServiceImpl(),
       _validationService =
           validationService ?? const AmountValidationServiceImpl(),
       _conversionService =
           conversionService ?? const AmountConversionServiceImpl(),
       super(const AmountConversionState()) {
    on<_GetCurrencyDetails>(_getCurrencyDetails);
    on<_GetConversionRate>(_getConversionRate);
    on<_UpdateTransferAmount>(_updateTransferAmount);
    on<_UpdateConvertedAmount>(_updateConvertedAmount);
  }

  final ConversionRateUsecase _conversionRateUsecase;
  final CurrencyService _currencyService;
  final AmountValidationService _validationService;
  final AmountConversionService _conversionService;

  FutureOr<void> _getCurrencyDetails(
    _GetCurrencyDetails event,
    Emitter<AmountConversionState> emit,
  ) {
    try {
      final currencyDetail = _currencyService.getCurrencyDetails(
        currencyAmountDetails: event.currencyAmountDetails,
        accountCurrency: event.accountCurrency,
      );

      if (event.isStartWithConversionRate) {
        emit(
          state.copyWith(
            homeCurrency: currencyDetail,
            paymentType: event.paymentType,
          ),
        );

        add(
          AmountConversionEvent.getConversionRate(
            accountCurrency: currencyDetail.currency,
            targetCurrency: event.targetCurrency ?? '',
            paymentType: event.paymentType,
            amount: event.amount ?? '0',
            conversionType:
                ConversionType
                    .accountToTargetCurrency, // Default value for initial load
            currencyAmountDetails: event.currencyAmountDetails,
          ),
        );
        emit(
          state.copyWith(
            processState: const AmountConversionProcessState.success(),
          ),
        );
      } else {
        emit(
          state.copyWith(
            homeCurrency: currencyDetail,
            paymentType: event.paymentType,
            processState: const AmountConversionProcessState.success(),
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          processState: AmountConversionProcessState.error(
            errorMessage: e.toString(),
          ),
        ),
      );
    }
  }

  FutureOr<void> _getConversionRate(
    _GetConversionRate event,
    Emitter<AmountConversionState> emit,
  ) async {
    emit(state.copyWith(processState: AmountConversionProcessState.loading()));
    final conversionRate =
        await _conversionRateUsecase(
          sourceCurrency: event.accountCurrency,
          targetCurrency: event.targetCurrency,
          paymentType: event.paymentType,
        ).run();

    conversionRate.fold(
      (l) {
        if (!isClosed) {
          emit(
            state.copyWith(
              processState: AmountConversionProcessState.error(
                errorMessage: l.toString(),
              ),
            ),
          );
        }
        addError(l);
      },
      (data) {
        if (!isClosed) {
          final doesNeedConversion = _currencyService.doesNeedConversion(
            accountCurrency: event.accountCurrency,
            targetCurrency: event.targetCurrency,
          );

          emit(
            state.copyWith(
              conversionRateData: data,
              doesNeedConversion: doesNeedConversion,
              convertedCurrency: event.targetCurrency,
            ),
          );
        }

        if (!isClosed) {
          final currentAmount =
              double.tryParse(event.amount.replaceAll(',', '')) ?? 0;
          add(
            AmountConversionEvent.updateConvertedAmount(
              amount: currentAmount,
              amountCallback: event.amountCallback,
              conversionRateData: data,
              conversionType: event.conversionType,
              accountCurrency: event.accountCurrency,
              targetCurrency: event.targetCurrency,
              currencyAmountDetails: event.currencyAmountDetails,
            ),
          );
        }
      },
    );
  }

  FutureOr<void> _updateConvertedAmount(
    _UpdateConvertedAmount event,
    Emitter<AmountConversionState> emit,
  ) {
    log(
      "inside _updateConvertedAmount and amount is ${event.amount} ${event.accountCurrency} ${event.targetCurrency}",
    );

    // Use conversion service to calculate converted amount
    final conversionResult =
        event.conversionType.isReversedConversion
            ? _conversionService.convertToAccountCurrency(
              amount: event.amount,
              accountCurrency: event.accountCurrency,
              targetCurrency: event.targetCurrency,
              conversionRateData: state.conversionRateData,
            )
            : _conversionService.convertToTargetCurrency(
              amount: event.amount,
              accountCurrency: event.accountCurrency,
              targetCurrency: event.targetCurrency,
              conversionRateData: state.conversionRateData,
            );

    // Use validation service to validate the converted amount
    final validationResult = _validationService.validateAmount(
      amount: conversionResult.convertedAmount,
      targetCurrency: event.targetCurrency,
      currencyAmountDetails: event.currencyAmountDetails,
    );

    emit(
      state.copyWith(
        convertedAmount: conversionResult.convertedAmount,
        isValidAmount: validationResult.isValid,
        validationError: validationResult.error,
        processState: const AmountConversionProcessState.success(),
      ),
    );
    event.amountCallback?.call(conversionResult.convertedAmount);
  }

  FutureOr<void> _updateTransferAmount(
    _UpdateTransferAmount event,
    Emitter<AmountConversionState> emit,
  ) {
    log("inside _updateTransferAmount and amount is ${event.amount}");

    // Use conversion service to calculate transfer amount
    final conversionResult = _conversionService.calculateTransferAmount(
      amount: event.amount,
      accountCurrency: event.accountCurrency,
      targetCurrency: event.targetCurrency,
      conversionRateData: state.conversionRateData,
      isReversedConversion: event.conversionType.isReversedConversion,
    );

    // Use validation service for reversed conversion validation
    AmountValidationResult validationResult = AmountValidationResult.valid;
    if (event.conversionType.isReversedConversion) {
      validationResult = _validationService.validateReversedAmount(
        amount: event.amount,
        targetCurrency: event.targetCurrency,
        currencyAmountDetails: event.currencyAmountDetails,
      );

      log(
        "Validation result for reversed conversion: isValid=${validationResult.isValid}, error=${validationResult.error}",
      );
    }

    emit(
      state.copyWith(
        transferAmount: conversionResult.convertedAmount,
        isValidAmount: validationResult.isValid,
        validationError: validationResult.error,
      ),
    );

    event.amountCallback?.call(conversionResult.convertedAmount);
  }
}
