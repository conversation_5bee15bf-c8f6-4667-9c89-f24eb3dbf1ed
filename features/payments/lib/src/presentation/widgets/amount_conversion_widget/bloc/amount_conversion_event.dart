part of 'amount_conversion_bloc.dart';

@freezed
sealed class AmountConversionEvent with _$AmountConversionEvent {
  const factory AmountConversionEvent.getConversionRate({
    required String accountCurrency,
    required String targetCurrency,
    required PaymentType paymentType,
    required String amount,
    required ConversionType conversionType,
    void Function(double amount)? amountCallback,
    required List<CurrencyAmountDetail> currencyAmountDetails,
  }) = _GetConversionRate;

  const factory AmountConversionEvent.getCurrencyDetails({
    required List<CurrencyAmountDetail> currencyAmountDetails,
    required String accountCurrency,
    required bool isHomeCurrency,
    required bool isStartWithConversionRate,
    required PaymentType paymentType,
    String? targetCurrency,
    String? amount,
  }) = _GetCurrencyDetails;

  const factory AmountConversionEvent.updateTransferAmount({
    required double amount,
    void Function(double amount)? amountCallback,
    ConversionRateModel? conversionRateData,
    required ConversionType conversionType,
    required String accountCurrency,
    required String targetCurrency,
    required List<CurrencyAmountDetail> currencyAmountDetails,
  }) = _UpdateTransferAmount;

  const factory AmountConversionEvent.updateConvertedAmount({
    required double amount,
    void Function(double amount)? amountCallback,
    ConversionRateModel? conversionRateData,
    required ConversionType conversionType,
    required String accountCurrency,
    required String targetCurrency,
    required List<CurrencyAmountDetail> currencyAmountDetails,
  }) = _UpdateConvertedAmount;
}
