part of 'amount_conversion_bloc.dart';

@freezed
sealed class AmountConversionState with _$AmountConversionState {
  const factory AmountConversionState({
    @Default(AmountConversionProcessState.loading())
    AmountConversionProcessState processState,
    ConversionRateModel? conversionRateData,
    String? errorMessage,
    bool? isValidAmount,
    @Default(AmountValidationError.none) AmountValidationError validationError,
    @Default(false) bool doesNeedConversion,
    String? convertedCurrency,
    CurrencyAmountDetail? homeCurrency,
    double? transferAmount,
    double? convertedAmount,
    PaymentType? paymentType,
    String? conversionRateString,
  }) = _AmountConversionState;
}

@freezed
sealed class AmountConversionProcessState with _$AmountConversionProcessState {
  const factory AmountConversionProcessState.loading() =
      AmountConversionLoadingProcessState;
  const factory AmountConversionProcessState.success() =
      AmountConversionSuccessProcessState;
  const factory AmountConversionProcessState.error({String? errorMessage}) =
      AmountConversionErrorProcessState;
}
