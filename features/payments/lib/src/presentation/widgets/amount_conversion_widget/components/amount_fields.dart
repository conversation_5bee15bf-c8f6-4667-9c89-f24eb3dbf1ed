import 'dart:developer';

import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:payment/src/assets/assets.gen.dart' as trader;
import 'package:payment/src/domain/data/conversion_type.dart';
import 'package:payment/src/domain/data/payment_type.dart';
import 'package:payment/src/presentation/widgets/amount_conversion_widget/amount_conversion_widget.dart';
import 'package:payment/src/presentation/widgets/amount_conversion_widget/bloc/amount_conversion_bloc.dart';
import 'package:payment/src/presentation/widgets/amount_conversion_widget/components/thousands_separator_input_formatter.dart';
import 'package:prelude/prelude.dart';

class AmountFields extends StatelessWidget {
  AmountFields({
    super.key,
    required this.transferAmountController,
    required this.convertedAmountController,
    required this.args,
    this.startWithConversionRate = false,
  });

  final TextEditingController transferAmountController;
  final TextEditingController convertedAmountController;
  final AmountConversionArgs args;
  final bool startWithConversionRate;
  String? selectedCurrency;

  /// Builds the transfer amount field
  Widget _buildTransferAmountField(
    BuildContext builderContext,
    AmountConversionState state,
    EquitiLocalization localization,
    String locale,
  ) {
    return Stack(
      children: [
        DuploTextField(
          key: const Key('transfer_amount_field'),
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          label: _returnDefaultFieldLabel(
            paymentType: args.paymentType,
            localization: localization,
            doesNeedConversion: state.doesNeedConversion,
          ),
          hint: _returnDefaultFieldLabel(
            paymentType: args.paymentType,
            localization: localization,
            doesNeedConversion: state.doesNeedConversion,
          ),
          controller: transferAmountController,
          scrollPadding: const EdgeInsets.only(bottom: 100),
          inputFormatters: const [ThousandsSeparatorInputFormatter()],
          onChanged: (amount) {
            // Remove commas before parsing to get the actual numeric value
            final numericValue = amount.replaceAll(',', '');
            builderContext.read<AmountConversionBloc>().add(
              AmountConversionEvent.updateConvertedAmount(
                amount:
                    double.tryParse(
                      numericValue.isNotEmpty ? numericValue : '0',
                    ) ??
                    0,
                amountCallback: (value) {
                  convertedAmountController.text = EquitiFormatter.formatNumber(
                    value: value,
                    locale: locale,
                  );
                  // Callback is handled by the main listener in amount_conversion_widget.dart
                },
                conversionRateData: state.conversionRateData,
                conversionType: args.conversionType,
                accountCurrency: args.transferCurrency,
                targetCurrency:
                    state.convertedCurrency ?? args.transferCurrency,
                currencyAmountDetails: args.currencyAmountDetails,
              ),
            );
          },
        ),
        state.doesNeedConversion
            ? Positioned(
              right: DuploSpacing.spacing_lg_12,
              bottom: DuploSpacing.spacing_md_8,
              child: DuploTagContainer.md(
                leading: FlagProvider.getFlagFromCurrencyCode(
                  args.transferCurrency,
                ),
                text: args.transferCurrency,
              ),
            )
            : Positioned(
              right: DuploSpacing.spacing_lg_12,
              bottom: DuploSpacing.spacing_md_8,
              child: DuploTap(
                key: const Key('transfer_currency_Inkwell'),
                child: DuploTagContainer.md(
                  leading: FlagProvider.getFlagFromCurrencyCode(
                    args.transferCurrency,
                  ),
                  text: args.transferCurrency,
                  trailing:
                      args.paymentType == PaymentType.transfer
                          ? null
                          : trader.Assets.images.chevronDown.svg(),
                ),
                onTap:
                    args.paymentType == PaymentType.transfer
                        ? null
                        : () {
                          DuploDropDown.customBottomSheetSelector(
                            context: builderContext,
                            bottomSheetTitle:
                                localization.payments_selectCurrency,
                            items:
                                args.currencies
                                    .map(
                                      (element) => DropDownItemModel(
                                        title: element,
                                        image:
                                            FlagProvider.getFlagFromCurrencyCode(
                                              element,
                                            ),
                                      ),
                                    )
                                    .toList(),
                            selectedIndex: args.currencies.indexWhere(
                              (element) => element == args.transferCurrency,
                            ),
                            onChanged: (index) {
                              selectedCurrency = args.currencies
                                  .elementAtOrNull(index);

                              builderContext.read<AmountConversionBloc>().add(
                                AmountConversionEvent.getConversionRate(
                                  accountCurrency: args.transferCurrency,
                                  targetCurrency: selectedCurrency ?? '',
                                  paymentType: args.paymentType,
                                  amount: transferAmountController.text
                                      .replaceAll(',', ''),
                                  conversionType: args.conversionType,
                                  amountCallback: (value) {
                                    convertedAmountController
                                        .text = EquitiFormatter.formatNumber(
                                      value: value,
                                      locale: locale,
                                    ).replaceAll(',', "");
                                  },
                                  currencyAmountDetails:
                                      args.currencyAmountDetails,
                                ),
                              );
                            },
                          );
                        },
              ),
            ),
      ],
    );
  }

  /// Builds the converted amount field
  Widget _buildConvertedAmountField(
    BuildContext builderContext,
    AmountConversionState state,
    EquitiLocalization localization,
    String locale,
  ) {
    return Stack(
      children: [
        DuploTextField(
          key: const Key('converted_amount_field'),
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          label: _returnTargetFieldLabel(
            localization: localization,
            paymentType: args.paymentType,
          ),
          hint: _returnTargetFieldLabel(
            localization: localization,
            paymentType: args.paymentType,
          ),
          controller: convertedAmountController,
          inputFormatters: const [ThousandsSeparatorInputFormatter()],
          onChanged: (amount) {
            // Remove commas before parsing to get the actual numeric value
            final numericValue = amount.replaceAll(',', '');
            log("Converted amount: $numericValue");
            builderContext.read<AmountConversionBloc>().add(
              AmountConversionEvent.updateTransferAmount(
                amount:
                    double.tryParse(
                      numericValue.isNotEmpty ? numericValue : '0',
                    ) ??
                    0,
                amountCallback: (value) {
                  transferAmountController.text = EquitiFormatter.formatNumber(
                    value: value,
                    locale: locale,
                  );
                },
                currencyAmountDetails: args.currencyAmountDetails,
                conversionType: args.conversionType,
                accountCurrency: args.transferCurrency,
                targetCurrency: selectedCurrency ?? args.transferCurrency,
              ),
            );
          },
        ),
        Positioned(
          right: 12,
          bottom: 6,
          child: InkWell(
            key: const Key('converted_currency_selector'),
            child: DuploTagContainer.md(
              leading: FlagProvider.getFlagFromCurrencyCode(
                state.convertedCurrency ?? '',
              ),
              text: state.convertedCurrency ?? '',
              trailing:
                  args.paymentType == PaymentType.transfer
                      ? null
                      : trader.Assets.images.chevronDown.svg(),
            ),
            onTap:
                args.paymentType == PaymentType.transfer
                    ? null
                    : () {
                      DuploDropDown.customBottomSheetSelector(
                        context: builderContext,
                        bottomSheetTitle: localization.payments_selectCurrency,
                        items:
                            args.currencies
                                .map(
                                  (element) => DropDownItemModel(
                                    title: element,
                                    image: FlagProvider.getFlagFromCurrencyCode(
                                      element,
                                    ),
                                  ),
                                )
                                .toList(),
                        selectedIndex: args.currencies.indexWhere(
                          (element) => element == state.convertedCurrency,
                        ),
                        onChanged: (index) {
                          selectedCurrency = args.currencies.elementAtOrNull(
                            index,
                          );
                          //added because when we prefill info and then change currency it doesn't fire callback so getting old value
                          transferAmountController.clear();
                          convertedAmountController.clear();

                          builderContext.read<AmountConversionBloc>().add(
                            AmountConversionEvent.getConversionRate(
                              accountCurrency: args.transferCurrency,
                              targetCurrency: selectedCurrency ?? '',
                              paymentType: args.paymentType,
                              amount: transferAmountController.text.replaceAll(
                                ',',
                                '',
                              ),
                              conversionType: args.conversionType,
                              currencyAmountDetails: args.currencyAmountDetails,
                            ),
                          );
                        },
                      );
                    },
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final locale = Localizations.localeOf(context).toString();
    final localization = EquitiLocalization.of(context);

    return BlocBuilder<AmountConversionBloc, AmountConversionState>(
      buildWhen: (previous, current) => previous != current,
      builder: (builderContext, state) {
        // Determine which field should be first and second based on isReversedConversion
        final firstField =
            args.conversionType.isReversedConversion
                ? _buildConvertedAmountField(
                  builderContext,
                  state,
                  localization,
                  locale,
                )
                : _buildTransferAmountField(
                  builderContext,
                  state,
                  localization,
                  locale,
                );

        final secondField =
            args.conversionType.isReversedConversion
                ? _buildTransferAmountField(
                  builderContext,
                  state,
                  localization,
                  locale,
                )
                : _buildConvertedAmountField(
                  builderContext,
                  state,
                  localization,
                  locale,
                );

        return Stack(
          alignment: Alignment.center,
          children: [
            Column(
              children: [
                if (state.doesNeedConversion) ...[
                  firstField,
                  const SizedBox(height: DuploSpacing.spacing_md_8),
                  secondField,
                ] else
                  _buildTransferAmountField(
                    builderContext,
                    state,
                    localization,
                    locale,
                  ),
              ],
            ),
            state.doesNeedConversion
                ? trader.Assets.images.conversionIcon.svg(
                  height: 40,
                  width: 40,
                  matchTextDirection: true,
                )
                : Container(),
          ],
        );
      },
    );
  }

  String _returnTargetFieldLabel({
    required PaymentType paymentType,
    required EquitiLocalization localization,
  }) {
    switch (paymentType) {
      case PaymentType.deposit:
        return localization.payments_toBeDeposited;
      case PaymentType.withdrawal:
        return localization.payments_withdrawalAmount;
      case PaymentType.transfer:
        return localization.payments_transferAmount;
    }
  }

  String _returnDefaultFieldLabel({
    required PaymentType paymentType,
    required EquitiLocalization localization,
    required bool doesNeedConversion,
  }) {
    if (doesNeedConversion) {
      switch (paymentType) {
        case PaymentType.deposit:
          return localization.payments_amount;
        case PaymentType.withdrawal || PaymentType.transfer:
          return localization.payments_convertedAmount;
      }
    } else {
      switch (paymentType) {
        case PaymentType.transfer:
          return localization.payments_transferAmount;
        case PaymentType.deposit:
        case PaymentType.withdrawal:
          return localization.payments_addAmount;
      }
    }
  }
}
