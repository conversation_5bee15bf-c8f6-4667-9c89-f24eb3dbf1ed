import 'dart:developer';

import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:payment/src/data/payment_method/payment_method_mix_model.dart';
import 'package:payment/src/domain/data/conversion_type.dart';
import 'package:payment/src/presentation/widgets/amount_conversion_widget/bloc/amount_conversion_bloc.dart';
import 'package:payment/src/presentation/widgets/amount_conversion_widget/amount_conversion_widget.dart';
import 'package:payment/src/presentation/widgets/amount_conversion_widget/utils/amount_conversion_utils.dart';
import 'package:prelude/prelude.dart';

class SuggestedAmounts extends StatelessWidget {
  const SuggestedAmounts({
    super.key,
    required this.transferAmountController,
    required this.convertedAmountController,
    required this.currencyAmountDetails,
    required this.targetCurrency,
    required this.args,
  });

  final TextEditingController transferAmountController;
  final TextEditingController convertedAmountController;
  final List<CurrencyAmountDetail> currencyAmountDetails;
  final String targetCurrency;
  final AmountConversionArgs args;

  @override
  Widget build(BuildContext context) {
    final locale = Localizations.localeOf(context).toString();
    final selectedCurrencyDetail =
        args.conversionType.isReversedConversion
            ? AmountConversionUtils.getTargetMinAndMaxAmount(
              currencyAmountDetails: currencyAmountDetails,
              targetCurrency: targetCurrency,
            )
            : AmountConversionUtils.getAccountMinAndMaxAmount(
              currencyAmountDetails: currencyAmountDetails,
              accountCurrency: args.transferCurrency,
            );

    return BlocBuilder<AmountConversionBloc, AmountConversionState>(
      buildWhen: (previous, current) => previous != current,
      builder: (builderContext, state) {
        return (selectedCurrencyDetail?.suggestedAmounts?.isNotEmpty ?? false)
            ? _buildSuggestedAmountsLayout(
              builderContext: builderContext,
              state: state,
              locale: locale,
              selectedCurrencyDetail: selectedCurrencyDetail!,
            )
            : Container();
      },
    );
  }

  Widget _buildSuggestedAmountsLayout({
    required BuildContext builderContext,
    required AmountConversionState state,
    required String locale,
    required CurrencyAmountDetail selectedCurrencyDetail,
  }) {
    final suggestedAmounts = selectedCurrencyDetail.suggestedAmounts!;
    final itemCount = suggestedAmounts.length;

    // For 3 or 4 items, use Row with Expanded to fill full width
    if (itemCount == 3) {
      final widgets = <Widget>[];
      for (int i = 0; i < suggestedAmounts.length; i++) {
        widgets.add(
          Expanded(
            child: _buildSuggestedAmountButton(
              context: builderContext,
              state: state,
              index: i,
              locale: locale,
              selectedCurrencyDetail: selectedCurrencyDetail,
            ),
          ),
        );
        // Add spacing between items (but not after the last item)
        if (i < suggestedAmounts.length - 1) {
          widgets.add(const SizedBox(width: DuploSpacing.spacing_md_8));
        }
      }
      return Row(children: widgets);
    }

    // For other cases, use Wrap with center alignment
    return Center(
      child: Wrap(
        alignment: WrapAlignment.center,
        spacing: DuploSpacing.spacing_xl_16,
        runSpacing: DuploSpacing.spacing_md_8,
        children:
            suggestedAmounts
                .asMap()
                .entries
                .map(
                  (entry) => _buildSuggestedAmountButton(
                    context: builderContext,
                    state: state,
                    index: entry.key,
                    locale: locale,
                    selectedCurrencyDetail: selectedCurrencyDetail,
                  ),
                )
                .toList(),
      ),
    );
  }

  Widget _buildSuggestedAmountButton({
    required BuildContext context,
    required AmountConversionState state,
    required int index,
    required String locale,
    required CurrencyAmountDetail selectedCurrencyDetail,
  }) {
    final suggestedAmount = selectedCurrencyDetail.suggestedAmounts
        ?.elementAtOrNull(index);

    if (suggestedAmount == null) {
      return const SizedBox.shrink();
    }

    return FittedBox(
      fit: BoxFit.scaleDown,
      child: DuploTap(
        child: DuploTagContainer.lg(
          text:
              "${EquitiFormatter.formatNumber(value: suggestedAmount, locale: locale)} ${selectedCurrencyDetail.currency}",
        ),
        onTap: () {
          final value = EquitiFormatter.formatNumber(
            value: suggestedAmount,
            locale: locale,
          ).replaceAll(',', "");
          if (args.conversionType.isReversedConversion &&
              state.doesNeedConversion) {
            convertedAmountController.text = value;
          } else {
            transferAmountController.text = value;
          }
          if (!args.conversionType.isReversedConversion &&
              state.doesNeedConversion) {
            context.read<AmountConversionBloc>().add(
              AmountConversionEvent.updateConvertedAmount(
                amount: suggestedAmount.toDouble(),
                amountCallback: (convertedValue) {
                  final formattedAmount = EquitiFormatter.formatNumber(
                    value: convertedValue,
                    locale: locale,
                  ).replaceAll(',', "");
                  if (args.conversionType.isReversedConversion &&
                      state.doesNeedConversion) {
                    transferAmountController.text = formattedAmount;
                  } else {
                    convertedAmountController.text = formattedAmount;
                  }
                },
                conversionRateData: state.conversionRateData,
                conversionType: args.conversionType,
                targetCurrency:
                    state.convertedCurrency ?? args.transferCurrency,
                accountCurrency: args.transferCurrency,
                currencyAmountDetails: args.currencyAmountDetails,
              ),
            );
          } else {
            context.read<AmountConversionBloc>().add(
              AmountConversionEvent.updateTransferAmount(
                amount: suggestedAmount.toDouble(),
                amountCallback: (convertedValue) {
                  final formattedAmount = EquitiFormatter.formatNumber(
                    value: convertedValue,
                    locale: locale,
                  ).replaceAll(',', "");
                  if (args.conversionType.isReversedConversion &&
                      state.doesNeedConversion) {
                    transferAmountController.text = formattedAmount;
                  } else {
                    convertedAmountController.text = formattedAmount;
                  }
                },
                conversionRateData: state.conversionRateData,
                conversionType: args.conversionType,
                targetCurrency:
                    state.convertedCurrency ?? args.transferCurrency,
                accountCurrency: args.transferCurrency,
                currencyAmountDetails: args.currencyAmountDetails,
              ),
            );
          }
        },
      ),
    );
  }
}
