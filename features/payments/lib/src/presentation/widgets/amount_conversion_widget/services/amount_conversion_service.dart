import 'package:payment/src/data/conversion_rate/conversion_rate_model.dart';
import 'package:payment/src/presentation/widgets/amount_conversion_widget/utils/amount_conversion_utils.dart';

/// Service responsible for amount conversion calculations
/// Follows Single Responsibility Principle by handling only conversion logic
abstract class AmountConversionService {
  /// Converts amount from account currency to target currency
  ConversionResult convertToTargetCurrency({
    required double amount,
    required String accountCurrency,
    required String targetCurrency,
    required ConversionRateModel? conversionRateData,
  });

  /// Converts amount from target currency to account currency (reversed)
  ConversionResult convertToAccountCurrency({
    required double amount,
    required String accountCurrency,
    required String targetCurrency,
    required ConversionRateModel? conversionRateData,
  });

  /// Calculates transfer amount based on conversion
  ConversionResult calculateTransferAmount({
    required double amount,
    required String accountCurrency,
    required String targetCurrency,
    required ConversionRateModel? conversionRateData,
    required bool isReversedConversion,
  });
}

/// Result of amount conversion calculation
class ConversionResult {
  const ConversionResult({required this.convertedAmount, required this.rate});

  final double convertedAmount;
  final double rate;
}

/// Implementation of AmountConversionService
class AmountConversionServiceImpl implements AmountConversionService {
  const AmountConversionServiceImpl();

  @override
  ConversionResult convertToTargetCurrency({
    required double amount,
    required String accountCurrency,
    required String targetCurrency,
    required ConversionRateModel? conversionRateData,
  }) {
    final rateModel = AmountConversionUtils.getTargetCurrencyRate(
      conversionRateData: conversionRateData,
      targetCurrency: targetCurrency,
    );

    final rate = rateModel?.rate ?? 1.0;
    final convertedAmount = amount / rate;

    // log("Converting to target: ${amount} / $rate = $convertedAmount");

    return ConversionResult(convertedAmount: convertedAmount, rate: rate);
  }

  @override
  ConversionResult convertToAccountCurrency({
    required double amount,
    required String accountCurrency,
    required String targetCurrency,
    required ConversionRateModel? conversionRateData,
  }) {
    final rateModel = AmountConversionUtils.getAccountCurrencyRate(
      conversionRateData: conversionRateData,
      accountCurrency: accountCurrency,
    );

    final rate = rateModel?.rate ?? 1.0;
    final convertedAmount = amount * rate;

    // log(
    //   "Converting to account: ${NumberFormat().format(amount)} * ${NumberFormat().format(rate)} = ${NumberFormat().format(convertedAmount)}",
    // );

    return ConversionResult(convertedAmount: convertedAmount, rate: rate);
  }

  @override
  ConversionResult calculateTransferAmount({
    required double amount,
    required String accountCurrency,
    required String targetCurrency,
    required ConversionRateModel? conversionRateData,
    required bool isReversedConversion,
  }) {
    final rateModel =
        isReversedConversion
            ? AmountConversionUtils.getAccountCurrencyRate(
              conversionRateData: conversionRateData,
              accountCurrency: accountCurrency,
            )
            : AmountConversionUtils.getTargetCurrencyRate(
              conversionRateData: conversionRateData,
              targetCurrency: targetCurrency,
            );

    final rate = rateModel?.rate ?? 1.0;
    final transferAmount = amount / rate;

    // log(
    //   "Calculating transfer amount: ${NumberFormat().format(amount)} / ${NumberFormat().format(rate)} = ${NumberFormat().format(transferAmount)}",
    // );

    return ConversionResult(convertedAmount: transferAmount, rate: rate);
  }
}
