import 'package:payment/src/data/payment_method/payment_method_mix_model.dart';
import 'package:payment/src/presentation/widgets/amount_conversion_widget/utils/amount_conversion_utils.dart';

/// Service responsible for amount validation
/// Follows Single Responsibility Principle by handling only validation logic
abstract class AmountValidationService {
  /// Validates if the amount is within the allowed range for the target currency
  AmountValidationResult validateAmount({
    required double amount,
    required String targetCurrency,
    required List<CurrencyAmountDetail> currencyAmountDetails,
  });

  /// Validates if the amount is within the allowed range for reversed conversion
  AmountValidationResult validateReversedAmount({
    required double amount,
    required String targetCurrency,
    required List<CurrencyAmountDetail> currencyAmountDetails,
  });
}

/// Validation result for amount validation
class AmountValidationResult {
  const AmountValidationResult({required this.isValid, required this.error});

  final bool isValid;
  final AmountValidationError error;

  static const AmountValidationResult valid = AmountValidationResult(
    isValid: true,
    error: AmountValidationError.none,
  );

  static AmountValidationResult invalid(AmountValidationError error) =>
      AmountValidationResult(isValid: false, error: error);
}

/// Implementation of AmountValidationService
class AmountValidationServiceImpl implements AmountValidationService {
  const AmountValidationServiceImpl();

  @override
  AmountValidationResult validateAmount({
    required double amount,
    required String targetCurrency,
    required List<CurrencyAmountDetail> currencyAmountDetails,
  }) {
    final targetCurrencyDetail = currencyAmountDetails.findByCurrency(
      targetCurrency,
    );

    if (targetCurrencyDetail == null) {
      return AmountValidationResult.valid;
    }

    final minAmount = targetCurrencyDetail.minAmount ?? 0;
    final maxAmount = targetCurrencyDetail.maxAmount ?? 0;

    if (amount < minAmount) {
      return AmountValidationResult.invalid(AmountValidationError.belowMinimum);
    } else if (amount > maxAmount) {
      return AmountValidationResult.invalid(AmountValidationError.aboveMaximum);
    }

    return AmountValidationResult.valid;
  }

  @override
  AmountValidationResult validateReversedAmount({
    required double amount,
    required String targetCurrency,
    required List<CurrencyAmountDetail> currencyAmountDetails,
  }) {
    final targetCurrencyDetail = currencyAmountDetails.findByCurrency(
      targetCurrency,
    );

    if (targetCurrencyDetail == null) {
      return AmountValidationResult.valid;
    }

    final minAmount = targetCurrencyDetail.minAmount ?? 0;
    final maxAmount = targetCurrencyDetail.maxAmount ?? 0;

    if (amount < minAmount) {
      return AmountValidationResult.invalid(AmountValidationError.belowMinimum);
    } else if (amount > maxAmount) {
      return AmountValidationResult.invalid(AmountValidationError.aboveMaximum);
    }

    return AmountValidationResult.valid;
  }
}

/// Enum for amount validation errors
enum AmountValidationError { none, belowMinimum, aboveMaximum }
