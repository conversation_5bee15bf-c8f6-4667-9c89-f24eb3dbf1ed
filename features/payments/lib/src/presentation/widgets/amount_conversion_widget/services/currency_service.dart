import 'package:payment/src/data/payment_method/payment_method_mix_model.dart';

/// Service responsible for currency-related operations
/// Follows Single Responsibility Principle by handling only currency operations
abstract class CurrencyService {
  /// Gets currency details for a specific currency
  CurrencyAmountDetail getCurrencyDetails({
    required List<CurrencyAmountDetail> currencyAmountDetails,
    required String accountCurrency,
  });

  /// Checks if conversion is needed between two currencies
  bool doesNeedConversion({
    required String accountCurrency,
    required String targetCurrency,
  });
}

/// Implementation of CurrencyService
class CurrencyServiceImpl implements CurrencyService {
  const CurrencyServiceImpl();

  @override
  CurrencyAmountDetail getCurrencyDetails({
    required List<CurrencyAmountDetail> currencyAmountDetails,
    required String accountCurrency,
  }) {
    return currencyAmountDetails.firstWhere(
      (detail) => detail.currency == accountCurrency,
      orElse: () => throw Exception('Currency not found: $accountCurrency'),
    );
  }

  @override
  bool doesNeedConversion({
    required String accountCurrency,
    required String targetCurrency,
  }) {
    return accountCurrency != targetCurrency;
  }
}
