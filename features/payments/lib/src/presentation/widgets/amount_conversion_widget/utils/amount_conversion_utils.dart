import 'package:payment/src/data/conversion_rate/conversion_rate_model.dart';
import 'package:payment/src/data/payment_method/payment_method_mix_model.dart';

class AmountConversionUtils {
  const AmountConversionUtils();

  static RatesModel? getTargetCurrencyRate({
    ConversionRateModel? conversionRateData,
    String? targetCurrency,
  }) {
    final defaultCurrencyRate = conversionRateData?.data.rates.firstOrNull;
    final targetCurrencyRate =
        conversionRateData?.data.rates
            .where((rate) => rate.fromCurrency == targetCurrency)
            .firstOrNull ??
        defaultCurrencyRate;
    return targetCurrencyRate;
  }

  static RatesModel? getAccountCurrencyRate({
    ConversionRateModel? conversionRateData,
    String? accountCurrency,
  }) {
    final defaultCurrencyRate = conversionRateData?.data.rates.firstOrNull;
    final accountCurrencyRate =
        conversionRateData?.data.rates
            .where((rate) => rate.fromCurrency == accountCurrency)
            .firstOrNull ??
        defaultCurrencyRate;
    return accountCurrencyRate;
  }

  static String getAccountConversionRateString({
    required ConversionRateModel? conversionRateData,
    required String accountCurrency,
  }) {
    final rateModel = getAccountCurrencyRate(
      conversionRateData: conversionRateData,
      accountCurrency: accountCurrency,
    );

    final conversionRateString =
        rateModel != null
            ? '1 ${rateModel.fromCurrency} = ${rateModel.rate.toStringAsFixed(2)} ${rateModel.toCurrency}'
            : '1 ${accountCurrency} = 1 ${accountCurrency}';

    return conversionRateString;
  }

  static String getTargetConversionRateString({
    ConversionRateModel? conversionRateData,
    required String targetCurrency,
  }) {
    final rateModel = getTargetCurrencyRate(
      conversionRateData: conversionRateData,
      targetCurrency: targetCurrency,
    );

    final conversionRateString =
        rateModel != null
            ? '1 ${rateModel.fromCurrency} = ${rateModel.rate.toStringAsFixed(2)} ${rateModel.toCurrency}'
            : '1 ${targetCurrency} = 1 ${targetCurrency}';

    return conversionRateString;
  }

  static CurrencyAmountDetail? getAccountMinAndMaxAmount({
    required List<CurrencyAmountDetail> currencyAmountDetails,
    required String accountCurrency,
  }) {
    return currencyAmountDetails.findByCurrency(accountCurrency);
  }

  static CurrencyAmountDetail? getTargetMinAndMaxAmount({
    required List<CurrencyAmountDetail> currencyAmountDetails,
    required String targetCurrency,
  }) {
    return currencyAmountDetails.findByCurrency(targetCurrency);
  }
}

/// Extension to find currency details from a list
extension AmountConversionWidgetExtension on List<CurrencyAmountDetail> {
  /// Finds the CurrencyAmountDetail for the specified currency
  /// Returns null if no matching currency is found
  CurrencyAmountDetail? findByCurrency(String currency) {
    try {
      return firstWhere((detail) => detail.currency == currency);
    } catch (e) {
      return null;
    }
  }
}
