import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:payment/src/di/di_container.dart';
import 'package:payment/src/domain/model/transfer_type.dart';
import 'package:payment/src/presentation/widgets/withdraw_fees_display/bloc/withdraw_fees_bloc.dart';

/// Arguments for the WithdrawFeesDisplay widget
typedef WithdrawFeesArgs =
    ({
      String paymentType,
      String accountId,
      String accountCurrency,
      String transactionCurrency,
      num amount,
      num convertedAmount,
      TransferType? transferType,
    });

/// Content strings for the WithdrawFeesDisplay widget
typedef WithdrawFeesContent =
    ({
      String idle,
      String loading,
      String zeroFees,
      String Function(num fees, num total, String currency) nonZeroFees,
    });

/// A widget that displays withdrawal fees information
class WithdrawFeesDisplay extends StatefulWidget {
  const WithdrawFeesDisplay({
    required this.args,
    required this.content,
    required this.onFeesChange,
    super.key,
  });

  final WithdrawFeesArgs args;
  final WithdrawFeesContent content;
  final void Function(WithdrawFeesState) onFeesChange;

  @override
  State<WithdrawFeesDisplay> createState() => _WithdrawFeesDisplayState();
}

class _WithdrawFeesDisplayState extends State<WithdrawFeesDisplay> {
  late WithdrawFeesBloc _bloc;

  @override
  void initState() {
    super.initState();
    _bloc = diContainer<WithdrawFeesBloc>();
    // Calculate fees immediately if amount > 0 and transferType is not none or transferType is null
    if (widget.args.amount > 0) {
      if (widget.args.transferType == null) {
        _triggerFeesCalculation();
      } else if (widget.args.transferType != TransferType.none) {
        _triggerFeesCalculation();
      }
    }
  }

  @override
  void didUpdateWidget(WithdrawFeesDisplay oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Check if any of the arguments that affect fees calculation have changed
    if (_hasArgsChanged(oldWidget.args, widget.args)) {
      _triggerFeesCalculation();
    }
  }

  @override
  void dispose() {
    _bloc.close();
    super.dispose();
  }

  /// Checks if any arguments that affect fees calculation have changed
  bool _hasArgsChanged(WithdrawFeesArgs oldArgs, WithdrawFeesArgs newArgs) {
    return oldArgs.paymentType != newArgs.paymentType ||
        oldArgs.amount != newArgs.amount ||
        oldArgs.accountId != newArgs.accountId ||
        oldArgs.accountCurrency != newArgs.accountCurrency ||
        oldArgs.transactionCurrency != newArgs.transactionCurrency ||
        oldArgs.convertedAmount != newArgs.convertedAmount ||
        oldArgs.transferType != newArgs.transferType;
  }

  /// Triggers the fees calculation event
  void _triggerFeesCalculation() {
    _bloc.add(
      WithdrawFeesEvent.calculateFees(
        paymentType: widget.args.paymentType,
        amount: widget.args.amount,
        accountId: widget.args.accountId,
        accountCurrency: widget.args.accountCurrency,
        transactionCurrency: widget.args.transactionCurrency,
        convertedAmount: widget.args.convertedAmount,
        transferType: widget.args.transferType,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _bloc,
      child: _WithdrawFeesContent(
        args: widget.args,
        content: widget.content,
        onFeesChange: widget.onFeesChange,
      ),
    );
  }
}

class _WithdrawFeesContent extends StatelessWidget {
  const _WithdrawFeesContent({
    required this.args,
    required this.content,
    required this.onFeesChange,
  });

  final WithdrawFeesArgs args;
  final WithdrawFeesContent content;
  final void Function(WithdrawFeesState) onFeesChange;

  @override
  Widget build(BuildContext context) {
    final duploTheme = context.duploTheme;
    final textStyles = context.duploTextStyles;
    return BlocConsumer<WithdrawFeesBloc, WithdrawFeesState>(
      listener: (blocContext, state) {
        onFeesChange(state);
      },
      buildWhen:
          (previous, current) =>
              previous.processState != current.processState ||
              previous.fees != current.fees ||
              previous.amount != current.amount ||
              previous.convertedAmount != current.convertedAmount ||
              previous.transactionCurrency != current.transactionCurrency ||
              previous.currency != current.currency ||
              previous.paymentOption != current.paymentOption,
      builder: (builderContext, state) {
        final ps = state.processState;
        switch (ps) {
          case WithdrawFeesIdleProcessState():
            return DuploAlertMessage.warning(title: content.idle);
          case WithdrawFeesLoadingProcessState():
            return DuploAlertMessage.warning(title: content.loading);
          case WithdrawFeesZeroProcessState():
            return DuploAlertMessage.info(title: content.zeroFees);
          case WithdrawFeesNonZeroProcessState():
            return DuploAlertMessage.warning(
              title: content.nonZeroFees(
                state.fees,
                state.total,
                state.currency,
              ),
            );
          case WithdrawFeesErrorProcessState():
            return DuploAlertMessage.error(
              title: 'Error calculating fees',
              expandable: true,
              children: [
                DuploText(
                  text: ps.errorMessage,
                  style: textStyles.textSm,
                  color: duploTheme.text.textTertiary,
                ),
              ],
            );
        }
      },
    );
  }
}
