import 'package:duplo/duplo.dart';
import 'package:equiti_localization/l10n/gen/equiti_localization.gen.dart';
import 'package:flutter/material.dart';
import 'package:payment/src/assets/assets.gen.dart' as payment;
import 'package:payment/src/domain/data/withdraw_status_type.dart';
import 'package:payment/src/navigation/payment_navigation.dart';

import 'package:payment/src/di/di_container.dart';

class WithdrawStatusScreen extends StatelessWidget {
  const WithdrawStatusScreen._({
    super.key,
    required this.statusType,
    required this.svgAsset,
    this.onButtonPressed,
    this.popUntilRoute,
  });

  final WithdrawStatusType statusType;
  final Widget svgAsset;
  final VoidCallback? onButtonPressed;
  final String? popUntilRoute;

  factory WithdrawStatusScreen.submitted({
    Key? key,
    VoidCallback? onButtonPressed,
    String? popUntilRoute,
  }) {
    return WithdrawStatusScreen._(
      key: key,
      statusType: WithdrawStatusType.newStatus,
      svgAsset: payment.Assets.images.withdrawalRequestSubmitted.svg(),
      onButtonPressed: onButtonPressed,
      popUntilRoute: popUntilRoute,
    );
  }

  factory WithdrawStatusScreen.successful({
    Key? key,
    VoidCallback? onButtonPressed,
    String? popUntilRoute,
  }) {
    return WithdrawStatusScreen._(
      key: key,
      statusType: WithdrawStatusType.successful,
      svgAsset: payment.Assets.images.depositSuccess.svg(),
      onButtonPressed: onButtonPressed,
      popUntilRoute: popUntilRoute,
    );
  }

  factory WithdrawStatusScreen.rejected({
    Key? key,
    VoidCallback? onButtonPressed,
    String? popUntilRoute,
  }) {
    return WithdrawStatusScreen._(
      key: key,
      statusType: WithdrawStatusType.rejected,
      svgAsset: payment.Assets.images.depositDeclined.svg(),
      onButtonPressed: onButtonPressed,
      popUntilRoute: popUntilRoute,
    );
  }
  factory WithdrawStatusScreen.error({
    Key? key,
    VoidCallback? onButtonPressed,
    String? popUntilRoute,
  }) {
    return WithdrawStatusScreen._(
      key: key,
      statusType: WithdrawStatusType.error,
      svgAsset: payment.Assets.images.withdrawalErrorStatus.svg(),
      onButtonPressed: onButtonPressed,
      popUntilRoute: popUntilRoute,
    );
  }

  factory WithdrawStatusScreen.fromWithDrawStatus({
    Key? key,
    WithdrawStatusType? statusType,
    VoidCallback? onButtonPressed,
    String? popUntilRoute,
  }) {
    switch (statusType) {
      case WithdrawStatusType.newStatus:
      case WithdrawStatusType.pendingApproval:
        return WithdrawStatusScreen.submitted(
          key: key,
          onButtonPressed: onButtonPressed,
          popUntilRoute: popUntilRoute,
        );
      case WithdrawStatusType.successful:
        return WithdrawStatusScreen.successful(
          key: key,
          onButtonPressed: onButtonPressed,
          popUntilRoute: popUntilRoute,
        );
      case WithdrawStatusType.rejected:
        return WithdrawStatusScreen.rejected(
          key: key,
          onButtonPressed: onButtonPressed,
          popUntilRoute: popUntilRoute,
        );
      case WithdrawStatusType.awaitingDocuments:
        throw Exception('Invalid status type');
      default:
        return WithdrawStatusScreen.error(
          key: key,
          onButtonPressed: onButtonPressed,
          popUntilRoute: popUntilRoute,
        );
    }
  }

  WithdrawStatusContent _getStatusContent(EquitiLocalization localization) {
    switch (statusType) {
      case WithdrawStatusType.newStatus:
      case WithdrawStatusType.pendingApproval:
        return WithdrawStatusContent(
          title: localization.payments_withdrawal_request_submitted,
          subtitle:
              localization.payments_withdrawal_request_submitted_description,
          buttonText: localization.payments_continue,
        );
      case WithdrawStatusType.successful:
        return WithdrawStatusContent(
          title: localization.payments_withdrawal_successful,
          subtitle: localization.payments_withdrawal_successful_description,
          buttonText: localization.payments_continue,
        );
      case WithdrawStatusType.rejected:
        return WithdrawStatusContent(
          title: localization.payments_withdrawal_rejected,
          subtitle: localization.payments_withdrawal_rejected_description,
          buttonText: localization.payments_tryAgain,
        );
      case WithdrawStatusType.error:
        return WithdrawStatusContent(
          title: localization.payments_withdrawal_error,
          subtitle: localization.payments_withdrawal_error_description,
          buttonText: localization.payments_tryAgain,
        );
      case WithdrawStatusType.awaitingDocuments:
        throw Exception('Invalid status type');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final textStyle = context.duploTextStyles;
    final localization = EquitiLocalization.of(context);

    final content = _getStatusContent(localization);

    return Scaffold(
      backgroundColor: theme.background.bgPrimary,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(DuploSpacing.spacing_xl_16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(
                      vertical: DuploSpacing.spacing_7xl_64,
                    ),
                    child: svgAsset,
                  ),
                  DuploText(
                    text: content.title,
                    style: textStyle.textXl,
                    fontWeight: DuploFontWeight.semiBold,
                    color: theme.text.textPrimary,
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: DuploSpacing.spacing_md_8),
                  DuploText(
                    text: content.subtitle,
                    style: textStyle.textSm,
                    fontWeight: DuploFontWeight.regular,
                    textAlign: TextAlign.center,
                    color: theme.text.textSecondary,
                  ),
                ],
              ),
              DuploButton.defaultPrimary(
                useFullWidth: true,
                title: content.buttonText,
                trailingIcon: payment.Assets.images.arrowRightIc.keyName,
                onTap: () {
                  if (onButtonPressed != null) {
                    onButtonPressed!();
                    return;
                  }

                  // Use the new navigation method if popUntilRoute is provided
                  if (popUntilRoute != null) {
                    diContainer<PaymentNavigation>().popUntilRoute(
                      popUntilRoute!,
                    );
                    return;
                  }

                  // Fallback to original behavior
                  if (statusType == WithdrawStatusType.error) {
                    Navigator.of(context).pop();
                  } else {
                    Navigator.of(context).pop();
                    Navigator.of(context).pop();
                  }
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class WithdrawStatusContent {
  const WithdrawStatusContent({
    required this.title,
    required this.subtitle,
    required this.buttonText,
  });

  final String title;
  final String subtitle;
  final String buttonText;
}
