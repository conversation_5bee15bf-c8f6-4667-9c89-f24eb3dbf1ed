import 'dart:async';
import 'dart:developer';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:payment/src/data/account_model.dart';
import 'package:payment/src/data/conversion_rate/conversion_rate_model.dart';
import 'package:payment/src/data/withdraw_card_model/withdraw_card_model.dart';
import 'package:payment/src/data/withdraw_fees_response_model/withdraw_fees_response_model.dart';
import 'package:payment/src/data/withdraw_request_model/withdraw_request_model.dart';
import 'package:payment/src/data/withdraw_response_model/withdraw_response_model.dart';
import 'package:payment/src/domain/data/withdrawal_mop_types.dart';
import 'package:payment/src/domain/data/withdraw_status_type.dart';
import 'package:payment/src/domain/model/withdraw_card_params/withdraw_card_params.dart';
import 'package:payment/src/domain/usecase/get_withdrawal_fees_usecase.dart';
import 'package:payment/src/domain/usecase/withdrawal_usecase.dart';
import 'package:payment/src/navigation/arguments/withdraw_bank_transfer_arguments.dart';
import 'package:payment/src/navigation/payment_navigation.dart';

part 'withdraw_accounts_and_amount_bloc.freezed.dart';
part 'withdraw_accounts_and_amount_event.dart';
part 'withdraw_accounts_and_amount_state.dart';

class WithdrawAccountsAndAmountBloc
    extends
        Bloc<WithdrawAccountsAndAmountEvent, WithdrawAccountsAndAmountState> {
  WithdrawAccountsAndAmountBloc(
    this._paymentNavigation,
    this._withdrawalUseCase,
    this._getWithdrawalFeesUsecase,
  ) : super(const WithdrawAccountsAndAmountState()) {
    on<_OnAccountChange>(_onAccountChange);
    on<_SetSelectedCard>(_setSelectedCard);
    on<_ChangeButtonState>(_changeButtonState);
    on<_CalculateFees>(_calculateFees);
    on<_SetPaymentMethod>(_setPaymentMethod);
    on<_HandleContinueAction>(_handleContinueAction);
  }

  final PaymentNavigation _paymentNavigation;
  final WithdrawalUseCase _withdrawalUseCase;
  final GetWithdrawalFeesUsecase _getWithdrawalFeesUsecase;

  FutureOr<void> _onAccountChange(
    _OnAccountChange event,
    Emitter<WithdrawAccountsAndAmountState> emit,
  ) {
    // Validate withdrawal amount with new account balance
    final validationResult = _validateWithdrawalAmount(
      state.amount,
      event.account.balance,
      state.fees?.fees ?? 0,
    );

    emit(
      state.copyWith(
        selectedAccount: event.account,
        hasInsufficientFunds: validationResult.hasInsufficientFunds,
        insufficientFundsMessage: validationResult.errorMessage,
        isButtonEnabled:
            state.isButtonEnabled && !validationResult.hasInsufficientFunds,
      ),
    );
  }

  FutureOr<void> _setSelectedCard(
    _SetSelectedCard event,
    Emitter<WithdrawAccountsAndAmountState> emit,
  ) {
    emit(state.copyWith(selectedCard: event.selectedCard));
  }

  FutureOr<void> _setPaymentMethod(
    _SetPaymentMethod event,
    Emitter<WithdrawAccountsAndAmountState> emit,
  ) {
    emit(state.copyWith(paymentMop: event.paymentMop));
  }

  void _handleContinueAction(
    _HandleContinueAction event,
    Emitter<WithdrawAccountsAndAmountState> emit,
  ) async {
    switch (event.mop) {
      case WithdrawalMopWrapper(:final mop):
        switch (mop) {
          case WithdrawalMop.skrill || WithdrawalMop.neteller:
            if (event.account != null) {
              // Call repository for Skrill/Neteller withdrawal
              await _submitWithdrawalRequest(mop, emit, account: event.account);
            } else {
              log('No account selected for Skrill/Neteller');
              addError(Exception('No account selected for Skrill/Neteller'));
            }
            break;
          case WithdrawalMop.cards:
            if (state.selectedCard != null) {
              // Call repository for card withdrawal
              await _submitWithdrawalRequest(mop, emit);
            } else {
              log('No card selected for card withdrawal');
              addError(Exception('No card selected for card withdrawal'));
            }
            break;
          case WithdrawalMop.bank:
            // Navigate to bank transfer screen
            final withdrawCardRequestModel = WithdrawCardParams(
              tradingAccountId: state.selectedAccount!.accountId,
              paymentType: mop,
              currency: state.targetCurrency,
              conversionRateString: state.conversionRateString,
              accountCurrency: state.selectedAccount!.accountCurrency,
              amount: double.tryParse(state.amount) ?? 0,
              convertedAmount: double.tryParse(state.convertedAmount) ?? 0,
              conversionRate: state.ratesModel?.rate ?? 1,
            );
            _paymentNavigation.goToWithdrawBankTransferScreen(
              WithdrawBankTransferArguments(
                bankTransferAmountModel: withdrawCardRequestModel,
                tradingAccountId: state.selectedAccount?.accountId ?? "",
              ),
            );
            break;
        }
      default:
        throw UnimplementedError('Invalid MOP type');
    }
  }

  Future<void> _submitWithdrawalRequest(
    WithdrawalMop mop,
    Emitter<WithdrawAccountsAndAmountState> emit, {
    String? account,
  }) async {
    emit(state.copyWith(isLoading: true));

    // Create metadata based on MOP type
    Metadata metadata;
    switch (mop) {
      case WithdrawalMop.skrill:
        metadata = Metadata.skrill(skrill: Skrill(email: account!));
        break;
      case WithdrawalMop.neteller:
        metadata = Metadata.neteller(neteller: Neteller(email: account!));
        break;
      case WithdrawalMop.cards:
        metadata = Metadata.card(card: Card(id: state.selectedCard!.id));
        break;
      case WithdrawalMop.bank:
        // This shouldn't be called for bank transfers as they have different flow
        throw UnimplementedError('Bank transfers should use different flow');
    }
    String paymentType;
    switch (mop) {
      case WithdrawalMop.cards:
        paymentType = state.selectedCard!.mop.name;
        break;
      default:
        paymentType = mop.name;
        break;
    }

    final withdrawalRes =
        await _withdrawalUseCase(
          request: WithdrawRequestModel(
            paymentType: paymentType,
            accountInfo: AccountInfo(
              tradingAccountId: state.selectedAccount!.accountId,
              accountCurrency: state.selectedAccount!.accountCurrency,
            ),
            amount: Amount(
              amount: double.tryParse(state.amount) ?? 0,
              conversionRate: state.ratesModel?.rate ?? 1,
              conversionRateString: state.conversionRateString,
              convertedAmount: double.tryParse(state.convertedAmount) ?? 0,
              currency: state.targetCurrency,
            ),
            metadata: metadata,
          ),
        ).run();

    if (isClosed) return;

    withdrawalRes.fold(
      (err) {
        emit(state.copyWith(isLoading: false));
        _paymentNavigation.goToWithdrawStatusScreen(
          status: WithdrawStatusType.error,
          replace: false,
        );
      },
      (WithdrawResponseModel res) {
        emit(state.copyWith(isLoading: false));
        switch (res) {
          case WithdrawSuccessResponse _:
            _paymentNavigation.goToWithdrawStatusScreen(
              status: res.data.status,
              replace: true,
              popUntilRoute: "/navBar",
            );
            break;
          case WithdrawInternalServerErrorResponse _:
            _paymentNavigation.goToWithdrawStatusScreen(
              status: WithdrawStatusType.error,
              replace: false,
            );
            break;
        }
      },
    );
  }

  void _calculateFees(
    _CalculateFees event,
    Emitter<WithdrawAccountsAndAmountState> emit,
  ) async {
    emit(state.copyWith(fees: Fees(isFeesLoading: true)));

    final feesRes =
        await _getWithdrawalFeesUsecase(
          paymentType: event.mop.mop.name,
          accountId: state.selectedAccount!.accountId,
          accountCurrency: state.selectedAccount!.accountCurrency,
          amount: double.tryParse(state.amount) ?? 0,
          convertedAmount: double.tryParse(state.convertedAmount) ?? 0,
          transactionCurrency: state.selectedAccount!.accountCurrency,
        ).run();

    if (isClosed) return;

    feesRes.fold(
      (err) {
        // If fee calculation fails, validate without fees
        final validationResult = _validateWithdrawalAmount(
          state.amount,
          state.selectedAccount?.balance ?? 0,
          0, // No fees since calculation failed
        );

        emit(
          state.copyWith(
            fees: Fees(isFeesLoading: false, errorMessage: err.toString()),
            hasInsufficientFunds: validationResult.hasInsufficientFunds,
            insufficientFundsMessage: validationResult.errorMessage,
            isButtonEnabled: !validationResult.hasInsufficientFunds,
          ),
        );
      },
      (WithdrawFeesData feesData) {
        // Update fees first
        final newFees = Fees(
          fees: feesData.fee,
          feesCurrency: feesData.currency,
          isFeesLoading: false,
        );

        // Validate withdrawal amount with new fees
        final validationResult = _validateWithdrawalAmount(
          state.amount,
          state.selectedAccount?.balance ?? 0,
          feesData.fee,
        );

        emit(
          state.copyWith(
            fees: newFees,
            hasInsufficientFunds: validationResult.hasInsufficientFunds,
            insufficientFundsMessage: validationResult.errorMessage,
            isButtonEnabled: !validationResult.hasInsufficientFunds,
          ),
        );
      },
    );
  }

  FutureOr<void> _changeButtonState(
    _ChangeButtonState event,
    Emitter<WithdrawAccountsAndAmountState> emit,
  ) {
    // Update amount and related fields first
    emit(
      state.copyWith(
        amount: event.amount,
        convertedAmount: event.convertedAmount,
        ratesModel: event.ratesModel,
        conversionRateString:
            event.conversionRateString ??
            '${event.amount} ${state.selectedAccount!.accountCurrency} = ${event.convertedAmount} ${state.selectedAccount!.accountCurrency}',
        targetCurrency:
            event.targetCurrency ?? state.selectedAccount!.accountCurrency,
      ),
    );

    // Trigger fee calculation if amount is valid and > 0
    final amount = double.tryParse(event.amount) ?? 0;
    if (event.isValid && amount > 0 && state.selectedAccount != null) {
      // Set fees loading state and disable button
      emit(
        state.copyWith(
          fees: Fees(isFeesLoading: true),
          isButtonEnabled: false, // Disable button while calculating fees
          hasInsufficientFunds: false, // Clear previous validation
          insufficientFundsMessage: '',
        ),
      );

      // Calculate fees only if we have payment method information
      if (state.paymentMop != null) {
        add(
          WithdrawAccountsAndAmountEvent.calculateFees(mop: state.paymentMop!),
        );
      } else {
        // If no payment method, validate immediately without fees
        final validationResult = _validateWithdrawalAmount(
          event.amount,
          state.selectedAccount?.balance ?? 0,
          0, // No fees available
        );

        emit(
          state.copyWith(
            isButtonEnabled: !validationResult.hasInsufficientFunds,
            hasInsufficientFunds: validationResult.hasInsufficientFunds,
            insufficientFundsMessage: validationResult.errorMessage,
            fees: null, // Clear fees since we can't calculate them
          ),
        );
      }
    } else {
      // If amount is invalid or 0, validate immediately without fees
      final validationResult = _validateWithdrawalAmount(
        event.amount,
        state.selectedAccount?.balance ?? 0,
        0, // No fees for invalid amounts
      );

      emit(
        state.copyWith(
          isButtonEnabled:
              event.isValid && !validationResult.hasInsufficientFunds,
          hasInsufficientFunds: validationResult.hasInsufficientFunds,
          insufficientFundsMessage: validationResult.errorMessage,
          fees: amount <= 0 ? null : state.fees, // Clear fees if amount is 0
        ),
      );
    }
  }

  /// Validates if the withdrawal amount plus fees exceeds the account balance
  ({bool hasInsufficientFunds, String errorMessage}) _validateWithdrawalAmount(
    String amountString,
    num accountBalance,
    num withdrawalFees,
  ) {
    // Parse the amount string to a number
    final amount = double.tryParse(amountString) ?? 0.0;

    // Skip validation if amount is 0 or empty
    if (amount <= 0) {
      return (hasInsufficientFunds: false, errorMessage: '');
    }

    // Calculate total required amount (withdrawal amount + fees)
    final totalRequired = amount + withdrawalFees;

    // Check if total required exceeds account balance
    if (totalRequired > accountBalance) {
      final currency = state.selectedAccount?.accountCurrency ?? '';
      final formattedBalance = accountBalance.toStringAsFixed(2);
      final formattedTotal = totalRequired.toStringAsFixed(2);

      String errorMessage;
      if (withdrawalFees > 0) {
        final formattedFees = withdrawalFees.toStringAsFixed(2);
        errorMessage =
            'Insufficient funds. Available balance: $formattedBalance $currency, Required: $formattedTotal $currency (including $formattedFees $currency fee)';
      } else {
        errorMessage =
            'Insufficient funds. Available balance: $formattedBalance $currency, Required: $formattedTotal $currency';
      }

      return (hasInsufficientFunds: true, errorMessage: errorMessage);
    }

    return (hasInsufficientFunds: false, errorMessage: '');
  }
}
