// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'withdraw_accounts_and_amount_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$WithdrawAccountsAndAmountEvent {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WithdrawAccountsAndAmountEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'WithdrawAccountsAndAmountEvent()';
}


}

/// @nodoc
class $WithdrawAccountsAndAmountEventCopyWith<$Res>  {
$WithdrawAccountsAndAmountEventCopyWith(WithdrawAccountsAndAmountEvent _, $Res Function(WithdrawAccountsAndAmountEvent) __);
}


/// @nodoc


class _OnAccountChange implements WithdrawAccountsAndAmountEvent {
  const _OnAccountChange(this.account);
  

 final  Account account;

/// Create a copy of WithdrawAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnAccountChangeCopyWith<_OnAccountChange> get copyWith => __$OnAccountChangeCopyWithImpl<_OnAccountChange>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnAccountChange&&(identical(other.account, account) || other.account == account));
}


@override
int get hashCode => Object.hash(runtimeType,account);

@override
String toString() {
  return 'WithdrawAccountsAndAmountEvent.onAccountChange(account: $account)';
}


}

/// @nodoc
abstract mixin class _$OnAccountChangeCopyWith<$Res> implements $WithdrawAccountsAndAmountEventCopyWith<$Res> {
  factory _$OnAccountChangeCopyWith(_OnAccountChange value, $Res Function(_OnAccountChange) _then) = __$OnAccountChangeCopyWithImpl;
@useResult
$Res call({
 Account account
});


$AccountCopyWith<$Res> get account;

}
/// @nodoc
class __$OnAccountChangeCopyWithImpl<$Res>
    implements _$OnAccountChangeCopyWith<$Res> {
  __$OnAccountChangeCopyWithImpl(this._self, this._then);

  final _OnAccountChange _self;
  final $Res Function(_OnAccountChange) _then;

/// Create a copy of WithdrawAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? account = null,}) {
  return _then(_OnAccountChange(
null == account ? _self.account : account // ignore: cast_nullable_to_non_nullable
as Account,
  ));
}

/// Create a copy of WithdrawAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AccountCopyWith<$Res> get account {
  
  return $AccountCopyWith<$Res>(_self.account, (value) {
    return _then(_self.copyWith(account: value));
  });
}
}

/// @nodoc


class _SetSelectedCard implements WithdrawAccountsAndAmountEvent {
  const _SetSelectedCard(this.selectedCard);
  

 final  WithdrawCard? selectedCard;

/// Create a copy of WithdrawAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SetSelectedCardCopyWith<_SetSelectedCard> get copyWith => __$SetSelectedCardCopyWithImpl<_SetSelectedCard>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SetSelectedCard&&(identical(other.selectedCard, selectedCard) || other.selectedCard == selectedCard));
}


@override
int get hashCode => Object.hash(runtimeType,selectedCard);

@override
String toString() {
  return 'WithdrawAccountsAndAmountEvent.setSelectedCard(selectedCard: $selectedCard)';
}


}

/// @nodoc
abstract mixin class _$SetSelectedCardCopyWith<$Res> implements $WithdrawAccountsAndAmountEventCopyWith<$Res> {
  factory _$SetSelectedCardCopyWith(_SetSelectedCard value, $Res Function(_SetSelectedCard) _then) = __$SetSelectedCardCopyWithImpl;
@useResult
$Res call({
 WithdrawCard? selectedCard
});


$WithdrawCardCopyWith<$Res>? get selectedCard;

}
/// @nodoc
class __$SetSelectedCardCopyWithImpl<$Res>
    implements _$SetSelectedCardCopyWith<$Res> {
  __$SetSelectedCardCopyWithImpl(this._self, this._then);

  final _SetSelectedCard _self;
  final $Res Function(_SetSelectedCard) _then;

/// Create a copy of WithdrawAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? selectedCard = freezed,}) {
  return _then(_SetSelectedCard(
freezed == selectedCard ? _self.selectedCard : selectedCard // ignore: cast_nullable_to_non_nullable
as WithdrawCard?,
  ));
}

/// Create a copy of WithdrawAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$WithdrawCardCopyWith<$Res>? get selectedCard {
    if (_self.selectedCard == null) {
    return null;
  }

  return $WithdrawCardCopyWith<$Res>(_self.selectedCard!, (value) {
    return _then(_self.copyWith(selectedCard: value));
  });
}
}

/// @nodoc


class _ChangeButtonState implements WithdrawAccountsAndAmountEvent {
  const _ChangeButtonState({required this.isValid, required this.amount, required this.convertedAmount, required this.ratesModel, this.conversionRateString, this.targetCurrency});
  

 final  bool isValid;
 final  String amount;
 final  String convertedAmount;
 final  RatesModel? ratesModel;
 final  String? conversionRateString;
 final  String? targetCurrency;

/// Create a copy of WithdrawAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ChangeButtonStateCopyWith<_ChangeButtonState> get copyWith => __$ChangeButtonStateCopyWithImpl<_ChangeButtonState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ChangeButtonState&&(identical(other.isValid, isValid) || other.isValid == isValid)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.convertedAmount, convertedAmount) || other.convertedAmount == convertedAmount)&&(identical(other.ratesModel, ratesModel) || other.ratesModel == ratesModel)&&(identical(other.conversionRateString, conversionRateString) || other.conversionRateString == conversionRateString)&&(identical(other.targetCurrency, targetCurrency) || other.targetCurrency == targetCurrency));
}


@override
int get hashCode => Object.hash(runtimeType,isValid,amount,convertedAmount,ratesModel,conversionRateString,targetCurrency);

@override
String toString() {
  return 'WithdrawAccountsAndAmountEvent.changeButtonState(isValid: $isValid, amount: $amount, convertedAmount: $convertedAmount, ratesModel: $ratesModel, conversionRateString: $conversionRateString, targetCurrency: $targetCurrency)';
}


}

/// @nodoc
abstract mixin class _$ChangeButtonStateCopyWith<$Res> implements $WithdrawAccountsAndAmountEventCopyWith<$Res> {
  factory _$ChangeButtonStateCopyWith(_ChangeButtonState value, $Res Function(_ChangeButtonState) _then) = __$ChangeButtonStateCopyWithImpl;
@useResult
$Res call({
 bool isValid, String amount, String convertedAmount, RatesModel? ratesModel, String? conversionRateString, String? targetCurrency
});


$RatesModelCopyWith<$Res>? get ratesModel;

}
/// @nodoc
class __$ChangeButtonStateCopyWithImpl<$Res>
    implements _$ChangeButtonStateCopyWith<$Res> {
  __$ChangeButtonStateCopyWithImpl(this._self, this._then);

  final _ChangeButtonState _self;
  final $Res Function(_ChangeButtonState) _then;

/// Create a copy of WithdrawAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? isValid = null,Object? amount = null,Object? convertedAmount = null,Object? ratesModel = freezed,Object? conversionRateString = freezed,Object? targetCurrency = freezed,}) {
  return _then(_ChangeButtonState(
isValid: null == isValid ? _self.isValid : isValid // ignore: cast_nullable_to_non_nullable
as bool,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as String,convertedAmount: null == convertedAmount ? _self.convertedAmount : convertedAmount // ignore: cast_nullable_to_non_nullable
as String,ratesModel: freezed == ratesModel ? _self.ratesModel : ratesModel // ignore: cast_nullable_to_non_nullable
as RatesModel?,conversionRateString: freezed == conversionRateString ? _self.conversionRateString : conversionRateString // ignore: cast_nullable_to_non_nullable
as String?,targetCurrency: freezed == targetCurrency ? _self.targetCurrency : targetCurrency // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

/// Create a copy of WithdrawAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$RatesModelCopyWith<$Res>? get ratesModel {
    if (_self.ratesModel == null) {
    return null;
  }

  return $RatesModelCopyWith<$Res>(_self.ratesModel!, (value) {
    return _then(_self.copyWith(ratesModel: value));
  });
}
}

/// @nodoc


class _CalculateFees implements WithdrawAccountsAndAmountEvent {
  const _CalculateFees({required this.mop});
  

 final  WithdrawalMopTypes mop;

/// Create a copy of WithdrawAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CalculateFeesCopyWith<_CalculateFees> get copyWith => __$CalculateFeesCopyWithImpl<_CalculateFees>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CalculateFees&&(identical(other.mop, mop) || other.mop == mop));
}


@override
int get hashCode => Object.hash(runtimeType,mop);

@override
String toString() {
  return 'WithdrawAccountsAndAmountEvent.calculateFees(mop: $mop)';
}


}

/// @nodoc
abstract mixin class _$CalculateFeesCopyWith<$Res> implements $WithdrawAccountsAndAmountEventCopyWith<$Res> {
  factory _$CalculateFeesCopyWith(_CalculateFees value, $Res Function(_CalculateFees) _then) = __$CalculateFeesCopyWithImpl;
@useResult
$Res call({
 WithdrawalMopTypes mop
});


$WithdrawalMopTypesCopyWith<$Res> get mop;

}
/// @nodoc
class __$CalculateFeesCopyWithImpl<$Res>
    implements _$CalculateFeesCopyWith<$Res> {
  __$CalculateFeesCopyWithImpl(this._self, this._then);

  final _CalculateFees _self;
  final $Res Function(_CalculateFees) _then;

/// Create a copy of WithdrawAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? mop = null,}) {
  return _then(_CalculateFees(
mop: null == mop ? _self.mop : mop // ignore: cast_nullable_to_non_nullable
as WithdrawalMopTypes,
  ));
}

/// Create a copy of WithdrawAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$WithdrawalMopTypesCopyWith<$Res> get mop {
  
  return $WithdrawalMopTypesCopyWith<$Res>(_self.mop, (value) {
    return _then(_self.copyWith(mop: value));
  });
}
}

/// @nodoc


class _SetPaymentMethod implements WithdrawAccountsAndAmountEvent {
  const _SetPaymentMethod({required this.paymentMop});
  

 final  WithdrawalMopTypes paymentMop;

/// Create a copy of WithdrawAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SetPaymentMethodCopyWith<_SetPaymentMethod> get copyWith => __$SetPaymentMethodCopyWithImpl<_SetPaymentMethod>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SetPaymentMethod&&(identical(other.paymentMop, paymentMop) || other.paymentMop == paymentMop));
}


@override
int get hashCode => Object.hash(runtimeType,paymentMop);

@override
String toString() {
  return 'WithdrawAccountsAndAmountEvent.setPaymentMethod(paymentMop: $paymentMop)';
}


}

/// @nodoc
abstract mixin class _$SetPaymentMethodCopyWith<$Res> implements $WithdrawAccountsAndAmountEventCopyWith<$Res> {
  factory _$SetPaymentMethodCopyWith(_SetPaymentMethod value, $Res Function(_SetPaymentMethod) _then) = __$SetPaymentMethodCopyWithImpl;
@useResult
$Res call({
 WithdrawalMopTypes paymentMop
});


$WithdrawalMopTypesCopyWith<$Res> get paymentMop;

}
/// @nodoc
class __$SetPaymentMethodCopyWithImpl<$Res>
    implements _$SetPaymentMethodCopyWith<$Res> {
  __$SetPaymentMethodCopyWithImpl(this._self, this._then);

  final _SetPaymentMethod _self;
  final $Res Function(_SetPaymentMethod) _then;

/// Create a copy of WithdrawAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? paymentMop = null,}) {
  return _then(_SetPaymentMethod(
paymentMop: null == paymentMop ? _self.paymentMop : paymentMop // ignore: cast_nullable_to_non_nullable
as WithdrawalMopTypes,
  ));
}

/// Create a copy of WithdrawAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$WithdrawalMopTypesCopyWith<$Res> get paymentMop {
  
  return $WithdrawalMopTypesCopyWith<$Res>(_self.paymentMop, (value) {
    return _then(_self.copyWith(paymentMop: value));
  });
}
}

/// @nodoc


class _HandleContinueAction implements WithdrawAccountsAndAmountEvent {
  const _HandleContinueAction({required this.mop, this.account});
  

 final  WithdrawalMopTypes mop;
 final  String? account;

/// Create a copy of WithdrawAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$HandleContinueActionCopyWith<_HandleContinueAction> get copyWith => __$HandleContinueActionCopyWithImpl<_HandleContinueAction>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _HandleContinueAction&&(identical(other.mop, mop) || other.mop == mop)&&(identical(other.account, account) || other.account == account));
}


@override
int get hashCode => Object.hash(runtimeType,mop,account);

@override
String toString() {
  return 'WithdrawAccountsAndAmountEvent.handleContinueAction(mop: $mop, account: $account)';
}


}

/// @nodoc
abstract mixin class _$HandleContinueActionCopyWith<$Res> implements $WithdrawAccountsAndAmountEventCopyWith<$Res> {
  factory _$HandleContinueActionCopyWith(_HandleContinueAction value, $Res Function(_HandleContinueAction) _then) = __$HandleContinueActionCopyWithImpl;
@useResult
$Res call({
 WithdrawalMopTypes mop, String? account
});


$WithdrawalMopTypesCopyWith<$Res> get mop;

}
/// @nodoc
class __$HandleContinueActionCopyWithImpl<$Res>
    implements _$HandleContinueActionCopyWith<$Res> {
  __$HandleContinueActionCopyWithImpl(this._self, this._then);

  final _HandleContinueAction _self;
  final $Res Function(_HandleContinueAction) _then;

/// Create a copy of WithdrawAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? mop = null,Object? account = freezed,}) {
  return _then(_HandleContinueAction(
mop: null == mop ? _self.mop : mop // ignore: cast_nullable_to_non_nullable
as WithdrawalMopTypes,account: freezed == account ? _self.account : account // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

/// Create a copy of WithdrawAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$WithdrawalMopTypesCopyWith<$Res> get mop {
  
  return $WithdrawalMopTypesCopyWith<$Res>(_self.mop, (value) {
    return _then(_self.copyWith(mop: value));
  });
}
}

/// @nodoc
mixin _$WithdrawAccountsAndAmountState {

 Account? get selectedAccount; WithdrawCard? get selectedCard; bool get isButtonEnabled; String get amount; String get convertedAmount; RatesModel? get ratesModel; String get conversionRateString; String get targetCurrency; bool get isLoading; Fees? get fees; bool get hasInsufficientFunds; String get insufficientFundsMessage; WithdrawalMopTypes? get paymentMop;
/// Create a copy of WithdrawAccountsAndAmountState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$WithdrawAccountsAndAmountStateCopyWith<WithdrawAccountsAndAmountState> get copyWith => _$WithdrawAccountsAndAmountStateCopyWithImpl<WithdrawAccountsAndAmountState>(this as WithdrawAccountsAndAmountState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WithdrawAccountsAndAmountState&&(identical(other.selectedAccount, selectedAccount) || other.selectedAccount == selectedAccount)&&(identical(other.selectedCard, selectedCard) || other.selectedCard == selectedCard)&&(identical(other.isButtonEnabled, isButtonEnabled) || other.isButtonEnabled == isButtonEnabled)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.convertedAmount, convertedAmount) || other.convertedAmount == convertedAmount)&&(identical(other.ratesModel, ratesModel) || other.ratesModel == ratesModel)&&(identical(other.conversionRateString, conversionRateString) || other.conversionRateString == conversionRateString)&&(identical(other.targetCurrency, targetCurrency) || other.targetCurrency == targetCurrency)&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.fees, fees) || other.fees == fees)&&(identical(other.hasInsufficientFunds, hasInsufficientFunds) || other.hasInsufficientFunds == hasInsufficientFunds)&&(identical(other.insufficientFundsMessage, insufficientFundsMessage) || other.insufficientFundsMessage == insufficientFundsMessage)&&(identical(other.paymentMop, paymentMop) || other.paymentMop == paymentMop));
}


@override
int get hashCode => Object.hash(runtimeType,selectedAccount,selectedCard,isButtonEnabled,amount,convertedAmount,ratesModel,conversionRateString,targetCurrency,isLoading,fees,hasInsufficientFunds,insufficientFundsMessage,paymentMop);

@override
String toString() {
  return 'WithdrawAccountsAndAmountState(selectedAccount: $selectedAccount, selectedCard: $selectedCard, isButtonEnabled: $isButtonEnabled, amount: $amount, convertedAmount: $convertedAmount, ratesModel: $ratesModel, conversionRateString: $conversionRateString, targetCurrency: $targetCurrency, isLoading: $isLoading, fees: $fees, hasInsufficientFunds: $hasInsufficientFunds, insufficientFundsMessage: $insufficientFundsMessage, paymentMop: $paymentMop)';
}


}

/// @nodoc
abstract mixin class $WithdrawAccountsAndAmountStateCopyWith<$Res>  {
  factory $WithdrawAccountsAndAmountStateCopyWith(WithdrawAccountsAndAmountState value, $Res Function(WithdrawAccountsAndAmountState) _then) = _$WithdrawAccountsAndAmountStateCopyWithImpl;
@useResult
$Res call({
 Account? selectedAccount, WithdrawCard? selectedCard, bool isButtonEnabled, String amount, String convertedAmount, RatesModel? ratesModel, String conversionRateString, String targetCurrency, bool isLoading, Fees? fees, bool hasInsufficientFunds, String insufficientFundsMessage, WithdrawalMopTypes? paymentMop
});


$AccountCopyWith<$Res>? get selectedAccount;$WithdrawCardCopyWith<$Res>? get selectedCard;$RatesModelCopyWith<$Res>? get ratesModel;$FeesCopyWith<$Res>? get fees;$WithdrawalMopTypesCopyWith<$Res>? get paymentMop;

}
/// @nodoc
class _$WithdrawAccountsAndAmountStateCopyWithImpl<$Res>
    implements $WithdrawAccountsAndAmountStateCopyWith<$Res> {
  _$WithdrawAccountsAndAmountStateCopyWithImpl(this._self, this._then);

  final WithdrawAccountsAndAmountState _self;
  final $Res Function(WithdrawAccountsAndAmountState) _then;

/// Create a copy of WithdrawAccountsAndAmountState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? selectedAccount = freezed,Object? selectedCard = freezed,Object? isButtonEnabled = null,Object? amount = null,Object? convertedAmount = null,Object? ratesModel = freezed,Object? conversionRateString = null,Object? targetCurrency = null,Object? isLoading = null,Object? fees = freezed,Object? hasInsufficientFunds = null,Object? insufficientFundsMessage = null,Object? paymentMop = freezed,}) {
  return _then(_self.copyWith(
selectedAccount: freezed == selectedAccount ? _self.selectedAccount : selectedAccount // ignore: cast_nullable_to_non_nullable
as Account?,selectedCard: freezed == selectedCard ? _self.selectedCard : selectedCard // ignore: cast_nullable_to_non_nullable
as WithdrawCard?,isButtonEnabled: null == isButtonEnabled ? _self.isButtonEnabled : isButtonEnabled // ignore: cast_nullable_to_non_nullable
as bool,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as String,convertedAmount: null == convertedAmount ? _self.convertedAmount : convertedAmount // ignore: cast_nullable_to_non_nullable
as String,ratesModel: freezed == ratesModel ? _self.ratesModel : ratesModel // ignore: cast_nullable_to_non_nullable
as RatesModel?,conversionRateString: null == conversionRateString ? _self.conversionRateString : conversionRateString // ignore: cast_nullable_to_non_nullable
as String,targetCurrency: null == targetCurrency ? _self.targetCurrency : targetCurrency // ignore: cast_nullable_to_non_nullable
as String,isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,fees: freezed == fees ? _self.fees : fees // ignore: cast_nullable_to_non_nullable
as Fees?,hasInsufficientFunds: null == hasInsufficientFunds ? _self.hasInsufficientFunds : hasInsufficientFunds // ignore: cast_nullable_to_non_nullable
as bool,insufficientFundsMessage: null == insufficientFundsMessage ? _self.insufficientFundsMessage : insufficientFundsMessage // ignore: cast_nullable_to_non_nullable
as String,paymentMop: freezed == paymentMop ? _self.paymentMop : paymentMop // ignore: cast_nullable_to_non_nullable
as WithdrawalMopTypes?,
  ));
}
/// Create a copy of WithdrawAccountsAndAmountState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AccountCopyWith<$Res>? get selectedAccount {
    if (_self.selectedAccount == null) {
    return null;
  }

  return $AccountCopyWith<$Res>(_self.selectedAccount!, (value) {
    return _then(_self.copyWith(selectedAccount: value));
  });
}/// Create a copy of WithdrawAccountsAndAmountState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$WithdrawCardCopyWith<$Res>? get selectedCard {
    if (_self.selectedCard == null) {
    return null;
  }

  return $WithdrawCardCopyWith<$Res>(_self.selectedCard!, (value) {
    return _then(_self.copyWith(selectedCard: value));
  });
}/// Create a copy of WithdrawAccountsAndAmountState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$RatesModelCopyWith<$Res>? get ratesModel {
    if (_self.ratesModel == null) {
    return null;
  }

  return $RatesModelCopyWith<$Res>(_self.ratesModel!, (value) {
    return _then(_self.copyWith(ratesModel: value));
  });
}/// Create a copy of WithdrawAccountsAndAmountState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$FeesCopyWith<$Res>? get fees {
    if (_self.fees == null) {
    return null;
  }

  return $FeesCopyWith<$Res>(_self.fees!, (value) {
    return _then(_self.copyWith(fees: value));
  });
}/// Create a copy of WithdrawAccountsAndAmountState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$WithdrawalMopTypesCopyWith<$Res>? get paymentMop {
    if (_self.paymentMop == null) {
    return null;
  }

  return $WithdrawalMopTypesCopyWith<$Res>(_self.paymentMop!, (value) {
    return _then(_self.copyWith(paymentMop: value));
  });
}
}


/// @nodoc


class _WithdrawAccountsAndAmountState implements WithdrawAccountsAndAmountState {
  const _WithdrawAccountsAndAmountState({this.selectedAccount, this.selectedCard, this.isButtonEnabled = false, this.amount = '', this.convertedAmount = '', this.ratesModel, this.conversionRateString = '', this.targetCurrency = '', this.isLoading = false, this.fees, this.hasInsufficientFunds = false, this.insufficientFundsMessage = '', this.paymentMop});
  

@override final  Account? selectedAccount;
@override final  WithdrawCard? selectedCard;
@override@JsonKey() final  bool isButtonEnabled;
@override@JsonKey() final  String amount;
@override@JsonKey() final  String convertedAmount;
@override final  RatesModel? ratesModel;
@override@JsonKey() final  String conversionRateString;
@override@JsonKey() final  String targetCurrency;
@override@JsonKey() final  bool isLoading;
@override final  Fees? fees;
@override@JsonKey() final  bool hasInsufficientFunds;
@override@JsonKey() final  String insufficientFundsMessage;
@override final  WithdrawalMopTypes? paymentMop;

/// Create a copy of WithdrawAccountsAndAmountState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$WithdrawAccountsAndAmountStateCopyWith<_WithdrawAccountsAndAmountState> get copyWith => __$WithdrawAccountsAndAmountStateCopyWithImpl<_WithdrawAccountsAndAmountState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _WithdrawAccountsAndAmountState&&(identical(other.selectedAccount, selectedAccount) || other.selectedAccount == selectedAccount)&&(identical(other.selectedCard, selectedCard) || other.selectedCard == selectedCard)&&(identical(other.isButtonEnabled, isButtonEnabled) || other.isButtonEnabled == isButtonEnabled)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.convertedAmount, convertedAmount) || other.convertedAmount == convertedAmount)&&(identical(other.ratesModel, ratesModel) || other.ratesModel == ratesModel)&&(identical(other.conversionRateString, conversionRateString) || other.conversionRateString == conversionRateString)&&(identical(other.targetCurrency, targetCurrency) || other.targetCurrency == targetCurrency)&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.fees, fees) || other.fees == fees)&&(identical(other.hasInsufficientFunds, hasInsufficientFunds) || other.hasInsufficientFunds == hasInsufficientFunds)&&(identical(other.insufficientFundsMessage, insufficientFundsMessage) || other.insufficientFundsMessage == insufficientFundsMessage)&&(identical(other.paymentMop, paymentMop) || other.paymentMop == paymentMop));
}


@override
int get hashCode => Object.hash(runtimeType,selectedAccount,selectedCard,isButtonEnabled,amount,convertedAmount,ratesModel,conversionRateString,targetCurrency,isLoading,fees,hasInsufficientFunds,insufficientFundsMessage,paymentMop);

@override
String toString() {
  return 'WithdrawAccountsAndAmountState(selectedAccount: $selectedAccount, selectedCard: $selectedCard, isButtonEnabled: $isButtonEnabled, amount: $amount, convertedAmount: $convertedAmount, ratesModel: $ratesModel, conversionRateString: $conversionRateString, targetCurrency: $targetCurrency, isLoading: $isLoading, fees: $fees, hasInsufficientFunds: $hasInsufficientFunds, insufficientFundsMessage: $insufficientFundsMessage, paymentMop: $paymentMop)';
}


}

/// @nodoc
abstract mixin class _$WithdrawAccountsAndAmountStateCopyWith<$Res> implements $WithdrawAccountsAndAmountStateCopyWith<$Res> {
  factory _$WithdrawAccountsAndAmountStateCopyWith(_WithdrawAccountsAndAmountState value, $Res Function(_WithdrawAccountsAndAmountState) _then) = __$WithdrawAccountsAndAmountStateCopyWithImpl;
@override @useResult
$Res call({
 Account? selectedAccount, WithdrawCard? selectedCard, bool isButtonEnabled, String amount, String convertedAmount, RatesModel? ratesModel, String conversionRateString, String targetCurrency, bool isLoading, Fees? fees, bool hasInsufficientFunds, String insufficientFundsMessage, WithdrawalMopTypes? paymentMop
});


@override $AccountCopyWith<$Res>? get selectedAccount;@override $WithdrawCardCopyWith<$Res>? get selectedCard;@override $RatesModelCopyWith<$Res>? get ratesModel;@override $FeesCopyWith<$Res>? get fees;@override $WithdrawalMopTypesCopyWith<$Res>? get paymentMop;

}
/// @nodoc
class __$WithdrawAccountsAndAmountStateCopyWithImpl<$Res>
    implements _$WithdrawAccountsAndAmountStateCopyWith<$Res> {
  __$WithdrawAccountsAndAmountStateCopyWithImpl(this._self, this._then);

  final _WithdrawAccountsAndAmountState _self;
  final $Res Function(_WithdrawAccountsAndAmountState) _then;

/// Create a copy of WithdrawAccountsAndAmountState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? selectedAccount = freezed,Object? selectedCard = freezed,Object? isButtonEnabled = null,Object? amount = null,Object? convertedAmount = null,Object? ratesModel = freezed,Object? conversionRateString = null,Object? targetCurrency = null,Object? isLoading = null,Object? fees = freezed,Object? hasInsufficientFunds = null,Object? insufficientFundsMessage = null,Object? paymentMop = freezed,}) {
  return _then(_WithdrawAccountsAndAmountState(
selectedAccount: freezed == selectedAccount ? _self.selectedAccount : selectedAccount // ignore: cast_nullable_to_non_nullable
as Account?,selectedCard: freezed == selectedCard ? _self.selectedCard : selectedCard // ignore: cast_nullable_to_non_nullable
as WithdrawCard?,isButtonEnabled: null == isButtonEnabled ? _self.isButtonEnabled : isButtonEnabled // ignore: cast_nullable_to_non_nullable
as bool,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as String,convertedAmount: null == convertedAmount ? _self.convertedAmount : convertedAmount // ignore: cast_nullable_to_non_nullable
as String,ratesModel: freezed == ratesModel ? _self.ratesModel : ratesModel // ignore: cast_nullable_to_non_nullable
as RatesModel?,conversionRateString: null == conversionRateString ? _self.conversionRateString : conversionRateString // ignore: cast_nullable_to_non_nullable
as String,targetCurrency: null == targetCurrency ? _self.targetCurrency : targetCurrency // ignore: cast_nullable_to_non_nullable
as String,isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,fees: freezed == fees ? _self.fees : fees // ignore: cast_nullable_to_non_nullable
as Fees?,hasInsufficientFunds: null == hasInsufficientFunds ? _self.hasInsufficientFunds : hasInsufficientFunds // ignore: cast_nullable_to_non_nullable
as bool,insufficientFundsMessage: null == insufficientFundsMessage ? _self.insufficientFundsMessage : insufficientFundsMessage // ignore: cast_nullable_to_non_nullable
as String,paymentMop: freezed == paymentMop ? _self.paymentMop : paymentMop // ignore: cast_nullable_to_non_nullable
as WithdrawalMopTypes?,
  ));
}

/// Create a copy of WithdrawAccountsAndAmountState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AccountCopyWith<$Res>? get selectedAccount {
    if (_self.selectedAccount == null) {
    return null;
  }

  return $AccountCopyWith<$Res>(_self.selectedAccount!, (value) {
    return _then(_self.copyWith(selectedAccount: value));
  });
}/// Create a copy of WithdrawAccountsAndAmountState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$WithdrawCardCopyWith<$Res>? get selectedCard {
    if (_self.selectedCard == null) {
    return null;
  }

  return $WithdrawCardCopyWith<$Res>(_self.selectedCard!, (value) {
    return _then(_self.copyWith(selectedCard: value));
  });
}/// Create a copy of WithdrawAccountsAndAmountState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$RatesModelCopyWith<$Res>? get ratesModel {
    if (_self.ratesModel == null) {
    return null;
  }

  return $RatesModelCopyWith<$Res>(_self.ratesModel!, (value) {
    return _then(_self.copyWith(ratesModel: value));
  });
}/// Create a copy of WithdrawAccountsAndAmountState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$FeesCopyWith<$Res>? get fees {
    if (_self.fees == null) {
    return null;
  }

  return $FeesCopyWith<$Res>(_self.fees!, (value) {
    return _then(_self.copyWith(fees: value));
  });
}/// Create a copy of WithdrawAccountsAndAmountState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$WithdrawalMopTypesCopyWith<$Res>? get paymentMop {
    if (_self.paymentMop == null) {
    return null;
  }

  return $WithdrawalMopTypesCopyWith<$Res>(_self.paymentMop!, (value) {
    return _then(_self.copyWith(paymentMop: value));
  });
}
}

/// @nodoc
mixin _$Fees {

 num get fees; String get feesCurrency; bool get isFeesLoading; String get errorMessage;
/// Create a copy of Fees
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$FeesCopyWith<Fees> get copyWith => _$FeesCopyWithImpl<Fees>(this as Fees, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Fees&&(identical(other.fees, fees) || other.fees == fees)&&(identical(other.feesCurrency, feesCurrency) || other.feesCurrency == feesCurrency)&&(identical(other.isFeesLoading, isFeesLoading) || other.isFeesLoading == isFeesLoading)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage));
}


@override
int get hashCode => Object.hash(runtimeType,fees,feesCurrency,isFeesLoading,errorMessage);

@override
String toString() {
  return 'Fees(fees: $fees, feesCurrency: $feesCurrency, isFeesLoading: $isFeesLoading, errorMessage: $errorMessage)';
}


}

/// @nodoc
abstract mixin class $FeesCopyWith<$Res>  {
  factory $FeesCopyWith(Fees value, $Res Function(Fees) _then) = _$FeesCopyWithImpl;
@useResult
$Res call({
 num fees, String feesCurrency, bool isFeesLoading, String errorMessage
});




}
/// @nodoc
class _$FeesCopyWithImpl<$Res>
    implements $FeesCopyWith<$Res> {
  _$FeesCopyWithImpl(this._self, this._then);

  final Fees _self;
  final $Res Function(Fees) _then;

/// Create a copy of Fees
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? fees = null,Object? feesCurrency = null,Object? isFeesLoading = null,Object? errorMessage = null,}) {
  return _then(_self.copyWith(
fees: null == fees ? _self.fees : fees // ignore: cast_nullable_to_non_nullable
as num,feesCurrency: null == feesCurrency ? _self.feesCurrency : feesCurrency // ignore: cast_nullable_to_non_nullable
as String,isFeesLoading: null == isFeesLoading ? _self.isFeesLoading : isFeesLoading // ignore: cast_nullable_to_non_nullable
as bool,errorMessage: null == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// @nodoc


class _Fees implements Fees {
  const _Fees({this.fees = 0.0, this.feesCurrency = '', this.isFeesLoading = false, this.errorMessage = ''});
  

@override@JsonKey() final  num fees;
@override@JsonKey() final  String feesCurrency;
@override@JsonKey() final  bool isFeesLoading;
@override@JsonKey() final  String errorMessage;

/// Create a copy of Fees
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$FeesCopyWith<_Fees> get copyWith => __$FeesCopyWithImpl<_Fees>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Fees&&(identical(other.fees, fees) || other.fees == fees)&&(identical(other.feesCurrency, feesCurrency) || other.feesCurrency == feesCurrency)&&(identical(other.isFeesLoading, isFeesLoading) || other.isFeesLoading == isFeesLoading)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage));
}


@override
int get hashCode => Object.hash(runtimeType,fees,feesCurrency,isFeesLoading,errorMessage);

@override
String toString() {
  return 'Fees(fees: $fees, feesCurrency: $feesCurrency, isFeesLoading: $isFeesLoading, errorMessage: $errorMessage)';
}


}

/// @nodoc
abstract mixin class _$FeesCopyWith<$Res> implements $FeesCopyWith<$Res> {
  factory _$FeesCopyWith(_Fees value, $Res Function(_Fees) _then) = __$FeesCopyWithImpl;
@override @useResult
$Res call({
 num fees, String feesCurrency, bool isFeesLoading, String errorMessage
});




}
/// @nodoc
class __$FeesCopyWithImpl<$Res>
    implements _$FeesCopyWith<$Res> {
  __$FeesCopyWithImpl(this._self, this._then);

  final _Fees _self;
  final $Res Function(_Fees) _then;

/// Create a copy of Fees
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? fees = null,Object? feesCurrency = null,Object? isFeesLoading = null,Object? errorMessage = null,}) {
  return _then(_Fees(
fees: null == fees ? _self.fees : fees // ignore: cast_nullable_to_non_nullable
as num,feesCurrency: null == feesCurrency ? _self.feesCurrency : feesCurrency // ignore: cast_nullable_to_non_nullable
as String,isFeesLoading: null == isFeesLoading ? _self.isFeesLoading : isFeesLoading // ignore: cast_nullable_to_non_nullable
as bool,errorMessage: null == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
