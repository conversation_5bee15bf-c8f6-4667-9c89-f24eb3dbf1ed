part of 'withdraw_accounts_and_amount_bloc.dart';

@freezed
sealed class WithdrawAccountsAndAmountEvent
    with _$WithdrawAccountsAndAmountEvent {
  const factory WithdrawAccountsAndAmountEvent.onAccountChange(
    Account account,
  ) = _OnAccountChange;

  const factory WithdrawAccountsAndAmountEvent.setSelectedCard(
    WithdrawCard? selectedCard,
  ) = _SetSelectedCard;

  const factory WithdrawAccountsAndAmountEvent.changeButtonState({
    required bool isValid,
    required String amount,
    required String convertedAmount,
    required RatesModel? ratesModel,
    String? conversionRateString,
    String? targetCurrency,
  }) = _ChangeButtonState;

  const factory WithdrawAccountsAndAmountEvent.calculateFees({
    required WithdrawalMopTypes mop,
  }) = _CalculateFees;

  const factory WithdrawAccountsAndAmountEvent.setPaymentMethod({
    required WithdrawalMopTypes paymentMop,
  }) = _SetPaymentMethod;

  const factory WithdrawAccountsAndAmountEvent.handleContinueAction({
    required WithdrawalMopTypes mop,
    String? account,
  }) = _HandleContinueAction;
}
