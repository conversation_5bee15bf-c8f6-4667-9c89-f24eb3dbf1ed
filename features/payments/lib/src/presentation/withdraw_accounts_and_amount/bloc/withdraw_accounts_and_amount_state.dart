part of 'withdraw_accounts_and_amount_bloc.dart';

@freezed
sealed class WithdrawAccountsAndAmountState
    with _$WithdrawAccountsAndAmountState {
  const factory WithdrawAccountsAndAmountState({
    Account? selectedAccount,
    WithdrawCard? selectedCard,
    @Default(false) bool isButtonEnabled,
    @Default('') String amount,
    @Default('') String convertedAmount,
    RatesModel? ratesModel,
    @Default('') String conversionRateString,
    @Default('') String targetCurrency,
    @Default(false) bool isLoading,
    Fees? fees,
    @Default(false) bool hasInsufficientFunds,
    @Default('') String insufficientFundsMessage,
    WithdrawalMopTypes? paymentMop, // Add payment method information
  }) = _WithdrawAccountsAndAmountState;
}

@freezed
sealed class Fees with _$Fees {
  const factory Fees({
    @Default(0.0) num fees,
    @Default('') String feesCurrency,
    @Default(false) bool isFeesLoading,
    @Default('') String errorMessage,
  }) = _Fees;
}
