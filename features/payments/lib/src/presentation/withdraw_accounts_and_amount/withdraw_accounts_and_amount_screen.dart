import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:payment/src/data/conversion_rate/conversion_rate_model.dart';
import 'package:payment/src/data/payment_method/withdraw_payment_methods_model/withdraw_payment_methods_model.dart';
import 'package:payment/src/data/withdraw_card_model/withdraw_card_model.dart';
import 'package:payment/src/di/di_container.dart';
import 'package:payment/src/domain/data/conversion_type.dart';
import 'package:payment/src/domain/data/payment_type.dart';
import 'package:payment/src/domain/data/withdrawal_mop_types.dart';
import 'package:payment/src/data/account_model.dart';
import 'package:payment/src/presentation/widgets/account_list_widget/account_list_widget.dart';
import 'package:payment/src/presentation/widgets/amount_conversion_widget/amount_conversion_widget.dart';
import 'package:payment/src/presentation/widgets/withdraw_fees_display/withdraw_fees_display.dart';
import 'package:prelude/prelude.dart';

import 'bloc/withdraw_accounts_and_amount_bloc.dart';

class WithdrawAccountsAndAmountScreen extends StatelessWidget {
  const WithdrawAccountsAndAmountScreen({
    super.key,
    required this.paymentMethod,
    this.account,
    this.selectedCard,
  });
  final WithdrawalPaymentMethod paymentMethod;
  // optional field for additional payment methods
  final String? account;
  final WithdrawCard? selectedCard;

  void _handleContinueButtonTap(BuildContext context) {
    context.read<WithdrawAccountsAndAmountBloc>().add(
      WithdrawAccountsAndAmountEvent.handleContinueAction(
        mop: paymentMethod.mop!,
        account: account,
      ),
    );
  }

  String getWithdrawalFees(
    BuildContext context,
    WithdrawAccountsAndAmountState state,
  ) {
    final locale = Localizations.localeOf(context).toString();
    if (state.amount.isEmpty) {
      return "Add an amount to calculate withdrawal fees (if applicable)";
    }
    if (state.fees?.isFeesLoading ?? false) {
      return "Calculating withdrawal fees..";
    }

    final amount = double.tryParse(state.amount) ?? 0.0;
    final fees = state.fees?.fees ?? 0.0;

    if (fees == 0) {
      return "No fees will be deducted for this withdrawal.";
    }

    final currency = state.fees?.feesCurrency ?? "";
    final total = amount + fees;
    final formattedFees = EquitiFormatter.formatNumber(
      value: fees,
      locale: locale,
    );
    final formattedAmount = EquitiFormatter.formatNumber(
      value: amount,
      locale: locale,
    );
    final formattedTotal = EquitiFormatter.formatNumber(
      value: total,
      locale: locale,
    );
    return "${formattedTotal} $currency will be deducted (includes $formattedFees $currency fee). You'll receive $formattedAmount $currency.";
  }

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final EquitiLocalization localization = EquitiLocalization.of(context);
    return BlocProvider(
      create:
          (createContext) =>
              diContainer<WithdrawAccountsAndAmountBloc>()
                ..add(
                  WithdrawAccountsAndAmountEvent.setPaymentMethod(
                    paymentMop: paymentMethod.mop!,
                  ),
                )
                ..add(
                  WithdrawAccountsAndAmountEvent.setSelectedCard(selectedCard),
                ),
      child: Scaffold(
        backgroundColor: theme.background.bgPrimary,
        appBar: DuploAppBar(
          title: "${localization.payments_withdraw_with} ${paymentMethod.name}",
        ),
        body: GestureDetector(
          onTap: () {
            FocusScope.of(context).unfocus();
          },
          child: SingleChildScrollView(
            physics: const BouncingScrollPhysics(),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  DuploText(
                    text: localization.payments_withdraw_from_account,
                    style: DuploTextStyles.of(context).textLg,
                    fontWeight: DuploFontWeight.semiBold,
                    color: theme.text.textPrimary,
                  ),
                  const SizedBox(height: 5),
                  DuploText(
                    text: localization.payments_withdraw_select_trading_account,
                    style: DuploTextStyles.of(context).textSm,
                    fontWeight: DuploFontWeight.regular,
                    color: theme.text.textSecondary,
                  ),
                  const SizedBox(height: 15),
                  BlocBuilder<
                    WithdrawAccountsAndAmountBloc,
                    WithdrawAccountsAndAmountState
                  >(
                    buildWhen:
                        (previous, current) => current.selectedAccount != null,
                    builder: (builderContext, state) {
                      return AccountListWidget(
                        args: (
                          selectByHighestBalance: false,
                          excludeAccountNumber: null,
                          onEmptyStateChanged: null,
                        ),
                        onAccountSelected: (Account acc) {
                          builderContext
                              .read<WithdrawAccountsAndAmountBloc>()
                              .add(
                                WithdrawAccountsAndAmountEvent.onAccountChange(
                                  acc,
                                ),
                              );
                        },
                      );
                    },
                  ),
                  const SizedBox(height: 15),
                  BlocBuilder<
                    WithdrawAccountsAndAmountBloc,
                    WithdrawAccountsAndAmountState
                  >(
                    buildWhen:
                        (previous, current) =>
                            previous.selectedAccount != current.selectedAccount,
                    builder: (builderContext, state) {
                      return state.selectedAccount != null
                          ? BlocBuilder<
                            WithdrawAccountsAndAmountBloc,
                            WithdrawAccountsAndAmountState
                          >(
                            buildWhen:
                                (previous, current) =>
                                    previous.hasInsufficientFunds !=
                                        current.hasInsufficientFunds ||
                                    previous.insufficientFundsMessage !=
                                        current.insufficientFundsMessage,
                            builder: (withdrawContext, withdrawState) {
                              return AmountConversionWidget(
                                args: (
                                  transferCurrency:
                                      state.selectedAccount!.accountCurrency,
                                  transferCurrencyImage: '',
                                  currencyAmountDetails:
                                      paymentMethod.currencyAmountDetails ?? [],
                                  currencies: paymentMethod.currencies ?? [],
                                  showSuggestedAmounts: true,
                                  isStartWithConversionRate: false,
                                  targetCurrency: null,
                                  externalErrorMessage:
                                      withdrawState.hasInsufficientFunds
                                          ? withdrawState
                                              .insufficientFundsMessage
                                          : null,
                                  paymentType: PaymentType.withdrawal,
                                  conversionType:
                                      ConversionType.targetToAccountCurrency,
                                ),
                                onAmountChange: ({
                                  required String amount,
                                  required bool isAmountValid,
                                  required String convertedAmount,
                                  required ConversionRateModel?
                                  conversionRateData,
                                  required RatesModel? ratesModel,
                                  String? conversionRateString,
                                  String? targetCurrency,
                                }) {
                                  final bloc =
                                      builderContext
                                          .read<
                                            WithdrawAccountsAndAmountBloc
                                          >();
                                  bloc.add(
                                    WithdrawAccountsAndAmountEvent.changeButtonState(
                                      isValid: isAmountValid,
                                      amount: amount,
                                      convertedAmount: convertedAmount,
                                      ratesModel: ratesModel,
                                      conversionRateString:
                                          conversionRateString,
                                      targetCurrency: targetCurrency,
                                    ),
                                  );
                                },
                              );
                            },
                          )
                          : Container();
                    },
                  ),
                  BlocBuilder<
                    WithdrawAccountsAndAmountBloc,
                    WithdrawAccountsAndAmountState
                  >(
                    buildWhen: (previous, current) => previous != current,
                    builder: (ctx, state) {
                      final String mopString;
                      switch (paymentMethod.mop) {
                        case WithdrawalMopWrapper(:final mop):
                          {
                            switch (mop) {
                              case WithdrawalMop.cards:
                                mopString = state.selectedCard?.mop.name ?? "";
                                break;
                              default:
                                mopString = mop.name;
                                break;
                            }
                            return state.selectedAccount != null &&
                                    mop != WithdrawalMop.bank
                                ? WithdrawFeesDisplay(
                                  args: (
                                    amount: double.tryParse(state.amount) ?? 0,
                                    accountId: state.selectedAccount!.accountId,
                                    accountCurrency:
                                        state.selectedAccount!.accountCurrency,
                                    transactionCurrency: state.targetCurrency,
                                    paymentType: mopString,
                                    convertedAmount:
                                        double.tryParse(
                                          state.convertedAmount,
                                        ) ??
                                        0,
                                    transferType: null,
                                  ),
                                  content: (
                                    idle:
                                        localization
                                            .payments_withdraw_fees_idle,
                                    loading:
                                        localization
                                            .payments_withdraw_fees_loading,
                                    zeroFees:
                                        localization
                                            .payments_withdraw_fees_zero,
                                    nonZeroFees:
                                        (fees, total, currency) => localization
                                            .payments_withdraw_fees_non_zero(
                                              '\$${fees.toStringAsFixed(2)}',
                                              '\$${total.toStringAsFixed(2)}',
                                              currency,
                                            ),
                                  ),
                                  onFeesChange: (withdrawFeesState) {
                                    print(
                                      "WithdrawFeesState: $withdrawFeesState",
                                    );
                                  },
                                )
                                : Container();
                          }
                        default:
                          return Container();
                      }
                    },
                  ),

                  const SizedBox(height: DuploSpacing.spacing_xl_16),
                  BlocBuilder<
                    WithdrawAccountsAndAmountBloc,
                    WithdrawAccountsAndAmountState
                  >(
                    buildWhen:
                        (previous, current) =>
                            previous.isButtonEnabled !=
                                current.isButtonEnabled ||
                            previous.isLoading != current.isLoading,
                    builder: (builderContext, state) {
                      return DuploButton.defaultPrimary(
                        title: "Continue",
                        onTap: () => _handleContinueButtonTap(builderContext),
                        isDisabled: !state.isButtonEnabled,
                        isLoading: state.isLoading,
                        useFullWidth: true,
                      );
                    },
                  ),
                  const SizedBox(height: 20), // Extra space at the bottom
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
