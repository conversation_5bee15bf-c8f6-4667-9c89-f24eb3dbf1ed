import 'dart:async';
import 'dart:developer';

import 'package:domain/domain.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:payment/src/data/withdraw_request_model/withdraw_request_model.dart';
import 'package:payment/src/data/withdraw_response_model/withdraw_response_model.dart';
import 'package:payment/src/data/withdraw_transfer_type_response_model/withdraw_transfer_type_response_model.dart';
import 'package:payment/src/domain/model/withdraw_add_new_account_param/withdraw_add_new_account_param.dart';
import 'package:payment/src/domain/model/withdraw_card_params/withdraw_card_params.dart';
import 'package:payment/src/domain/usecase/get_transfer_type_usecase.dart';
import 'package:payment/src/domain/usecase/withdrawal_usecase.dart';
import 'package:payment/src/navigation/payment_navigation.dart';
import 'package:payment/src/domain/data/withdraw_status_type.dart';

@injectable
part 'withdraw_add_new_bank_bloc.freezed.dart';
part 'withdraw_add_new_bank_event.dart';
part 'withdraw_add_new_bank_state.dart';

class WithdrawAddNewBankBloc
    extends Bloc<WithdrawAddNewBankEvent, WithdrawAddNewBankState> {
  final GetCountryUseCase _getCountryUseCase;
  final GetTransferTypeUseCase _getTransferTypeUseCase;
  final PaymentNavigation _paymentNavigation;
  final WithdrawalUseCase _withdrawalUseCase;

  WithdrawAddNewBankBloc({
    required GetCountryUseCase getCountryUseCase,
    required GetTransferTypeUseCase getTransferTypeUseCase,
    required PaymentNavigation paymentNavigation,
    required WithdrawalUseCase withdrawalUseCase,
  }) : _getCountryUseCase = getCountryUseCase,
       _getTransferTypeUseCase = getTransferTypeUseCase,
       _paymentNavigation = paymentNavigation,
       _withdrawalUseCase = withdrawalUseCase,
       super(const WithdrawAddNewBankState()) {
    on<OnLocationOfBankChangedEvent>(_onLocationOfBankChanged);
    on<GetCountriesForLocationOfBankEvent>(_getCountriesForLocationOfBank);
    on<OnRequestWithdrawButtonPressedEvent>(_onRequestWithdrawButtonPressed);
    on<OnWithdrawalDetailsPressedEvent>(_onWithdrawalDetailsPressed);
    on<GetTransferTypeEvent>(_getTransferType);
    on<OnTransferTypeChangedEvent>(_onTransferTypeChanged);
    on<OnFormChangedEvent>(_onFormChanged);
    on<OnAccountManuallyEnteredEvent>(_onAccountManuallyEntered);
    on<ResetProcessStateEvent>(_resetProcessState);
    add(GetCountriesForLocationOfBankEvent());
  }

  Future<void> _getCountriesForLocationOfBank(
    GetCountriesForLocationOfBankEvent event,
    Emitter<WithdrawAddNewBankState> emit,
  ) async {
    emit(
      state.copyWith(isBankLocationLoading: true, processState: LoadedState()),
    );
    try {
      final result = await _getCountryUseCase().run();
      if (isClosed) return;
      result.fold(
        (l) {
          // todo(sambhav) : handle error message
          emit(
            state.copyWith(
              processState: ErrorState(
                message: "Something went wrong! Please try again letter",
              ),
              isBankLocationLoading: false,
            ),
          );
        },
        (r) {
          switch (r) {
            case AllCountriesModelSuccess(:final countries):
              emit(
                state.copyWith(
                  countries: countries,
                  processState: LoadedState(),
                  isBankLocationLoading: false,
                ),
              );
              break;
            case AllCountriesModelInternalServerError(:final error):
              emit(
                state.copyWith(
                  processState: ErrorState(
                    message:
                        error ??
                        "Something went wrong! Please try again letter",
                  ),
                  isBankLocationLoading: false,
                ),
              );
          }
        },
      );
    } catch (e, stacktrace) {
      if (!isClosed) {
        log('Exception in _onLocationOfBankChanged: $e\n$stacktrace');
        emit(
          state.copyWith(
            isBankLocationLoading: false,
            processState: ErrorState(
              message: "Something went wrong! Please try again letter",
            ),
          ),
        );
      }
    }
  }

  void _onLocationOfBankChanged(
    OnLocationOfBankChangedEvent event,
    Emitter<WithdrawAddNewBankState> emit,
  ) {
    emit(
      state.copyWith(
        selectedBankLocationIndex: event.index,
        transferType: [],
        selectedTransferTypeIndex: -1,
      ),
    );
    final selectedCountry = state.countries.elementAtOrNull(event.index);
    if (selectedCountry?.brokerId == null) {
      log('brokerId is null can\'t get transfer type');
      return;
    }
    add(
      GetTransferTypeEvent(
        brokerId: selectedCountry!.brokerId!,
        countryCodeInThreeCharacter: selectedCountry.code,
      ),
    );
  }

  FutureOr<void> _onTransferTypeChanged(
    OnTransferTypeChangedEvent event,
    Emitter<WithdrawAddNewBankState> emit,
  ) {
    log('onTransferTypeChanged: ${event.index}');

    emit(state.copyWith(selectedTransferTypeIndex: event.index));
  }

  Future<void> _onRequestWithdrawButtonPressed(
    OnRequestWithdrawButtonPressedEvent event,
    Emitter<WithdrawAddNewBankState> emit,
  ) async {
    log('onRequestWithdrawButtonPressed');
    final request = WithdrawRequestModel(
      paymentType: event.withdrawCardParams.paymentType.name,
      accountInfo: AccountInfo(
        tradingAccountId: event.withdrawCardParams.tradingAccountId,
        accountCurrency: event.withdrawCardParams.accountCurrency,
      ),
      amount: Amount(
        currency: event.withdrawCardParams.currency,
        conversionRateString: event.withdrawCardParams.conversionRateString,
        amount: event.withdrawCardParams.amount,
        convertedAmount: event.withdrawCardParams.convertedAmount,
        conversionRate: event.withdrawCardParams.conversionRate,
      ),
      metadata: Metadata.bank(
        transferType: event.withdrawAddNewAccountParam.transferType!,
        bankAccount: BankAccount(
          country: event.withdrawAddNewAccountParam.country!.code,
          accountHolder: event.withdrawAddNewAccountParam.nameOfAccountHolder!,
          bankName: event.withdrawAddNewAccountParam.bankName!,
          accountNickname: event.withdrawAddNewAccountParam.accountNickname!,
          branchName: event.withdrawAddNewAccountParam.branchName!,
          swiftBic: event.withdrawAddNewAccountParam.swiftBic!,
          iban: event.withdrawAddNewAccountParam.accountNumber!,
        ),
      ),
    );

    try {
      emit(state.copyWith(isRequestWithdrawButtonLoading: true));
      log('request for withdraw : $request');
      final result = await _withdrawalUseCase(request: request).run();
      result.fold(
        (failure) {
          log('error on withdraw ${failure}');
          if (!isClosed) {
            emit(
              state.copyWith(
                processState: ErrorState(message: failure.toString()),
                isRequestWithdrawButtonLoading: false,
              ),
            );
          }
        },
        (WithdrawResponseModel withdrawResponseModel) {
          switch (withdrawResponseModel) {
            case final WithdrawSuccessResponse _success:
              log('success on withdraw ${_success.data}');
              if (!isClosed) {
                emit(
                  state.copyWith(
                    processState: SuccessState(),
                    isRequestWithdrawButtonLoading: false,
                    withdrawStatus: _success.data.status,
                  ),
                );
              }
              _paymentNavigation.goToWithdrawNewBankUploadDocScreen(
                operationId: _success.data.operationId!,
                tradingAccountId: event.withdrawCardParams.tradingAccountId,
                replace: true,
              );
              break;
            case final WithdrawInternalServerErrorResponse internalServerError:
              log(
                'internal server error on withdraw ${internalServerError.error}',
              );
              if (!isClosed) {
                emit(
                  state.copyWith(
                    processState: ErrorState(
                      message: internalServerError.error,
                    ),
                    isRequestWithdrawButtonLoading: false,
                  ),
                );
              }
              break;
          }
        },
      );
    } catch (e) {
      log(' error on withdrawal options $e');
      if (!isClosed) {
        emit(
          state.copyWith(
            processState: ErrorState(message: e.toString()),
            isRequestWithdrawButtonLoading: false,
          ),
        );
      }
    }
  }

  FutureOr<void> _onWithdrawalDetailsPressed(
    OnWithdrawalDetailsPressedEvent event,
    Emitter<WithdrawAddNewBankState> emit,
  ) {
    log('onWithdrawalDetailsPressed');
  }

  Future<void> _getTransferType(
    GetTransferTypeEvent event,
    Emitter<WithdrawAddNewBankState> emit,
  ) async {
    emit(
      state.copyWith(isTransferTypeLoading: true, processState: LoadedState()),
    );
    try {
      final result =
          await _getTransferTypeUseCase(
            brokerId: event.brokerId,
            //todo (sambhav): get 3 character country code from selected country but its not coming for now
            countryCodeInThreeCharacter: event.countryCodeInThreeCharacter,
          ).run();
      if (isClosed) return;
      result.fold(
        (l) {
          // todo(sambhav) : handle error message
          emit(
            state.copyWith(
              processState: ErrorState(
                message: "Something went wrong! Please try again letter",
              ),
              isTransferTypeLoading: false,
            ),
          );
        },
        (r) {
          switch (r) {
            case WithdrawTransferTypeResponseSuccessModel(:final data):
              emit(
                state.copyWith(
                  processState: LoadedState(),
                  isTransferTypeLoading: false,
                  transferType: data.transferTypes,
                ),
              );
              break;
            case WithdrawTransferTypeResponseModelInternalServerError(
              :final error,
            ):
              emit(
                state.copyWith(
                  processState: ErrorState(message: error),
                  isTransferTypeLoading: false,
                ),
              );
          }
        },
      );
    } catch (e, stacktrace) {
      if (!isClosed) {
        log('Exception in _onLocationOfBankChanged: $e\n$stacktrace');
        emit(
          state.copyWith(
            isTransferTypeLoading: false,
            processState: ErrorState(
              message: "Something went wrong! Please try again letter",
            ),
          ),
        );
      }
    }
  }

  FutureOr<void> _onFormChanged(
    OnFormChangedEvent event,
    Emitter<WithdrawAddNewBankState> emit,
  ) {
    final selectedCountry =
        state.countries.isNotEmpty && state.selectedBankLocationIndex != -1
            ? state.countries.elementAtOrNull(state.selectedBankLocationIndex)
            : null;
    final selectedTransferType =
        state.transferType.isNotEmpty && state.selectedTransferTypeIndex != -1
            ? state.transferType.elementAtOrNull(
              state.selectedTransferTypeIndex,
            )
            : null;
    final isValid =
        event.withdrawAddNewAccountParam
            .copyWith(
              country: selectedCountry,
              transferType: selectedTransferType,
            )
            .isValid;
    log('onFormChanged: $isValid');
    final bankName = (event.withdrawAddNewAccountParam.bankName ?? "")
        .replaceAll(" ", "");
    final fullAccountNumber =
        event.withdrawAddNewAccountParam.accountNumber ?? "";
    final accountNumber = (fullAccountNumber.length > 4
            ? fullAccountNumber.substring(fullAccountNumber.length - 4)
            : fullAccountNumber)
        .replaceAll(" ", "");

    if (bankName.isNotEmpty &&
        accountNumber.isNotEmpty &&
        !state.isAccountNameManuallyEntered) {
      if (!isClosed) {
        final autoCreatedAccountName = '${bankName}${accountNumber}';
        emit(
          state.copyWith(autoCreatedAccountNickname: autoCreatedAccountName),
        );
      }
    }

    if (!isClosed) {
      emit(
        state.copyWith(
          isButtonDisabled: !isValid,
          formData: event.withdrawAddNewAccountParam,
        ),
      );
    }
  }

  FutureOr<void> _onAccountManuallyEntered(
    OnAccountManuallyEnteredEvent event,
    Emitter<WithdrawAddNewBankState> emit,
  ) {
    emit(state.copyWith(isAccountNameManuallyEntered: true));
  }

  FutureOr<void> _resetProcessState(
    ResetProcessStateEvent event,
    Emitter<WithdrawAddNewBankState> emit,
  ) {
    emit(state.copyWith(processState: LoadedState()));
  }
}
