import 'dart:async';
import 'dart:developer';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:payment/src/data/withdraw_request_model/withdraw_request_model.dart';
import 'package:payment/src/data/withdraw_response_model/withdraw_response_model.dart';
import 'package:payment/src/domain/model/withdraw_card_params/withdraw_card_params.dart';
import 'package:payment/src/domain/usecase/withdrawal_usecase.dart';
import 'package:payment/src/navigation/payment_navigation.dart';
import 'package:user_account/user_account.dart' show ClientProfileUseCase;

part 'transfer_type_bloc.freezed.dart';
part 'transfer_type_event.dart';
part 'transfer_type_state.dart';

class TransferTypeBloc extends Bloc<TransferTypeEvent, TransferTypeState> {
  final WithdrawalUseCase _withdrawalUseCase;
  final ClientProfileUseCase _clientProfileUseCase;
  final PaymentNavigation _paymentNavigation;

  TransferTypeBloc(
    this._withdrawalUseCase,
    this._clientProfileUseCase,
    this._paymentNavigation,
  ) : super(TransferTypeState()) {
    on<_OnChangeTransferType>(_onChangeTransferType);
    on<_OnWithdrawalRequest>(_onWithdrawalRequest);
    on<_OnRequestClientProfileData>(_onRequestClientProfileData);
    on<_ResetProcessState>(_onResetProcessState);
  }

  FutureOr<void> _onChangeTransferType(
    _OnChangeTransferType event,
    Emitter<TransferTypeState> emit,
  ) {
    emit(state.copyWith(transferType: event.transferType));
  }

  FutureOr<void> _onWithdrawalRequest(
    _OnWithdrawalRequest event,
    Emitter<TransferTypeState> emit,
  ) async {
    emit(state.copyWith(isRequestWithdrawButtonLoading: true));

    try {
      final result =
          await _withdrawalUseCase(
            request: WithdrawRequestModel(
              paymentType: event.amountModel.paymentType.name,
              accountInfo: AccountInfo(
                tradingAccountId: event.amountModel.tradingAccountId,
                accountCurrency: event.amountModel.accountCurrency,
              ),
              amount: Amount(
                currency: event.amountModel.currency,
                conversionRateString: event.amountModel.conversionRateString,
                amount: event.amountModel.amount,
                convertedAmount: event.amountModel.convertedAmount,
                conversionRate: event.amountModel.conversionRate,
              ),
              metadata: Metadata.bank(
                transferType: state.transferType ?? "",
                bankAccount: event.bankAccount,
              ),
            ),
          ).run();

      if (isClosed) return;

      result.fold(
        (failure) {
          log('Withdrawal request failed: $failure');
          emit(
            state.copyWith(
              processState: TransferTypeProcessState.error(
                message: "Something went wrong! Please try again later",
              ),
              isRequestWithdrawButtonLoading: false,
            ),
          );
        },
        (withdrawResponseModel) {
          switch (withdrawResponseModel) {
            case final WithdrawSuccessResponse success:
              log('Withdrawal request successful: ${success.data}');
              emit(
                state.copyWith(
                  processState: TransferTypeProcessState.success(),
                  isRequestWithdrawButtonLoading: false,
                ),
              );
              break;
            case final WithdrawInternalServerErrorResponse internalServerError:
              log(
                'Withdrawal internal server error: ${internalServerError.error}',
              );
              emit(
                state.copyWith(
                  processState: TransferTypeProcessState.error(
                    message: internalServerError.error,
                  ),
                  isRequestWithdrawButtonLoading: false,
                ),
              );
              break;
          }
        },
      );
    } catch (e) {
      log('Withdrawal request exception: $e');
      if (!isClosed) {
        emit(
          state.copyWith(
            processState: TransferTypeProcessState.error(
              message: "Something went wrong! Please try again later",
            ),
            isRequestWithdrawButtonLoading: false,
          ),
        );
      }
    }
  }

  FutureOr<void> _onRequestClientProfileData(
    _OnRequestClientProfileData event,
    Emitter<TransferTypeState> emit,
  ) async {
    final clientProfile = await _clientProfileUseCase().run();
    clientProfile.fold(
      (left) {
        log("Client profile request failed: $left");
      },
      (right) {
        if (!isClosed) emit(state.copyWith(clientAccountId: right.accountId));
      },
    );
  }

  FutureOr<void> _onResetProcessState(
    _ResetProcessState event,
    Emitter<TransferTypeState> emit,
  ) {
    emit(
      state.copyWith(
        processState: TransferTypeProcessState.loaded(),
        isRequestWithdrawButtonLoading: false,
        transferType: null,
      ),
    );
  }
}
