import 'package:duplo/duplo.dart';
import 'package:equiti_localization/l10n/gen/equiti_localization.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:payment/src/assets/assets.gen.dart' as assets;
import 'package:payment/src/data/bank_accounts_model.dart';
import 'package:payment/src/data/withdraw_request_model/withdraw_request_model.dart';
import 'package:payment/src/domain/model/transfer_type.dart';
import 'package:payment/src/domain/model/withdraw_card_params/withdraw_card_params.dart';
import 'package:payment/src/presentation/widgets/withdraw_fees_display/withdraw_fees_display.dart';
import 'package:payment/src/presentation/withdraw_bank_transfer/transfer_type/bloc/transfer_type_bloc.dart';
import 'package:payment/src/presentation/withdraw_bank_transfer/widgets/bank_details_bottom_sheet.dart';
import 'package:payment/src/presentation/withdraw_bank_transfer/widgets/transfer_type_bottom_sheet.dart';

class TransferTypeLoadedScreen extends StatefulWidget {
  const TransferTypeLoadedScreen({
    super.key,
    required this.bankTransferAmountModel,
    required this.bank,
  });

  final WithdrawCardParams bankTransferAmountModel;
  final BankAccountData bank;

  @override
  State<TransferTypeLoadedScreen> createState() =>
      _TransferTypeLoadedScreenState();
}

class _TransferTypeLoadedScreenState extends State<TransferTypeLoadedScreen> {
  final TextEditingController _controller = TextEditingController();

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final localizations = EquitiLocalization.of(context);
    return Scaffold(
      backgroundColor: theme.background.bgPrimary,
      appBar: DuploAppBar(
        title: localizations.payments_withdraw_to_your_bank_Account,
      ),
      body: Padding(
        padding: const EdgeInsets.all(18.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            DuploText(
              text: localizations.payments_choose_transfer_type,
              color: theme.text.textPrimary,
              fontWeight: DuploFontWeight.semiBold,
              style: DuploTextStyles.of(context).textXl,
            ),
            const SizedBox(height: 20),
            Stack(
              alignment: Alignment.center,
              children: [
                BlocBuilder<TransferTypeBloc, TransferTypeState>(
                  buildWhen: (previous, current) => false,
                  builder: (builderContext, state) {
                    return DuploTextField(
                      controller: _controller,
                      label: localizations.payments_transfer_type,
                      hint: localizations.payments_transfer_type,
                      readOnly: true,
                      onTap: () {
                        DuploSheet.showModalSheet<Widget>(
                          context: context,
                          title: widget.bank.accountNickname,
                          subtitle: widget.bank.bankName,
                          hideCloseButton: true,
                          content: (contextContext) {
                            return TransferTypeBottomSheet(
                              transferTypes:
                                  widget.bank.transferTypes
                                      .map((e) => e.toString())
                                      .toList(),
                              onSelectItem: (item) {
                                _controller.text = item;
                                builderContext.read<TransferTypeBloc>().add(
                                  TransferTypeEvent.onChangeTransferType(item),
                                );
                              },
                            );
                          },
                        );
                      },
                    );
                  },
                ),
                Padding(
                  padding: const EdgeInsets.only(right: 12.0),
                  child: Align(
                    alignment: Alignment.centerRight,
                    child: assets.Assets.images.chevronDown.svg(),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            BlocBuilder<TransferTypeBloc, TransferTypeState>(
              buildWhen:
                  (previous, current) =>
                      previous.transferType != current.transferType,
              builder: (builderContext, state) {
                return state.transferType != null
                    ? WithdrawFeesDisplay(
                      args: (
                        amount: widget.bankTransferAmountModel.amount,
                        accountId: state.clientAccountId ?? "",
                        accountCurrency:
                            widget.bankTransferAmountModel.accountCurrency,
                        transactionCurrency:
                            widget.bankTransferAmountModel.currency,
                        paymentType:
                            widget.bankTransferAmountModel.paymentType.name,
                        convertedAmount:
                            widget.bankTransferAmountModel.convertedAmount,
                        transferType:
                            state.transferType == "Domestic"
                                ? TransferType.domestic
                                : TransferType.international,
                      ),
                      content: (
                        idle: localizations.payments_withdraw_fees_idle,
                        loading: localizations.payments_withdraw_fees_loading,
                        zeroFees: localizations.payments_withdraw_fees_zero,
                        nonZeroFees:
                            (fees, total, currency) =>
                                localizations.payments_withdraw_fees_non_zero(
                                  '\$${fees.toStringAsFixed(2)}',
                                  '\$${total.toStringAsFixed(2)}',
                                  currency,
                                ),
                      ),
                      onFeesChange: (withdrawFeesState) {
                        print("WithdrawFeesState: $withdrawFeesState");
                      },
                    )
                    : DuploAlertMessage.warning(
                      title:
                          localizations.payments_expandable_info_transfer_type,
                    );
              },
            ),
            const Spacer(),
            SizedBox(
              width: MediaQuery.sizeOf(context).width,
              child: BlocBuilder<TransferTypeBloc, TransferTypeState>(
                buildWhen:
                    (previous, current) =>
                        previous.transferType != current.transferType ||
                        previous.isRequestWithdrawButtonLoading !=
                            current.isRequestWithdrawButtonLoading,
                builder: (builderContext, state) {
                  return DuploButton.defaultPrimary(
                    trailingIcon: assets.Assets.images.arrowRightIc.keyName,
                    title: localizations.payments_request_withdrawal,
                    isLoading: state.isRequestWithdrawButtonLoading,
                    onTap: () {
                      builderContext.read<TransferTypeBloc>().add(
                        TransferTypeEvent.onWithdrawalRequest(
                          widget.bankTransferAmountModel,
                          BankAccount(
                            country: widget.bank.country,
                            accountHolder: widget.bank.accountHolder,
                            bankName: widget.bank.bankName,
                            accountNickname: widget.bank.accountNickname,
                            branchName: widget.bank.branchName ?? "",
                            swiftBic: widget.bank.swiftBic,
                            iban: widget.bank.iban,
                          ),
                        ),
                      );
                    },
                    isDisabled:
                        state.transferType == null ||
                        state.isRequestWithdrawButtonLoading,
                  );
                },
              ),
            ),
            const SizedBox(height: 10),
            Center(
              child: DuploButton.link(
                title: localizations.payments_bank_details,
                onTap: () {
                  DuploSheet.showModalSheet<Widget>(
                    context: context,
                    title: widget.bank.accountNickname,
                    subtitle: widget.bank.bankName,
                    hideCloseButton: true,
                    content: (contextContext) {
                      return BankDetailsBottomSheet(bank: widget.bank);
                    },
                  );
                },
                textColor: theme.foreground.fgDisabled,
              ),
            ),
            const SizedBox(height: 10),
          ],
        ),
      ),
    );
  }
}
