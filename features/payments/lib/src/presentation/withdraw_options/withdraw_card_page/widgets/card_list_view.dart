import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:payment/src/presentation/withdraw_options/withdraw_card_page/bloc/withdraw_card_bloc.dart';
import 'package:payment/src/presentation/withdraw_options/withdraw_card_page/widgets/loading_shimmer_item.dart';
import 'package:payment/src/presentation/withdraw_options/withdraw_card_page/widgets/selectable_base_container.dart';
import 'package:payment/src/presentation/withdraw_options/withdraw_card_page/widgets/withdraw_info_widget.dart';

class CardListView extends StatefulWidget {
  const CardListView({super.key, required this.isLoading});
  final bool isLoading;
  @override
  State<CardListView> createState() => _CardListViewState();
}

class _CardListViewState extends State<CardListView> {
  final ScrollController scrollController = ScrollController();

  @override
  void dispose() {
    scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final textStyle = context.duploTextStyles;
    final bloc = context.read<WithdrawCardBloc>();
    //todo add localization
    return Scaffold(
      backgroundColor: theme.background.bgPrimary,
      appBar: DuploAppBar(title: 'Withdraw to your bank card'),
      bottomNavigationBar: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: DuploSpacing.spacing_xl_16,
            vertical: DuploSpacing.spacing_md_8,
          ),
          child: BlocBuilder<WithdrawCardBloc, WithdrawCardState>(
            buildWhen: (previous, current) {
              return previous.isButtonLoading != current.isButtonLoading;
            },
            builder: (blocContext, state) {
              return DuploButton.defaultPrimary(
                title: 'Withdraw',
                isDisabled: widget.isLoading,
                isLoading: bloc.state.isButtonLoading,
                onTap:
                    () => blocContext.read<WithdrawCardBloc>().add(
                      WithdrawCardEvent.onConfirmButtonPressed(),
                    ),
              );
            },
          ),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(DuploSpacing.spacing_xl_16),
        child: SingleChildScrollView(
          controller: scrollController,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              DuploText(
                text: 'My cards',
                style: textStyle.textXl,
                fontWeight: DuploFontWeight.semiBold,
                color: theme.text.textPrimary,
              ),
              SizedBox(height: 24),
              widget.isLoading
                  ? LoadingShimmerItem()
                  : BlocBuilder<WithdrawCardBloc, WithdrawCardState>(
                    buildWhen: (previous, current) {
                      return previous.selectedCardIndex !=
                          current.selectedCardIndex;
                    },
                    builder: (blocCtx, state) {
                      return ListView.separated(
                        itemCount: bloc.state.cards?.length ?? 0,
                        shrinkWrap: true,
                        separatorBuilder:
                            (ctx, index) =>
                                SizedBox(height: DuploSpacing.spacing_lg_12),
                        physics: NeverScrollableScrollPhysics(),
                        itemBuilder: (ctx, index) {
                          return SelectableBaseContainer(
                            isSelected: bloc.state.selectedCardIndex == index,
                            onSelected: () {
                              bloc.add(
                                WithdrawCardEvent.onCardSelected(index: index),
                              );
                            },
                            child: WithdrawInfoWidget(
                              networkImageUrl:
                                  bloc.state.cards
                                      ?.elementAtOrNull(index)
                                      ?.imageUrl ??
                                  "",
                              title:
                                  bloc.state.cards
                                      ?.elementAtOrNull(index)
                                      ?.displayName ??
                                  '',
                              subtitle: 'Expiry 06/2025',
                            ),
                          );
                        },
                      );
                    },
                  ),
              SizedBox(height: DuploSpacing.spacing_lg_12),
              DuploAlertMessage.warning(
                title: 'Equiti follows a strict return to source policy',
                children: [
                  DuploText(
                    text:
                        'In all instances where applicable, all monies paid out by Equiti will be paid back to the source from where they originated from.',
                    style: textStyle.textSm,
                    color: theme.text.textTertiary,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
