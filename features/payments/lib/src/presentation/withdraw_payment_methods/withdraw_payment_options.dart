import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:payment/src/data/payment_method/withdraw_payment_methods_model/withdraw_payment_methods_model.dart';
import 'package:payment/src/di/di_container.dart';

import 'bloc/withdraw_payment_methods_bloc.dart';
import 'widgets/payment_method_tile.dart';

class WithdrawPaymentOptions extends StatelessWidget {
  const WithdrawPaymentOptions({super.key});

  @override
  Widget build(BuildContext context) {
    final localization = EquitiLocalization.of(context);
    final theme = DuploTheme.of(context);
    final textStyle = context.duploTextStyles;
    return BlocProvider(
      create:
          (providerContext) =>
              diContainer<WithdrawPaymentMethodsBloc>()
                ..add(const WithdrawPaymentMethodsEvent.getPaymentOptions()),
      child: BlocBuilder<
        WithdrawPaymentMethodsBloc,
        WithdrawPaymentMethodsState
      >(
        buildWhen:
            (previous, current) =>
                previous.paymentMethodsProccessState !=
                current.paymentMethodsProccessState,
        builder: (builderContext, state) {
          return Scaffold(
            appBar: DuploAppBar(title: localization.payments_withdraw),
            backgroundColor: theme.background.bgPrimary,
            body: switch (state.paymentMethodsProccessState) {
              PaymentMethodsProccessStateLoading() => Center(
                child: DuploLoadingIndicator(
                  color: theme.button.buttonPrimaryBg,
                ),
              ),
              PaymentMethodsProccessStateSuccess() => Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16.0,
                      vertical: 16.0,
                    ),
                    child: DuploAlertMessage.warning(
                      title: localization.payments_withdraw_policy_first_text,
                      children: [
                        DuploText(
                          text:
                              localization.payments_withdraw_policy_second_text,
                          style: textStyle.textSm,
                          color: theme.text.textTertiary,
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: ListView.builder(
                      shrinkWrap: true,
                      itemCount:
                          state
                              .paymentMethodsData
                              ?.data
                              .paymentsMethods
                              .length ??
                          0,
                      itemBuilder: (itemBuilderContext, mainIndex) {
                        final paymentMethods =
                            state.paymentMethodsData?.data.paymentsMethods
                                .elementAtOrNull(mainIndex)
                                ?.methods;
                        return Padding(
                          padding: const EdgeInsets.symmetric(
                            vertical: 10,
                            horizontal: 15,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  SizedBox(width: 15),
                                  DuploText(
                                    text:
                                        state
                                            .paymentMethodsData!
                                            .data
                                            .paymentsMethods
                                            .elementAtOrNull(mainIndex)
                                            ?.label ??
                                        "",
                                    color: theme.text.textPrimary,
                                    style: DuploTextStyles.of(context).textMd,
                                    fontWeight: DuploFontWeight.semiBold,
                                  ),
                                ],
                              ),
                              for (WithdrawalPaymentMethod paymentMethod
                                  in paymentMethods ?? [])
                                Padding(
                                  padding: const EdgeInsets.symmetric(
                                    vertical: 8,
                                  ),
                                  child: DuploTap(
                                    radius: DuploRadius.radius_md_8,
                                    onTap: () {
                                      builderContext
                                          .read<WithdrawPaymentMethodsBloc>()
                                          .add(
                                            WithdrawPaymentMethodsEvent.navigateToSelectAccountAndAmount(
                                              paymentMethod: paymentMethod,
                                            ),
                                          );
                                    },
                                    child: PaymentMethodTile(
                                      currencies:
                                          paymentMethod.currencies ?? [],
                                      imageUrl: paymentMethod.imageUrl ?? "",
                                      tags: paymentMethod.tag ?? [],
                                      name: paymentMethod.name,
                                      fee: paymentMethod.fee,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
              PaymentMethodsProccessStateError() => const Center(
                child: Text('Error'),
              ),
            },
          );
        },
      ),
    );
  }
}
