import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:payment/src/data/withdrawal_allowed_response_model/withdrawal_allowed_response_model.dart';
import 'package:payment/src/domain/usecase/check_withdrawal_allowed_usecase.dart';
part 'withdraw_skrill_and_neteller_event.dart';
part 'withdraw_skrill_and_neteller_state.dart';
part 'withdraw_skrill_and_neteller_bloc.freezed.dart';

class WithdrawSkrillAndNetellerBloc
    extends
        Bloc<WithdrawSkrillAndNetellerEvent, WithdrawSkrillAndNetellerState> {
  WithdrawSkrillAndNetellerBloc(this._checkWithdrawalAllowedUsecase)
    : super(WithdrawSkrillAndNetellerState()) {
    on<CheckPaymentMethod>(_checkPaymentMethod);
  }

  final CheckWithdrawalAllowedUsecase _checkWithdrawalAllowedUsecase;

  Future<void> _checkPaymentMethod(
    CheckPaymentMethod event,
    Emitter<WithdrawSkrillAndNetellerState> emit,
  ) async {
    emit(
      state.copyWith(
        withdrawSkrillAndNetellerProgressState:
            const WithdrawSkrillAndNetellerProgressState.loading(),
      ),
    );

    final checkWithdrawalAllowedUsecase =
        await _checkWithdrawalAllowedUsecase(
          paymentMethod: event.paymentMethod,
        ).run();

    if (isClosed) return;

    checkWithdrawalAllowedUsecase.fold(
      (err) {
        emit(
          state.copyWith(
            withdrawSkrillAndNetellerProgressState:
                WithdrawSkrillAndNetellerProgressState.error(
                  errorMessage: err.toString(),
                ),
          ),
        );
      },
      (res) {
        if (res.data.accounts.isEmpty) {
          emit(
            state.copyWith(
              withdrawalAllowedResponseModel: res,
              withdrawSkrillAndNetellerProgressState:
                  WithdrawSkrillAndNetellerProgressState.empty(),
            ),
          );
          return;
        }
        emit(
          state.copyWith(
            withdrawalAllowedResponseModel: res,
            withdrawSkrillAndNetellerProgressState:
                WithdrawSkrillAndNetellerProgressState.success(
                  isWithdrawalAllowed: res.success,
                ),
          ),
        );
      },
    );
  }
}
