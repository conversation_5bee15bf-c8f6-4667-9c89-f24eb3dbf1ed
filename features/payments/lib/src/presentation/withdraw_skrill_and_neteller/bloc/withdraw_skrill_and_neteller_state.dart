part of 'withdraw_skrill_and_neteller_bloc.dart';

@freezed
sealed class WithdrawSkrillAndNetellerState
    with _$WithdrawSkrillAndNetellerState {
  const factory WithdrawSkrillAndNetellerState({
    WithdrawalAllowedResponseModel? withdrawalAllowedResponseModel,
    @Default(WithdrawSkrillAndNetellerProgressState.loading())
    WithdrawSkrillAndNetellerProgressState
    withdrawSkrillAndNetellerProgressState,
  }) = _WithdrawSkrillAndNetellerState;
}

@freezed
sealed class WithdrawSkrillAndNetellerProgressState
    with _$WithdrawSkrillAndNetellerProgressState {
  const factory WithdrawSkrillAndNetellerProgressState.loading() =
      WithdrawSkrillAndNetellerProgressStateLoading;

  const factory WithdrawSkrillAndNetellerProgressState.success({
    required bool isWithdrawalAllowed,
  }) = WithdrawSkrillAndNetellerProgressStateSuccess;

  const factory WithdrawSkrillAndNetellerProgressState.empty() =
      WithdrawSkrillAndNetellerProgressStateEmpty;

  const factory WithdrawSkrillAndNetellerProgressState.error({
    String? errorMessage,
  }) = WithdrawSkrillAndNetellerProgressStateError;
}
