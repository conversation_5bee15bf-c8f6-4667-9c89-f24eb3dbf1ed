import 'scenarios/amount_conversion_success.dart';
import 'scenarios/amount_conversion_failure.dart';
import 'scenarios/amount_conversion_loading.dart';
import 'package:equiti_test/equiti_test.dart';
import 'amount_conversion_widget_test_data.dart';


Feature: Amount Conversion Widget Component
  @testMethodName: testGoldens
  Scenario: Show Loading State
    Given The {AmountConversionWidgetTestData.loading()} app is rendered {scenarios:[getConversionLoading]}
    Then screenshot verified {'amount_conversion_widget/amount_conversion_loading'} with custom pump

  @testMethodName: testGoldens
  Scenario: Show Error State
    Given The {AmountConversionWidgetTestData.basic()} app is rendered {scenarios:[]}
    And I wait for {2} seconds
    Then screenshot verified {'amount_conversion_widget/amount_conversion_error'}

  @testMethodName: testGoldens
  Scenario: Show Without Conversion Same Currency
    Given The {AmountConversionWidgetTestData.sameCurrency()} app is rendered {scenarios:[]}
    Then screenshot verified {'amount_conversion_widget/amount_conversion_without_conversion'}

  @testMethodName: testGoldens
  Scenario: Show With Conversion Different Currency
    Given The {AmountConversionWidgetTestData.withConversion()} app is rendered {scenarios:[getConversionSuccess]}
    And I wait for {2} seconds
    Then screenshot verified {'amount_conversion_widget/amount_conversion_with_conversion'}

  @testMethodName: testGoldens
  Scenario: Show Validation Error Below Minimum
    Given The {AmountConversionWidgetTestData.basic()} app is rendered {scenarios:[]}
    And I wait
    Then I fill {'0.5'} into {'transfer_amount_field'} field
    And I wait for {1} seconds
    Then screenshot verified {'amount_conversion_widget/amount_conversion_below_minimum'}

  @testMethodName: testGoldens
  Scenario: Show Validation Error Above Maximum
    Given The {AmountConversionWidgetTestData.basic()} app is rendered {scenarios:[]}
    And I wait
    Then I fill {'5000'} into {'transfer_amount_field'} field
    And I wait for {1} seconds
    Then screenshot verified {'amount_conversion_widget/amount_conversion_above_maximum'}

  @testMethodName: testGoldens
  Scenario: Remove validation error when i clear the amount field
    Given The {AmountConversionWidgetTestData.basic()} app is rendered {scenarios:[]}
    And I wait
    Then I fill {'5000'} into {'transfer_amount_field'} field
    And I wait for {1} seconds
    Then I fill {''} into {'transfer_amount_field'} field
    And I wait for {1} seconds
    Then screenshot verified {'amount_conversion_widget/amount_conversion_remove_amount'}


  @testMethodName: testGoldens
  Scenario: Show Suggested Amounts Three Items
    Given The {AmountConversionWidgetTestData.withThreeSuggestedAmounts()} app is rendered {scenarios:[]}
    Then screenshot verified {'amount_conversion_widget/amount_conversion_three_suggested_amounts'}

  @testMethodName: testGoldens
  Scenario: Show Suggested Amounts Four Items
    Given The {AmountConversionWidgetTestData.withFourSuggestedAmounts()} app is rendered {scenarios:[]}
    Then screenshot verified {'amount_conversion_widget/amount_conversion_four_suggested_amounts'}

  @testMethodName: testGoldens
  Scenario: Show Suggested Amounts Many Items
    Given The {AmountConversionWidgetTestData.withManySuggestedAmounts()} app is rendered {scenarios:[]}
    Then screenshot verified {'amount_conversion_widget/amount_conversion_many_suggested_amounts'}

  @testMethodName: testGoldens
  Scenario: Show Currency Dropdown
    Given The {AmountConversionWidgetTestData.withConversion()} app is rendered {scenarios:[getConversionSuccess]}
    And I wait for {2} seconds
    Then i tap {'converted_currency_selector'} key
    And I wait
    Then screenshot verified {'amount_conversion_widget/amount_conversion_currency_dropdown'}

  @testMethodName: testGoldens
  Scenario: Show Without Suggested Amounts
    Given The {AmountConversionWidgetTestData.withoutSuggestedAmounts()} app is rendered {scenarios:[]}
    Then screenshot verified {'amount_conversion_widget/amount_conversion_without_suggested_amounts'}

  @testMethodName: testGoldens
  Scenario: Show With Conversion Rate Display
    Given The {AmountConversionWidgetTestData.withConversion()} app is rendered {scenarios:[getConversionSuccess]}
    And I wait for {2} seconds
    Then I fill {'100'} into {'transfer_amount_field'} field
    And I wait for {1} seconds
    Then screenshot verified {'amount_conversion_widget/amount_conversion_with_rate_display'}
