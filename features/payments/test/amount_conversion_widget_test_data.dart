import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';
import 'package:payment/payments.dart';
import 'package:payment/src/domain/data/conversion_type.dart';
import 'package:payment/src/domain/data/payment_type.dart';

class AmountConversionWidgetTestData extends StatelessWidget {
  const AmountConversionWidgetTestData({super.key, required this.args});

  final AmountConversionArgs args;

  // Basic configuration for testing
  factory AmountConversionWidgetTestData.basic() {
    return AmountConversionWidgetTestData(
      args: (
        transferCurrency: 'USD',
        transferCurrencyImage: '',
        currencyAmountDetails: [
          const CurrencyAmountDetail(
            currency: 'USD',
            suggestedAmounts: [50, 100, 150],
            minAmount: 1,
            maxAmount: 2000,
          ),
        ],
        currencies: ['USD', 'AED', 'EUR'],
        showSuggestedAmounts: true,
        isStartWithConversionRate: false,
        targetCurrency: null,
        externalErrorMessage: null,
        paymentType: PaymentType.transfer,
        conversionType: ConversionType.accountToTargetCurrency,
      ),
    );
  }

  // Loading state configuration
  factory AmountConversionWidgetTestData.loading() {
    return AmountConversionWidgetTestData(
      args: (
        transferCurrency: 'USD',
        transferCurrencyImage: '',
        currencyAmountDetails: [
          const CurrencyAmountDetail(
            currency: 'USD',
            suggestedAmounts: [50, 100, 150],
            minAmount: 1,
            maxAmount: 2000,
          ),
        ],
        currencies: ['USD', 'AED', 'EUR'],
        showSuggestedAmounts: true,
        isStartWithConversionRate: true,
        targetCurrency: 'AED', // This will trigger conversion rate loading
        externalErrorMessage: null,
        conversionType: ConversionType.accountToTargetCurrency,
        paymentType: PaymentType.transfer,
      ),
    );
  }

  // Same currency (no conversion needed)
  factory AmountConversionWidgetTestData.sameCurrency() {
    return AmountConversionWidgetTestData(
      args: (
        transferCurrency: 'USD',
        transferCurrencyImage: '',
        currencyAmountDetails: [
          const CurrencyAmountDetail(
            currency: 'USD',
            suggestedAmounts: [50, 100, 150],
            minAmount: 1,
            maxAmount: 2000,
          ),
        ],
        currencies: ['USD', 'AED', 'EUR'],
        showSuggestedAmounts: true,
        isStartWithConversionRate: false,
        targetCurrency: null,
        externalErrorMessage: null,
        paymentType: PaymentType.transfer,
        conversionType: ConversionType.accountToTargetCurrency,
      ),
    );
  }

  // With conversion (different currencies)
  factory AmountConversionWidgetTestData.withConversion() {
    return AmountConversionWidgetTestData(
      args: (
        transferCurrency: 'USD',
        transferCurrencyImage: '',
        currencyAmountDetails: [
          const CurrencyAmountDetail(
            currency: 'USD',
            suggestedAmounts: [50, 100, 150],
            minAmount: 1,
            maxAmount: 2000,
          ),
          const CurrencyAmountDetail(
            currency: 'AED',
            suggestedAmounts: [200, 400, 600],
            minAmount: 5,
            maxAmount: 8000,
          ),
        ],
        currencies: ['USD', 'AED', 'EUR'],
        showSuggestedAmounts: true,
        isStartWithConversionRate: true,
        targetCurrency: 'AED',
        externalErrorMessage: null,
        conversionType: ConversionType.accountToTargetCurrency,
        paymentType: PaymentType.transfer,
      ),
    );
  }

  // Three suggested amounts (should use Row layout)
  factory AmountConversionWidgetTestData.withThreeSuggestedAmounts() {
    return AmountConversionWidgetTestData(
      args: (
        transferCurrency: 'USD',
        transferCurrencyImage: '',
        currencyAmountDetails: [
          const CurrencyAmountDetail(
            currency: 'USD',
            suggestedAmounts: [50, 100, 150],
            minAmount: 1,
            maxAmount: 2000,
          ),
        ],
        currencies: ['USD', 'AED', 'EUR'],
        showSuggestedAmounts: true,
        isStartWithConversionRate: false,
        targetCurrency: null,
        externalErrorMessage: null,
        paymentType: PaymentType.transfer,
        conversionType: ConversionType.accountToTargetCurrency,
      ),
    );
  }

  // Four suggested amounts (should use Row layout)
  factory AmountConversionWidgetTestData.withFourSuggestedAmounts() {
    return AmountConversionWidgetTestData(
      args: (
        transferCurrency: 'USD',
        transferCurrencyImage: '',
        currencyAmountDetails: [
          const CurrencyAmountDetail(
            currency: 'USD',
            suggestedAmounts: [25, 50, 100, 200],
            minAmount: 1,
            maxAmount: 2000,
          ),
        ],
        currencies: ['USD', 'AED', 'EUR'],
        showSuggestedAmounts: true,
        isStartWithConversionRate: false,
        targetCurrency: null,
        externalErrorMessage: null,
        paymentType: PaymentType.transfer,
        conversionType: ConversionType.accountToTargetCurrency,
      ),
    );
  }

  // Many suggested amounts (should use Wrap layout)
  factory AmountConversionWidgetTestData.withManySuggestedAmounts() {
    return AmountConversionWidgetTestData(
      args: (
        transferCurrency: 'USD',
        transferCurrencyImage: '',
        currencyAmountDetails: [
          const CurrencyAmountDetail(
            currency: 'USD',
            suggestedAmounts: [10, 25, 50, 100, 200, 500],
            minAmount: 1,
            maxAmount: 2000,
          ),
        ],
        currencies: ['USD', 'AED', 'EUR'],
        showSuggestedAmounts: true,
        isStartWithConversionRate: false,
        targetCurrency: null,
        externalErrorMessage: null,
        paymentType: PaymentType.transfer,
        conversionType: ConversionType.accountToTargetCurrency,
      ),
    );
  }

  // Without suggested amounts
  factory AmountConversionWidgetTestData.withoutSuggestedAmounts() {
    return AmountConversionWidgetTestData(
      args: (
        transferCurrency: 'USD',
        transferCurrencyImage: '',
        currencyAmountDetails: [
          const CurrencyAmountDetail(
            currency: 'USD',
            suggestedAmounts: null,
            minAmount: 1,
            maxAmount: 2000,
          ),
        ],
        currencies: ['USD', 'AED', 'EUR'],
        showSuggestedAmounts: false,
        isStartWithConversionRate: false,
        targetCurrency: null,
        externalErrorMessage: null,
        paymentType: PaymentType.transfer,
        conversionType: ConversionType.accountToTargetCurrency,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    return Scaffold(
      backgroundColor: theme.background.bgPrimary,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: AmountConversionWidget(
            args: args,
            onAmountChange: ({
              required String amount,
              required bool isAmountValid,
              required String convertedAmount,
              required ConversionRateModel? conversionRateData,
              required RatesModel? ratesModel,
              String? conversionRateString,
              String? targetCurrency,
            }) {
              // Test callback - no action needed
            },
          ),
        ),
      ),
    );
  }
}
