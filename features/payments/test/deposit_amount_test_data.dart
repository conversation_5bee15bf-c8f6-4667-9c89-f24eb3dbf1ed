import 'package:flutter/material.dart';
import 'package:payment/payments.dart';
import 'package:payment/src/domain/data/conversion_type.dart';
import 'package:payment/src/domain/data/payment_type.dart';

class DepositAmountTestData extends StatefulWidget {
  const DepositAmountTestData({super.key});

  @override
  State<DepositAmountTestData> createState() => _DepositAmountTestDataState();
}

class _DepositAmountTestDataState extends State<DepositAmountTestData> {
  final TextEditingController transferAmountController =
      TextEditingController();
  final TextEditingController convertedAmountController =
      TextEditingController();

  @override
  void dispose() {
    transferAmountController.dispose();
    convertedAmountController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: AmountConversionWidget(
          args: (
            transferCurrency: 'USD',
            transferCurrencyImage: '',
            currencyAmountDetails: [
              CurrencyAmountDetail(
                currency: 'AED',
                suggestedAmounts: [500, 1000, 1600],
                minAmount: 1,
                maxAmount: 1000,
              ),
              CurrencyAmountDetail(
                currency: 'USD',
                suggestedAmounts: [50, 100, 150],
                minAmount: 1,
                maxAmount: 2000,
              ),
            ],
            currencies: ['USD', 'AED'],
            showSuggestedAmounts: true,
            isStartWithConversionRate: false,
            targetCurrency: null,
            externalErrorMessage: null,
            paymentType: PaymentType.deposit,
            conversionType: ConversionType.accountToTargetCurrency,
          ),
          onAmountChange:
              ({
                required String amount,
                required bool isAmountValid,
                required String convertedAmount,
                required ConversionRateModel? conversionRateData,
                required RatesModel? ratesModel,
                String? conversionRateString,
                String? targetCurrency,
              }) {},
        ),
      ),
    );
  }
}
