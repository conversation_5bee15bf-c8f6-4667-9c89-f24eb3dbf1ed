import 'package:api_client/api_client.dart';
import 'package:payment/src/di/di_container.dart';

void checkWithdrawalAllowedEmptyState() {
  diContainer<MockApiInterceptor>()
    ..reset()
    ..reply({
      '/api/v1/client-profiles/********?brokerId=********': [
        MockResponse(
          bodyFilePath: 'resources/mocks/client_profile/success.json',
        ),
      ],
      '/api/v1/withdrawal/account': [
        MockResponse(
          bodyFilePath: 'resources/mocks/check_withdrawal_allowed/empty.json',
        ),
      ],
    });
}
