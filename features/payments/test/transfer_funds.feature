import 'package:equiti_test/equiti_test.dart';
import 'package:payment/src/presentation/transfer_funds/transfer_funds_screen.dart';
import 'scenarios/transfer_funds_success.dart';
import 'transfer_funds_test_data.dart';
import 'scenarios/transfer_funds_failure.dart';


Feature: TransferFunds
  @testMethodName: testGoldens
  Scenario: TransferFunds first Screen
    Given The {TransferFundsScreen()} app is rendered {scenarios:[transferFundsSuccess]}
    Then i wait
    Then screenshot verified {'transfer_funds_first_screen'}

  @testMethodName: testGoldens
  Scenario: TransferFunds seconed Screen
    Given The {TransferFundsTestData()} app is rendered {scenarios:[transferFundsSuccess]}
    Then i wait
    Then screenshot verified {'transfer_funds_seconed_screen'}

Feature: TransferFunds
  @testMethodName: testGoldens
  Scenario: TransferFunds success Screen
    Given The {TransferFundsTestData()} app is rendered {scenarios:[transferFundsSuccess]}
    Then I fill {'50'} into {'transfer_amount_field'} field
    Then i wait
    Then I tap {"Continue"} Duplo button
    Then i wait
    Then screenshot verified {'transfer_funds_success_screen'}

Feature: TransferFunds
  @testMethodName: testGoldens
  Scenario: TransferFunds failure Screen
    Given The {TransferFundsTestData()} app is rendered {scenarios:[transferFundsfailure]}
    Then I fill {'50'} into {'transfer_amount_field'} field
    Then i wait
    Then I tap {"Continue"} Duplo button
    Then i wait
    Then screenshot verified {'transfer_funds_failure_screen'}
