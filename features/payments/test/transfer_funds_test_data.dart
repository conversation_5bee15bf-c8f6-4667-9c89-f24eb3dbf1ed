import 'package:flutter/material.dart';
import 'package:payment/payments.dart';
import 'package:payment/src/data/account_model.dart';

class TransferFundsTestData extends StatefulWidget {
  const TransferFundsTestData({super.key});

  @override
  State<TransferFundsTestData> createState() => _DepositAmountTestDataState();
}

class _DepositAmountTestDataState extends State<TransferFundsTestData> {
  final TextEditingController transferAmountController =
      TextEditingController();
  final TextEditingController convertedAmountController =
      TextEditingController();

  @override
  void dispose() {
    transferAmountController.dispose();
    convertedAmountController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final Account account = Account(
      accountId: "43d00ffe-0439-7da5-68d2-673c835f0b7b",
      accountStatus: "Active",
      nickName: "sambhav",
      accountType: "LandingWallet",
      accountCurrency: "USD",
      platformAccountNumber: "W-2004192-001",
      clientId: "6b350c83-81a6-e47d-e021-673c83d7ca52",
      accountGroup: "",
      brokerId: "42bfd663-d215-43f7-a5c1-8d286a3cb8c7",
      platformAccountType: "Standard",
      name: "Mohammad Abuatieh",
      primaryEmail: "<EMAIL>",
      leverage: 0,
      serverCode: "",
      platformType: "Equiti",
      leadSource: "",
      balance: 9999,
      margin: 0,
      equity: 0,
      profit: 0,
      grossProfit: 0,
      dateCreated: DateTime.parse("2024-11-19T12:25:22"),
      isDemo: false,
      classification: "DirectClient",
      accountIdLong: 0,
      credit: 0,
      accountCurrencyUsdPair: "",
    );
    return TransferFundsDestSelectionScreen(account: account);
  }
}
