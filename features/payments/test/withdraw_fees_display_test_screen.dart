import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';
import 'package:payment/src/domain/data/withdrawal_mop_types.dart';
import 'package:payment/src/domain/model/transfer_type.dart';
import 'package:payment/src/presentation/widgets/withdraw_fees_display/withdraw_fees_display.dart';

class WithdrawFeesDisplayTestScreen extends StatelessWidget {
  const WithdrawFeesDisplayTestScreen({
    this.amount = 100.0,
    this.paymentType = WithdrawalMop.cards,
    this.accountId = '123456',
    this.accountCurrency = 'USD',
    this.transactionCurrency = 'USD',
    this.transferType,
    super.key,
  });

  final double amount;
  final WithdrawalMop paymentType;
  final String accountId;
  final String accountCurrency;
  final String transactionCurrency;
  final TransferType? transferType;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.duploTheme.background.bgPrimary,
      appBar: AppBar(),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: WithdrawFeesDisplay(
          args: (
            amount: amount,
            accountId: accountId,
            accountCurrency: accountCurrency,
            transactionCurrency: transactionCurrency,
            paymentType: paymentType.name,
            convertedAmount: amount,
            transferType: transferType,
          ),
          content: (
            idle:
                'Enter an amount or transfer type to calculate withdrawal fees',
            loading: 'Calculating withdrawal fees...',
            zeroFees: 'No fees will be deducted for this withdrawal.',
            nonZeroFees:
                (fees, total, currency) =>
                    'Withdrawal will incur a \$${fees.toStringAsFixed(2)} $currency transfer fee. Total: \$${total.toStringAsFixed(2)}',
          ),
          onFeesChange: (state) {
            // Test callback - can be used for assertions
            debugPrint('Fees changed: $state');
          },
        ),
      ),
    );
  }
}
