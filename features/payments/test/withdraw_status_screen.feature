import 'package:equiti_test/equiti_test.dart';
import 'package:payment/src/presentation/widgets/withdraw_status_screen.dart';

Feature: Withdraw Status Screen
  @testMethodName: testGoldens
  Scenario: Display submitted status
    Given The {WithdrawStatusScreen.submitted()} app is rendered
    Then screenshot verified {'withdraw_status_screen/submitted_status'}

  @testMethodName: testGoldens
  Scenario: Display successful status
    Given The {WithdrawStatusScreen.successful()} app is rendered
    Then screenshot verified {'withdraw_status_screen/successful_status'}

  @testMethodName: testGoldens
  Scenario: Display rejected status
    Given The {WithdrawStatusScreen.rejected()} app is rendered
    Then screenshot verified {'withdraw_status_screen/rejected_status'}

  @testMethodName: testGoldens
  Scenario: Display error status
    Given The {WithdrawStatusScreen.error()} app is rendered
    Then screenshot verified {'withdraw_status_screen/error_status'}

  @testMethodName: testGoldens
  Scenario: Display successful status with popUntilRoute
    Given The {WithdrawStatusScreen.successful(popUntilRoute: 'withdrawOptions')} app is rendered
    Then screenshot verified {'withdraw_status_screen/successful_status_with_pop_until_route'}
