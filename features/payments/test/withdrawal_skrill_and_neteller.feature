import 'package:equiti_test/equiti_test.dart';
import 'package:payment/payments.dart';
import 'scenarios/check_withdrawal_allowed_failure.dart';
import 'scenarios/check_withdrawal_allowed_success.dart';
import 'scenarios/check_withdrawal_allowed_empty.dart';

Feature: Withdrawal By Skrill and Neteller
  @testMethodName: testGoldens
  Scenario: Withdrawal Skrill And Neteller View Success
    Given The {WithdrawSkrillAndNetellerScreen(method: WithdrawalPaymentMethod(name: "Skrill", currencyAmountDetails: [CurrencyAmountDetail(currency: 'AED', suggestedAmounts: [500, 1000, 1500], minAmount: 1, maxAmount: 1000), CurrencyAmountDetail(currency: 'USD', suggestedAmounts: [50, 100, 150], minAmount: 1, maxAmount: 2000)], currencies: ["USD", "AED"]))} app is rendered {scenarios: [checkWithdrawalAllowedSuccess]}
    Then screenshot verified {'withdrawal_skrill_and_neteller_success'}

 @testMethodName: testGoldens
  Scenario: Withdra<PERSON> Skrill And Neteller View Failure
    Given The {WithdrawSkrillAndNetellerScreen(method: WithdrawalPaymentMethod(name: "Skrill", currencyAmountDetails: [CurrencyAmountDetail(currency: 'AED', suggestedAmounts: [500, 1000, 1500], minAmount: 1, maxAmount: 1000), CurrencyAmountDetail(currency: 'USD', suggestedAmounts: [50, 100, 150], minAmount: 1, maxAmount: 2000)], currencies: ["USD", "AED"]))} app is rendered {scenarios: [checkWithdrawalAllowedFailure]}
    Then screenshot verified {'withdrawal_skrill_and_neteller_failure'}
  
  @testMethodName: testGoldens
  Scenario: Withdrawal Skrill And Neteller View Empty State
    Given The {WithdrawSkrillAndNetellerScreen(method: WithdrawalPaymentMethod(name: "Skrill", currencyAmountDetails: [CurrencyAmountDetail(currency: 'AED', suggestedAmounts: [500, 1000, 1500], minAmount: 1, maxAmount: 1000), CurrencyAmountDetail(currency: 'USD', suggestedAmounts: [50, 100, 150], minAmount: 1, maxAmount: 2000)], currencies: ["USD", "AED"]))} app is rendered {scenarios: [checkWithdrawalAllowedEmptyState]}
    Then screenshot verified {'withdrawal_skrill_and_neteller_empty_state'}
