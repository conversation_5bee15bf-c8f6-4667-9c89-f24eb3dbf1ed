import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';

/// A utility class that provides a customizable action strip above the keyboard
/// when it becomes visible.
///
/// This widget solves common keyboard UX issues by:
/// - Automatically showing/hiding a "Done" button above the keyboard
/// - Allowing easy keyboard dismissal
/// - Supporting custom widgets in place of the default Done button
/// - Handling keyboard visibility state management
///
/// Usage:
/// 1. Add to your app's dependency injection:
/// ```dart
/// @module
/// abstract class AppModule {
///   @singleton
///   CustomActionKeyboard get customActionKeyboard => CustomActionKeyboard();
/// }
/// ```
///
/// 2. Initialize the service in your widget's initState:
/// ```dart
///@override
///void initState() {
///  super.initState();
///  diContainer<CustomActionKeyboard>().initService(
///    diContainer<GlobalKey<NavigatorState>>(),
///  );
///}
///```

/// 3. Dispose the service in your widget's dispose:
///```
///@override
///void dispose() {
///  diContainer<CustomActionKeyboard>().dispose();
///  super.dispose();
///}
/// ```

///
/// 4.[Optional] Listen to keyboard visibility changes and adjust your layout to prevent content from being hidden:
/// The stream provides visibility updates that allow you to add padding when needed to ensure
/// content remains visible above the keyboard and done button overlay
/// ```dart
/// StreamBuilder<bool>(
///   stream: customActionKeyboard.keyboardVisibilityStream,
///   builder: (context, snapshot) {
///     final isKeyboardVisible = snapshot.data ?? false;
///     return Padding(
///       padding: EdgeInsets.only(
///         // Add padding when keyboard is visible to prevent content overlap
///         bottom: isKeyboardVisible ? 48.0 : 0.0,
///       ),
///       child: YourContent(),
///     );
///   },
/// )
/// ```
///
/// Benefits:
/// - Consistent keyboard behavior across the app
/// - Improved user experience with easy keyboard dismissal
/// - Flexible customization options
/// - Automatic handling of keyboard visibility
/// - Proper content padding management
/// - Scoped to specific widgets for better resource management
class CustomActionKeyboard {
  bool _isKeyboardVisible = false;
  OverlayEntry? _overlayEntry;
  final KeyboardVisibilityController _keyboardVisibilityController =
      KeyboardVisibilityController();
  StreamSubscription<bool>? _keyboardSubscription;
  late GlobalKey<NavigatorState> _navigatorKey;
  Widget? _customWidget;
  Color? _backgroundColor, _textColor;
  String? _doneButtonText;

  /// Returns whether the keyboard is currently visible
  bool get isKeyboardVisible => _isKeyboardVisible;

  /// Stream of keyboard visibility changes
  /// Use this to adjust your UI layout when keyboard appears/disappears
  Stream<bool> get keyboardVisibilityStream =>
      _keyboardVisibilityController.onChange;

  // create a method which will get used as setter so amy anyclass if i need to use this i can call the to make it visible or invisible
  void setKeyboardVisibility(bool visible) {
    if (visible) {
      initService(_navigatorKey);
    } else {
      dispose();
    }
  }

  /// Initializes the keyboard action service
  ///
  /// [navigatorKey] - Required GlobalKey for navigator state to properly position overlay
  /// [customWidget] - Optional custom widget to show instead of default Done button
  /// [backgroundColor] - Optional background color for the action strip
  ///
  /// This should be called in your widget's initState method
  Future<void> initService(
    GlobalKey<NavigatorState> navigatorKey, {
    Widget? customWidget,
    Color? backgroundColor,
    Color? textColor,
    String? doneButtonText,
  }) async {
    if (_keyboardSubscription != null ||
        Platform.environment.containsKey('FLUTTER_TEST'))
      return;
    _navigatorKey = navigatorKey;
    _customWidget = customWidget;
    _backgroundColor = backgroundColor;
    _textColor = textColor;
    _doneButtonText = doneButtonText;
    _keyboardSubscription = _keyboardVisibilityController.onChange.listen((
      bool visible,
    ) {
      _isKeyboardVisible = visible;
      if (isKeyboardVisible) {
        _insertOverlay();
      } else {
        _removeOverlay();
      }
    });
  }

  /// Disposes of the keyboard action service
  /// Should be called in your widget's dispose method
  void dispose() {
    _keyboardSubscription?.cancel();
    _keyboardSubscription = null;
    _overlayEntry?.markNeedsBuild();
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  void _insertOverlay() {
    if (_overlayEntry != null) return;
    _overlayEntry = OverlayEntry(
      builder: (context) {
        final queryData = MediaQuery.of(context);
        return Positioned(
          left: 0,
          right: 0,
          bottom: queryData.viewInsets.bottom,
          child: Material(
            color: _backgroundColor ?? Colors.white,
            child:
                _customWidget ??
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: () {
                        FocusManager.instance.primaryFocus?.unfocus();
                        SystemChannels.textInput.invokeMethod('TextInput.hide');
                      },
                      child: Text(
                        _doneButtonText ?? 'Done',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: _textColor ?? Color(0xFF000700),
                        ),
                      ),
                    ),
                  ],
                ),
          ),
        );
      },
    );
    WidgetsBinding.instance.addPostFrameCallback((_) {
      assert(
        _navigatorKey.currentState != null ||
            _navigatorKey.currentState?.overlay != null,
        'Navigator key is null, did you forget to initialize the navigator key?',
      );
      _navigatorKey.currentState?.overlay?.insert(_overlayEntry!);
    });
  }

  void _removeOverlay() async {
    _overlayEntry?.markNeedsBuild();
    _overlayEntry?.remove();
    _overlayEntry = null;
  }
}
