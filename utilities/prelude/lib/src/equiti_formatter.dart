import 'package:intl/intl.dart';

/// A comprehensive formatting utility class for the Equiti Platform.
///
/// This class provides standardized formatting methods for numbers, currencies,
/// percentages, and dates across the entire Equiti Platform codebase.
/// Use this instead of <PERSON>lut<PERSON>'s built-in NumberFormat for consistency.
///
/// All numeric formatting methods support locale-specific formatting to ensure
/// proper display for international users.
class EquitiFormatter {
  /// Formats a number using the locale's default decimal pattern.
  ///
  /// This is the most basic number formatting method that applies locale-specific
  /// decimal separators and grouping. Use this for general number display where
  /// you want to respect the user's locale settings without specific decimal control.
  ///
  /// **Parameters:**
  /// - [value]: The numeric value to format (int, double, or num)
  /// - [locale]: The locale string (e.g., 'en_US', 'de_DE', 'fr_FR')
  ///
  /// **Returns:** A formatted string with locale-appropriate decimal and grouping separators
  ///
  /// **Example:**
  /// ```dart
  /// EquitiFormatter.formatNumber(value: 1234.567, locale: 'en_US')
  /// // Returns: "1,234.567"
  ///
  /// EquitiFormatter.formatNumber(value: 1234.567, locale: 'de_DE')
  /// // Returns: "1.234,567"
  /// ```
  ///
  /// **Use when:** You need basic number formatting with locale support
  /// **Avoid when:** You need specific decimal place control (use [decimalPatternDigits] instead)
  static String formatNumber({required num value, required String locale}) {
    return NumberFormat.decimalPattern(locale).format(value);
  }

  /// Formats a number with a specific number of decimal places using locale patterns.
  ///
  /// This method provides precise control over decimal places while maintaining
  /// locale-specific formatting for separators and grouping. The number of decimal
  /// places is fixed - trailing zeros will be shown if necessary.
  ///
  /// **Parameters:**
  /// - [value]: The numeric value to format
  /// - [digits]: The exact number of decimal places to display (0 or positive integer)
  /// - [locale]: The locale string for formatting rules
  ///
  /// **Returns:** A formatted string with exactly [digits] decimal places
  ///
  /// **Example:**
  /// ```dart
  /// EquitiFormatter.decimalPatternDigits(value: 123.4, digits: 2, locale: 'en_US')
  /// // Returns: "123.40"
  ///
  /// EquitiFormatter.decimalPatternDigits(value: 123.456789, digits: 3, locale: 'en_US')
  /// // Returns: "123.457" (rounded)
  /// ```
  ///
  /// **Use when:** You need exact decimal place control (e.g., currency amounts, percentages)
  /// **Difference from [formatNumber]:** This enforces a specific number of decimal places
  /// **Difference from [formatDynamicDigits]:** This shows trailing zeros, dynamic doesn't
  static String decimalPatternDigits({
    required num value,
    required int digits,
    required String locale,
  }) {
    final formatter = NumberFormat.decimalPatternDigits(
      locale: locale,
      decimalDigits: digits,
    );
    return formatter.format(value);
  }

  /// Formats a number as a percentage using locale-specific patterns.
  ///
  /// This method converts a decimal value to percentage format with the appropriate
  /// percentage symbol and locale-specific formatting. The input value should be
  /// in decimal form (e.g., 0.25 for 25%).
  ///
  /// **Parameters:**
  /// - [percentage]: The decimal value to format as percentage (0.25 = 25%)
  /// - [locale]: The locale string for formatting rules
  ///
  /// **Returns:** A formatted percentage string with locale-appropriate symbol
  ///
  /// **Example:**
  /// ```dart
  /// EquitiFormatter.formatPercentagePattern(percentage: 0.1234, locale: 'en_US')
  /// // Returns: "12%" (rounded to nearest whole percent)
  ///
  /// EquitiFormatter.formatPercentagePattern(percentage: 0.1234, locale: 'fr_FR')
  /// // Returns: "12 %" (French locale adds space before %)
  /// ```
  ///
  /// **Important:** Input should be decimal form (0.25), not percentage form (25)
  /// **Use when:** Displaying percentage values with proper locale formatting
  static String formatPercentagePattern({
    required num percentage,
    required String locale,
  }) {
    return NumberFormat.percentPattern(locale).format(percentage);
  }

  /// Formats a number with thousand separators and fixed decimal places.
  ///
  /// This method ensures thousand separators are always shown and enforces
  /// a specific number of decimal places. Unlike locale-based methods, this
  /// uses a fixed pattern that may not respect all locale conventions.
  ///
  /// **Parameters:**
  /// - [value]: The numeric value to format
  /// - [digits]: The exact number of decimal places to display
  /// - [locale]: Optional locale string (defaults to system locale if null)
  ///
  /// **Returns:** A formatted string with commas as thousand separators and fixed decimals
  ///
  /// **Example:**
  /// ```dart
  /// EquitiFormatter.formatDecimalWithSeparator(value: 1234567.89, digits: 2)
  /// // Returns: "1,234,567.89"
  ///
  /// EquitiFormatter.formatDecimalWithSeparator(value: 1000, digits: 4)
  /// // Returns: "1,000.0000"
  /// ```
  ///
  /// **Use when:** You need guaranteed thousand separators with fixed decimal places
  /// **Difference from [decimalPatternDigits]:** This forces comma separators regardless of locale
  /// **Note:** May not respect all locale-specific formatting conventions
  static String formatDecimalWithSeparator({
    required num value,
    required int digits,
    String? locale,
  }) {
    final formatter = NumberFormat("#,##0.${'0' * digits}", locale);
    return formatter.format(value);
  }

  /// Formats a number without any thousand separators.
  ///
  /// This method displays numbers as plain integers or decimals without
  /// any grouping separators, regardless of the number's size. Useful for
  /// IDs, codes, or when space is limited.
  ///
  /// **Parameters:**
  /// - [value]: The numeric value to format
  /// - [locale]: Optional locale string (affects decimal separator only)
  ///
  /// **Returns:** A formatted string without thousand separators
  ///
  /// **Example:**
  /// ```dart
  /// EquitiFormatter.formatWithoutSeparator(value: 1234567.89)
  /// // Returns: "1234567.89"
  ///
  /// EquitiFormatter.formatWithoutSeparator(value: 1000)
  /// // Returns: "1000"
  /// ```
  ///
  /// **Use when:** Displaying IDs, codes, or when thousand separators are unwanted
  /// **Difference from [formatNumber]:** This never shows thousand separators
  /// **Note:** Decimal places are shown as needed (not fixed)
  static String formatWithoutSeparator({required num value, String? locale}) {
    final formatter = NumberFormat("###0", locale);
    return formatter.format(value);
  }

  /// Formats a number with up to 6 decimal places, trimming trailing zeros.
  ///
  /// This method shows decimal places only when needed, up to a maximum of 6.
  /// Trailing zeros are automatically removed. Ideal for displaying precise
  /// values without unnecessary trailing zeros.
  ///
  /// **Parameters:**
  /// - [value]: The numeric value to format
  /// - [locale]: The locale string for decimal separator formatting
  ///
  /// **Returns:** A formatted string with up to 6 decimal places, trailing zeros removed
  ///
  /// **Example:**
  /// ```dart
  /// EquitiFormatter.formatAtMost6TrimmedDecimal(value: 123.450000, locale: 'en_US')
  /// // Returns: "123.45"
  ///
  /// EquitiFormatter.formatAtMost6TrimmedDecimal(value: 123.123456789, locale: 'en_US')
  /// // Returns: "123.123457" (rounded to 6 decimal places)
  ///
  /// EquitiFormatter.formatAtMost6TrimmedDecimal(value: 123, locale: 'en_US')
  /// // Returns: "123"
  /// ```
  ///
  /// **Use when:** Displaying precise values where trailing zeros should be hidden
  /// **Difference from [decimalPatternDigits]:** This trims trailing zeros and has a 6-digit limit
  /// **Difference from [formatDynamicDigits]:** This has a fixed maximum of 6 decimal places
  static String formatAtMost6TrimmedDecimal({
    required num value,
    required String locale,
  }) {
    final formatter = NumberFormat("0.######", locale);
    return formatter.format(value);
  }

  /// Formats a number with up to a specified number of decimal places, trimming trailing zeros.
  ///
  /// This method allows you to set the maximum number of decimal places while
  /// automatically trimming trailing zeros. Unlike [decimalPatternDigits], this
  /// won't show trailing zeros if they're not needed.
  ///
  /// **Parameters:**
  /// - [value]: The numeric value to format
  /// - [digits]: The maximum number of decimal places to display
  /// - [locale]: The locale string for formatting rules
  ///
  /// **Returns:** A formatted string with up to [digits] decimal places, trailing zeros removed
  ///
  /// **Example:**
  /// ```dart
  /// EquitiFormatter.formatDynamicDigits(value: 123.450, digits: 4, locale: 'en_US')
  /// // Returns: "123.45" (trailing zero trimmed)
  ///
  /// EquitiFormatter.formatDynamicDigits(value: 123.123456, digits: 3, locale: 'en_US')
  /// // Returns: "123.123"
  ///
  /// EquitiFormatter.formatDynamicDigits(value: 123, digits: 2, locale: 'en_US')
  /// // Returns: "123" (no unnecessary decimals)
  /// ```
  ///
  /// **Use when:** You want flexible decimal places with automatic trailing zero removal
  /// **Difference from [decimalPatternDigits]:** This trims trailing zeros
  /// **Difference from [formatAtMost6TrimmedDecimal]:** This allows custom maximum digits
  static String formatDynamicDigits({
    required num value,
    required int digits,
    required String locale,
  }) {
    final formatter = NumberFormat("0.${"#" * digits}", locale);
    return formatter.format(value);
  }

  /// Formats a date with ordinal suffix (1st, 2nd, 3rd, etc.) in "day month year" format.
  ///
  /// This method creates human-readable dates with proper English ordinal suffixes
  /// for the day. The format is "1st January 2024", "22nd March 2024", etc.
  ///
  /// **Parameters:**
  /// - [date]: The DateTime to format (null returns empty string)
  ///
  /// **Returns:** A formatted date string with ordinal suffix, or empty string if null
  ///
  /// **Example:**
  /// ```dart
  /// EquitiFormatter.formatDateWithSuffix(DateTime(2024, 1, 1))
  /// // Returns: "1st January 2024"
  ///
  /// EquitiFormatter.formatDateWithSuffix(DateTime(2024, 3, 22))
  /// // Returns: "22nd March 2024"
  ///
  /// EquitiFormatter.formatDateWithSuffix(null)
  /// // Returns: ""
  /// ```
  ///
  /// **Use when:** Displaying dates in a formal, human-readable format
  /// **Note:** Always uses English month names and ordinal suffixes
  static String formatDateWithSuffix(DateTime? date, {required String locale}) {
    if (date == null) return "";

    // 1) Get the locale-specific day string (so e.g. Arabic → Eastern-Arabic numerals)
    final dayString = DateFormat('d', locale).format(date);

    // 2) Only apply an English suffix when not Arabic (you could extend this for other locales)
    final suffix = locale == 'ar' ? '' : _getDaySuffix(date.day);

    // 3) Month + year in the right language
    final monthYear = DateFormat('MMMM yyyy', locale).format(date);

    return '$dayString$suffix $monthYear';
  }

  /// Formats an ISO date string with ordinal suffix.
  ///
  /// This is a convenience method that parses an ISO 8601 date string and
  /// formats it using [formatDateWithSuffix]. Useful when working with
  /// API responses that return dates as ISO strings.
  ///
  /// **Parameters:**
  /// - [isoDate]: An ISO 8601 date string (e.g., "2024-01-01T00:00:00.000Z")
  ///
  /// **Returns:** A formatted date string with ordinal suffix
  ///
  /// **Example:**
  /// ```dart
  /// EquitiFormatter.formatIsoDateWithSuffix("2024-01-01T00:00:00.000Z")
  /// // Returns: "1st January 2024"
  /// ```
  ///
  /// **Use when:** Converting ISO date strings to human-readable format
  /// **Throws:** FormatException if the ISO string is invalid
  static String formatIsoDateWithSuffix(
    String isoDate, {
    required String locale,
  }) {
    final date = DateTime.parse(isoDate);
    return formatDateWithSuffix(date, locale: locale);
  }

  /// Private helper method to get the appropriate ordinal suffix for a day.
  ///
  /// Handles special cases for 11th, 12th, 13th (which use "th" instead of
  /// "st", "nd", "rd") and applies standard English ordinal rules.
  ///
  /// **Parameters:**
  /// - [day]: The day of the month (1-31)
  ///
  /// **Returns:** The appropriate suffix ("st", "nd", "rd", or "th")
  static String _getDaySuffix(int day) {
    if (day >= 11 && day <= 13) {
      return 'th';
    }
    switch (day % 10) {
      case 1:
        return 'st';
      case 2:
        return 'nd';
      case 3:
        return 'rd';
      default:
        return 'th';
    }
  }

  /// Formats a date in DD/MM/YYYY format with zero-padding.
  ///
  /// This method creates a compact date format commonly used in forms and
  /// data entry. Days and months are zero-padded to ensure consistent width.
  ///
  /// **Parameters:**
  /// - [date]: The DateTime to format (null returns placeholder)
  /// - [placeholder]: String to return when date is null (defaults to empty string)
  ///
  /// **Returns:** A formatted date string in DD/MM/YYYY format
  ///
  /// **Example:**
  /// ```dart
  /// EquitiFormatter.formatDayMonthYear(DateTime(2024, 1, 5))
  /// // Returns: "05/01/2024"
  ///
  /// EquitiFormatter.formatDayMonthYear(null, placeholder: 'No date')
  /// // Returns: "No date"
  /// ```
  ///
  /// **Use when:** Displaying dates in compact, standardized format
  /// **Note:** Always uses DD/MM/YYYY format regardless of locale
  static String formatDayMonthYear(DateTime? date, {String placeholder = ''}) {
    if (date == null) return placeholder;
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  static String formatDayMonthYearTime(
    DateTime? date, {
    String placeholder = '',
  }) {
    if (date == null) return placeholder;
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year} ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }

  /// Formats a date in DD - MM - YYYY format with zero-padding and dashes.
  ///
  /// Similar to [formatDayMonthYear] but uses dashes instead of slashes as
  /// separators. Useful for different UI contexts or when slash separators
  /// might conflict with other formatting.
  ///
  /// **Parameters:**
  /// - [date]: The DateTime to format (null returns placeholder)
  /// - [placeholder]: String to return when date is null (defaults to empty string)
  ///
  /// **Returns:** A formatted date string in DD - MM - YYYY format
  ///
  /// **Example:**
  /// ```dart
  /// EquitiFormatter.formatFromToDayMonthYear(DateTime(2024, 1, 5))
  /// // Returns: "05 - 01 - 2024"
  ///
  /// EquitiFormatter.formatFromToDayMonthYear(null, placeholder: 'TBD')
  /// // Returns: "TBD"
  /// ```
  ///
  /// **Use when:** You need dash-separated date format
  /// **Difference from [formatDayMonthYear]:** Uses dashes with spaces instead of slashes
  static String formatFromToDayMonthYear(
    DateTime? date, {
    String placeholder = '',
    required String locale,

    /// When true output uses month number,
    /// when false uses full Arabic month name.
    bool monthAsNumber = true,
  }) {
    if (date == null) return placeholder;

    // pattern:
    //  - 'dd - MM - yyyy' → ٠٥ - ٠٢ - ٢٠٢٤
    //  - 'dd - MMMM - yyyy' → ٠٥ - فبراير - ٢٠٢٤
    final pattern = monthAsNumber ? 'dd - MM - yyyy' : 'dd - MMMM - yyyy';
    return DateFormat(pattern, locale).format(date);
  }

  /// Gets the appropriate currency symbol or code for display.
  ///
  /// This method returns either a currency symbol (like $, €, £) for supported
  /// currencies, or the currency code itself for unsupported ones. Provides
  /// flexible spacing options for different UI layouts.
  ///
  /// **Parameters:**
  /// - [currencyCode]: The 3-letter currency code (e.g., 'USD', 'EUR', 'GBP')
  /// - [uppercase]: Whether to return currency codes in uppercase (default: true)
  /// - [addSpaceAfterIfSymbol]: Add space after currency symbols (default: false)
  /// - [addSpaceAfterIfCurrency]: Add space after currency codes (default: false)
  ///
  /// **Returns:** Currency symbol or code, optionally with trailing space
  ///
  /// **Example:**
  /// ```dart
  /// EquitiFormatter.getCurrencySymbol('USD')
  /// // Returns: "\$"
  ///
  /// EquitiFormatter.getCurrencySymbol('USD', addSpaceAfterIfSymbol: true)
  /// // Returns: "\$ "
  ///
  /// EquitiFormatter.getCurrencySymbol('JPY')
  /// // Returns: "JPY" (no symbol available)
  ///
  /// EquitiFormatter.getCurrencySymbol('JPY', addSpaceAfterIfCurrency: true)
  /// // Returns: "JPY "
  ///
  /// EquitiFormatter.getCurrencySymbol(null)
  /// // Returns: ""
  /// ```
  ///
  /// **Supported symbols:** USD ($), EUR (€), GBP (£)
  /// **Use when:** Displaying currency prefixes or suffixes in financial data
  /// **Note:** Unsupported currencies return their 3-letter code
  static String getCurrencySymbol(
    String? currencyCode, {
    bool uppercase = true,
    bool addSpaceAfterIfSymbol = false,
    bool addSpaceAfterIfCurrency = false,
  }) {
    String result = '';
    if (currencyCode == null) return result;
    final Map<String, String> currencySymbols = {
      'USD': '\$',
      'EUR': '€',
      'GBP': '£',
    };
    final symbol = currencySymbols[currencyCode.toUpperCase()];
    if (symbol != null) {
      result = symbol;
      if (addSpaceAfterIfSymbol) {
        result = symbol + ' ';
      }
    } else {
      result =
          uppercase ? currencyCode.toUpperCase() : currencyCode.toLowerCase();
      if (addSpaceAfterIfCurrency) {
        result = currencyCode.toUpperCase() + ' ';
      }
    }

    return result;
  }

  /// Formats a DateTime to a readable date string without ordinal suffixes.
  ///
  /// This method creates a clean, readable date format using full month names.
  /// Unlike [formatDateWithSuffix], this doesn't include ordinal suffixes (st, nd, rd, th).
  ///
  /// **Parameters:**
  /// - [date]: The DateTime to format (null returns placeholder)
  /// - [placeholder]: String to return when date is null (defaults to empty string)
  ///
  /// **Returns:** A formatted date string in "d MMMM yyyy" format
  ///
  /// **Example:**
  /// ```dart
  /// EquitiFormatter.formatDateTimeToDateString(DateTime(2024, 1, 5))
  /// // Returns: "5 January 2024"
  ///
  /// EquitiFormatter.formatDateTimeToDateString(DateTime(2024, 12, 25))
  /// // Returns: "25 December 2024"
  ///
  /// EquitiFormatter.formatDateTimeToDateString(null, placeholder: 'Unknown')
  /// // Returns: "Unknown"
  /// ```
  ///
  /// **Use when:** Displaying dates in clean, readable format without ordinal suffixes
  /// **Difference from [formatDateWithSuffix]:** No ordinal suffixes (1st, 2nd, etc.)
  static String formatDateTimeToDateString(
    DateTime? date, {
    String placeholder = '',
  }) {
    if (date == null) return placeholder;
    final formatter = DateFormat('d MMMM yyyy');
    return formatter.format(date);
  }

  /// Formats a DateTime to a time string in 24-hour format.
  ///
  /// This method extracts and formats only the time portion of a DateTime
  /// using 24-hour format with zero-padded hours and minutes.
  ///
  /// **Parameters:**
  /// - [time]: The DateTime to extract time from (null returns placeholder)
  /// - [placeholder]: String to return when time is null (defaults to empty string)
  ///
  /// **Returns:** A formatted time string in "HH:mm" format
  ///
  /// **Example:**
  /// ```dart
  /// EquitiFormatter.formatDateTimeToTimeString(DateTime(2024, 1, 1, 9, 30))
  /// // Returns: "09:30"
  ///
  /// EquitiFormatter.formatDateTimeToTimeString(DateTime(2024, 1, 1, 15, 45))
  /// // Returns: "15:45"
  ///
  /// EquitiFormatter.formatDateTimeToTimeString(null, placeholder: '--:--')
  /// // Returns: "--:--"
  /// ```
  ///
  /// **Use when:** Displaying time portions of DateTime objects
  /// **Note:** Always uses 24-hour format, not 12-hour AM/PM format
  static String formatDateTimeToTimeString(
    DateTime? time, {
    String placeholder = '',
  }) {
    if (time == null) return placeholder;
    final formatter = DateFormat('HH:mm');
    return formatter.format(time);
  }

  /// Formats a number as compact currency with abbreviated large numbers.
  ///
  /// This method creates compact representations of large currency amounts
  /// using abbreviations like K (thousands), M (millions), B (billions).
  /// Ideal for displaying large financial values in limited space.
  ///
  /// **Parameters:**
  /// - [value]: The numeric value to format
  /// - [digits]: Number of decimal places to show
  /// - [symbol]: Currency symbol to use (defaults to empty string)
  ///
  /// **Returns:** A compact currency string with abbreviations for large numbers
  ///
  /// **Example:**
  /// ```dart
  /// EquitiFormatter.formatCompactCurrency(value: 1234567, digits: 1, symbol: '\$')
  /// // Returns: "\$1.2M"
  ///
  /// EquitiFormatter.formatCompactCurrency(value: 1500, digits: 0, symbol: '€')
  /// // Returns: "€2K" (rounded)
  ///
  /// EquitiFormatter.formatCompactCurrency(value: 999, digits: 0, symbol: '\$')
  /// // Returns: "\$999"
  ///
  /// EquitiFormatter.formatCompactCurrency(value: 1000000000, digits: 2, symbol: '£')
  /// // Returns: "£1.00B"
  /// ```
  ///
  /// **Use when:** Displaying large currency amounts in dashboards or summaries
  /// **Note:** Automatically chooses appropriate abbreviation (K, M, B, T)
  /// **Abbreviations:** K = thousands, M = millions, B = billions, T = trillions
  static String formatCompactCurrency({
    required num value,
    required int digits,
    String? symbol,
  }) {
    return NumberFormat.compactCurrency(
      decimalDigits: digits,
      symbol: symbol ?? '',
    ).format(value);
  }

  /// Formats a number with up to 5 decimal places, trimming trailing zeros.
  /// Example:
  /// EquitiFormatter.formatUpTo5TrimmedDecimal(1.00100, 'en_US') // "1.001"
  /// EquitiFormatter.formatUpTo5TrimmedDecimal(1.23000, 'en_US') // "1.23"
  static String formatUpToTrimmedDecimal({
    required num value,
    required int digits,
    required String locale,
  }) => formatDynamicDigits(value: value, digits: digits, locale: locale);

  /// Formats a number using the locale's default decimal pattern, with special handling for zero values.
  ///
  /// This method works exactly like formatNumber but ensures zero values are displayed
  /// with consistent decimal places for financial consistency. Non-zero values use the standard
  /// decimal pattern formatting unless specific digits are provided.
  ///
  /// **Parameters:**
  /// - [value]: The numeric value to format (int, double, or num)
  /// - [locale]: The locale string (e.g., 'en_US', 'de_DE', 'fr_FR')
  /// - [digits]: Optional number of decimal places to enforce. If provided, all values use this precision.
  ///                If null, zero values show 2 decimal places, non-zero values use default formatting.
  ///
  /// **Returns:** A formatted string with locale-appropriate formatting
  ///
  /// **Example:**
  /// ```dart
  /// EquitiFormatter.formatNumberWithZeroDefault(value: 1234.567, locale: 'en_US')
  /// // Returns: "1,234.567"
  ///
  /// EquitiFormatter.formatNumberWithZeroDefault(value: 0, locale: 'en_US')
  /// // Returns: "0.00"
  ///
  /// EquitiFormatter.formatNumberWithZeroDefault(value: 1234.567, locale: 'en_US', digits: 3)
  /// // Returns: "1,234.567"
  ///
  /// EquitiFormatter.formatNumberWithZeroDefault(value: 0, locale: 'en_US', digits: 5)
  /// // Returns: "0.00000"
  /// ```
  ///
  /// **Use when:** You need formatNumber behavior but want zero values to show with decimal places
  /// **Difference from [formatNumber]:** Zero values display with decimal places, optional precision control
  static String formatNumberWithZeroDefault({
    required num? value,
    required String locale,
    int? digits,
  }) {
    final safeValue = value ?? 0;

    // If digits are specified, use decimalPatternDigits for all values
    if (digits != null) {
      return NumberFormat.decimalPatternDigits(
        locale: locale,
        decimalDigits: digits,
      ).format(safeValue);
    }

    // Handle zero values to show with 2 decimal places for financial consistency
    if (safeValue == 0) {
      return NumberFormat.decimalPatternDigits(
        locale: locale,
        decimalDigits: 2,
      ).format(safeValue);
    }

    // Non-zero values use standard decimal pattern
    return NumberFormat.decimalPattern(locale).format(safeValue);
  }
}
